%%[
  SET @action_area = QueryParameter('action_area')
  SET @src_Secret = QueryParameter('e')
  SET @event_id = QueryParameter('event_id')
  SET @biz_env = QueryParameter('biz_env')
  SET @sourceTable = IIF(@AppStateEnvironment == 'UAT','ent.EmailObject_UAT','ent.EmailObject')
  SET @de_ecc = IIF(@AppStateEnvironment == 'UAT','ent.email_content_cache_UAT','ent.email_content_cache')
  SET @de_llm_results = IIF(@biz_env == 'UAT','ent.LLM_content_results_UAT','ent.LLM_content_results')
]%%
<div class="related_fields" style="padding:0!important;margin-bottom:0.8vw;border: 1px solid #dee2e6;border-radius: var(--border-radius, 0.42vw);overflow: hidden;margin-top:1vw;">
  <div class="list_view_table_row_container" style="max-height:8vw!important;margin-bottom:0!important;overflow-y:auto;" id="ai_results_list%%=v(@event_id)=%%">
    <table style="width:100%;border-collapse:separate;border-spacing:0;">
      <thead style="background-color: #f8f9fa;">
        <tr>
          <th style="width:25%;font-size:0.9vw;font-weight:600;padding:0.4vw 0.5vw;text-align:left;border-bottom:1px solid #dee2e6;height: 2vw;position:sticky;top:0;background-color: #f8f9fa;">Created date</th> 
          <th style="width:50%;font-size:0.9vw;font-weight:600;padding:0.4vw 0.5vw;text-align:left;border-bottom:1px solid #dee2e6;height: 2vw;position:sticky;top:0;background-color: #f8f9fa;">AI Generation</th>
          <th style="width:25%;font-size:0.9vw;font-weight:600;padding:0.4vw 0.5vw;text-align:left;border-bottom:1px solid #dee2e6;height: 2vw;position:sticky;top:0;background-color: #f8f9fa;">Username</th>
        </tr>
      </thead>
      <tbody>
        %%[
        /* Sort by created_date */
        SET @sort_field = 'created_date'

        /* Get all LLM results for this event_id */
        SET @llm_results_count = RowCount(LookupOrderedRows(@de_llm_results, 50, Concat(@sort_field, ' DESC'), 'field', '1','status','Success'))

        IF @llm_results_count > 0 THEN
          SET @llm_results = LookupOrderedRows(@de_llm_results, 50, Concat(@sort_field, ' DESC'), 'field', '1','status','Success')

          for @result = 1 to @llm_results_count do
            SET @current_event_id = Field(Row(@llm_results, @result), 'event_id')
            SET @current_content_name = Field(Row(@llm_results, @result), 'content_name')
            SET @current_email_name = Field(Row(@llm_results, @result), 'EmailName')
            SET @current_created_date = Field(Row(@llm_results, @result), 'created_date')
            SET @current_llm_result = Field(Row(@llm_results, @result), 'llm_result')
            SET @current_user_prompt = Field(Row(@llm_results, @result), 'user_prompt')
            SET @current_goal = Field(Row(@llm_results, @result), 'goal')
            SET @current_form_json = Field(Row(@llm_results, @result), 'form_json')
            SET @current_username = Field(Row(@llm_results, @result), 'username')
            ]%%
            <tr id="table_tr_row_ai%%=v(@event_id)=%%%%=v(@current_event_id)=%%" class="table_tr_row" style="cursor:pointer;transition:background-color 0.2s ease;">
              <td style="width:25%;font-size:0.85vw;padding:0.4vw 0.5vw;text-align:left;border-bottom:1px solid #f0f0f0;" class="spanRowData">
                %%=Format(@current_created_date,'dd/MM/yy hh:mm tt')=%%
              </td>
              <td style="width:50%;font-size:0.85vw;padding:0.4vw 0.5vw;text-align:left;border-bottom:1px solid #f0f0f0;" class="spanRowData">
                <span style="color:#0070d2;font-size:0.85vw;padding-left:0;" type="button" class="toggleSortByFields" id='{"created_date":"%%=Format(@current_created_date,'dd/MM/yy hh:mm tt')=%%","secret":"%%=v(@src_Secret)=%%","event_id":"%%=v(@current_event_id)=%%","html":"%%=Base64Encode(@current_llm_result)=%%","user_prompt":"%%=Base64Encode(@current_user_prompt)=%%","goal":"%%=Base64Encode(@current_goal)=%%","form_json":"%%=Base64Encode(@current_form_json)=%%","username":"%%=v(@current_username)=%%"}'  onclick="$('#aiResultsPreviewIframe%%=v(@event_id)=%%').attr('src','');$('#loading_ai_results_preview%%=v(@event_id)=%%').show();$('#no_ai_results_preview_available%%=v(@event_id)=%%').hide();extract_val_from_select_option_id_ai_result(event,'%%=v(@event_id)=%%');$('#ai_results_preview_box%%=v(@event_id)=%%').css('display','block');$('#ai_results_preview_text_placeholder%%=v(@event_id)=%%').css('display','none');change_background_tr_row_on_click('table_tr_row_ai%%=v(@event_id)=%%%%=v(@current_event_id)=%%','#daedf9');">
                  %%=IIF(NOT EMPTY(@current_user_prompt), Concat(Substring(@current_user_prompt, 1, 45), IIF(Length(@current_user_prompt) > 45, "...", "")), Concat('AI Generation from ', Format(@current_created_date, 'dd/MM/yy hh:mm tt')))=%%
                </span>
              </td>
              <td style="width:25%;font-size:0.85vw;padding:0.4vw 0.5vw;text-align:left;border-bottom:1px solid #f0f0f0;" class="spanRowData">
                %%=v(@current_username)=%%
              </td>
            </tr>
            %%[
          next @result
        ELSE ]%%
          <tr>
            <td colspan="3" style="font-size:0.85vw;padding:0.4vw 0.5vw;text-align:center;border-bottom:1px solid #f0f0f0;color:#6c757d;" class="spanRowData">
              No AI generations available
            </td>
          </tr>
        %%[ ENDIF ]%%
      </tbody>
    </table>
  </div>
</div>
<div class="subText2" style="font-size: 0.9vw; margin-bottom: 0.8vw; color: var(--secondary-color, #64748b);">
  %%[ IF @llm_results_count == 0 THEN ]%%
  No AI generations found. Generate content using the AI tools to see your results here.
  %%[ ELSE ]%%
  Click on an AI generation to preview the content and view the prompt used.
  %%[ ENDIF ]%%
</div>
<div id="ai_results_preview_box%%=v(@event_id)=%%" style="display:none;">
  <div class="view-toggle-buttons" style="display: flex; gap: 0.5vw; margin-bottom: 0.8vw;">
    <button type="button" id="aiResultsDesktopView%%=v(@event_id)=%%" class="view-toggle-btn active" style="border: 1px solid #dee2e6; background-color: #0d6efd; color: white; border-color: #0d6efd; border-radius: var(--border-radius, 0.42vw); padding: 0.375vw 0.75vw; font-size: var(--font-size-base, 0.97vw);" onclick="toggleAIResultsViewMode('Desktop', '%%=v(@event_id)=%%');">💻 Desktop</button>
    <button type="button" id="aiResultsMobileView%%=v(@event_id)=%%" class="view-toggle-btn" style="border: 1px solid #dee2e6; background-color: #fff; color: black; border-radius: var(--border-radius, 0.42vw); padding: 0.375vw 0.75vw; font-size: var(--font-size-base, 0.97vw);" onclick="toggleAIResultsViewMode('Mobile', '%%=v(@event_id)=%%');">📱 Mobile</button>
  </div>
  <select class="question2" id="ToggleAIResultsView%%=v(@event_id)=%%" style="display:none;">
    <option value="Desktop" selected>Desktop view</option>
    <option value="Mobile">Mobile view</option>
  </select>

  <!-- Content Preview Section -->
  <div style="position:relative!important;width:100%;border: 1px solid #dee2e6;border-radius: var(--border-radius, 0.42vw);background-color:#f8fafc;margin-top:0.5vw;height: 18.75vw;">
    <div id="ai_results_preview_text_placeholder%%=v(@event_id)=%%" style="padding-left:1vw;font-size:1vw;margin-top:7vw;text-align:center;">Select an AI generation from the list to view the content.</div>

    <!-- Iframe is now inside the preview container -->
    <iframe id="aiResultsPreviewIframe%%=v(@event_id)=%%" class="rightPreviewIframe other_block_preview ai-results-preview-desktop-view" style="display:none;position:absolute;top:0;left:0;width:100%;height:100%;border:none;overflow:hidden;"></iframe>

    <div id="loading_ai_results_preview%%=v(@event_id)=%%" style="display:none;position:absolute;top:0;left:0;width:100%;height:100%;background-color:rgba(255,255,255,0.8);z-index:1000;">
      <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
    </div>
    <div id="no_ai_results_preview_available%%=v(@event_id)=%%" style="display:none;position:absolute;top:0;left:0;width:100%;height:100%;background-color:rgba(255,255,255,0.8);z-index:1000;">
      <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);">
        <div class="alert alert-warning" role="alert">
          No preview available
        </div>
      </div>
    </div>
  </div>
</div>

