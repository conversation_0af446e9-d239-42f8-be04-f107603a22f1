# Dynamic Field Management System Documentation

## Overview

The Dynamic Field Management System provides automatic handling of TinyMCE dynamic field spans with `mceNonEditable` class. This system operates with **opposite logic** to the UTM Management System:

- **On Save**: Removes `<span class="mceNonEditable">` wrappers from dynamic fields
- **On Load/Content Changes**: Adds `<span class="mceNonEditable">` wrappers to dynamic fields

## System Architecture

### Core Components

| Component             | Purpose                           | Location               |
| --------------------- | --------------------------------- | ---------------------- |
| `DynamicFieldManager` | Main management object            | `cmp_ai_editor_js.amp` |
| Save Integration      | Removes spans during save process | `cmp_ai_pop-up_js.amp` |
| Mutation Observer     | Monitors content changes          | `cmp_ai_editor_js.amp` |
| TinyMCE Integration   | Handles editor interactions       | `cmp_ai_editor_js.amp` |

### Supported Dynamic Fields

| Field Name     | Purpose                       | Example Output       |
| -------------- | ----------------------------- | -------------------- |
| `FirstName`    | Customer first name           | `[[[FirstName]]]`    |
| `LastName`     | Customer last name            | `[[[LastName]]]`     |
| `EmailAddress` | Customer email                | `[[[EmailAddress]]]` |
| `PostCode`     | Customer postcode             | `[[[PostCode]]]`     |
| `Weekday`      | Current weekday               | `[[[Weekday]]]`      |
| `DOTM`         | Day of the month              | `[[[DOTM]]]`         |
| `DSFX`         | Day suffix (th, nd, rd)       | `[[[DSFX]]]`         |
| `MonthName`    | Month name                    | `[[[MonthName]]]`    |
| `TimeOfDay`    | Time of day                   | `[[[TimeOfDay]]]`    |
| `BrandName`    | Current brand name            | `[[[BrandName]]]`    |
| `MastheadName` | Current masthead name         | `[[[MastheadName]]]` |
| `SiteLink`     | Current masthead website link | `[[[SiteLink]]]`     |

## System Flow Diagrams

### Content Lifecycle Flow

The Dynamic Field Management System follows a clear lifecycle pattern that ensures dynamic fields are properly handled throughout the content editing process.

### Save vs Load Process Comparison

## Technical Implementation

### Core Functions

| Function                               | Purpose                      | Input           | Output          |
| -------------------------------------- | ---------------------------- | --------------- | --------------- |
| `removeDynamicFieldSpansFromContent()` | Remove spans for saving      | HTML with spans | Clean HTML      |
| `addDynamicFieldSpansToContent()`      | Add spans for display        | Raw HTML        | HTML with spans |
| `addVisualDynamicFieldSpans()`         | Update content wrapper       | None            | DOM updated     |
| `setupDynamicFieldMonitoring()`        | Initialise mutation observer | None            | Observer active |
| `debugCurrentState()`                  | Debug field states           | None            | Console output  |

### Regular Expression Patterns

| Pattern Type  | Regex                                                                                                                       | Purpose                 |
| ------------- | --------------------------------------------------------------------------------------------------------------------------- | ----------------------- |
| Remove Spans  | `<span[^>]*class="[^"]*mceNonEditable[^"]*"[^>]*>\[\[\[(${fieldPattern})\]\]\]</span>`                                      | Strip spans during save |
| Add Spans     | `\[\[\[(${fieldPattern})\]\]\](?!</span>)`                                                                                  | Find unprotected fields |
| Field Pattern | `Weekday\|DOTM\|DSFX\|MonthName\|TimeOfDay\|FirstName\|LastName\|EmailAddress\|PostCode\|BrandName\|MastheadName\|SiteLink` | Match supported fields  |

### Content Transformation Examples

| Stage                | Content Example                                                                                                                     |
| -------------------- | ----------------------------------------------------------------------------------------------------------------------------------- |
| **Raw from Server**  | `<p>Hello [[[FirstName]]], welcome to [[[BrandName]]]!</p>`                                                                         |
| **Visual in Editor** | `<p>Hello <span class="mceNonEditable">[[[FirstName]]]</span>, welcome to <span class="mceNonEditable">[[[BrandName]]]</span>!</p>` |
| **Saved to Server**  | `<p>Hello [[[FirstName]]], welcome to [[[BrandName]]]!</p>`                                                                         |

## Integration Points

### File Locations and Integration

| File                   | Integration Point            | Purpose                   |
| ---------------------- | ---------------------------- | ------------------------- |
| `cmp_ai_editor_js.amp` | `DynamicFieldManager` object | Core functionality        |
| `cmp_ai_editor_js.amp` | Document ready handler       | Initialisation            |
| `cmp_ai_pop-up_js.amp` | Save button handler          | Span removal during save  |
| `cmp_ai_editor_js.amp` | Mutation observer setup      | Content change monitoring |

### Mutation Observer Configuration

| Property        | Value              | Purpose                             |
| --------------- | ------------------ | ----------------------------------- |
| `childList`     | `true`             | Monitor DOM node additions/removals |
| `subtree`       | `true`             | Monitor all descendant changes      |
| `characterData` | `true`             | Monitor text content changes        |
| Target Element  | `.content-wrapper` | Same as UTM system                  |

## Debugging and Testing

### Available Debug Functions

| Function                  | Purpose                          | Usage           |
| ------------------------- | -------------------------------- | --------------- |
| `debugDynamicFields()`    | Show current field states        | Console command |
| `addTestDynamicFields()`  | Add test content                 | Console command |
| `forceAddSpansNow()`      | Force span addition              | Console command |
| `testDynamicFieldRegex()` | Test regex replacement functions | Console command |

### Debug Output Example

```javascript
// Console output from debugDynamicFields()
🔍 Dynamic Field Debug State:
  Fields with spans: 2 ['<span class="mceNonEditable">[[[FirstName]]]</span>', '<span class="mceNonEditable">[[[BrandName]]]</span>']
  Fields without spans: 0 []
```

## System Comparison: UTM vs Dynamic Fields

### Operational Differences

| Aspect              | UTM Management                  | Dynamic Field Management      |
| ------------------- | ------------------------------- | ----------------------------- |
| **Save Behaviour**  | Add UTM parameters to URLs      | Remove spans from fields      |
| **Load Behaviour**  | Remove UTM parameters from URLs | Add spans to fields           |
| **Target Elements** | `<a href>` attributes           | `[[[FieldName]]]` patterns    |
| **Visual Impact**   | Clean URLs in editor            | Non-editable field protection |
| **Storage Format**  | URLs without UTM parameters     | Fields without spans          |
| **User Experience** | Cleaner visual URLs             | Protected merge fields        |

### Shared Infrastructure

| Component                | Shared Usage                            |
| ------------------------ | --------------------------------------- |
| Mutation Observer Target | `.content-wrapper` element              |
| Monitoring Events        | `childList`, `subtree`, `characterData` |
| Initialisation Timing    | Document ready + delayed execution      |
| Debug Function Pattern   | Global console functions                |
| Integration Points       | Save process and content monitoring     |

## Implementation Benefits

### User Experience Improvements

| Benefit                           | Description                                         |
| --------------------------------- | --------------------------------------------------- |
| **Visual Clarity**                | Dynamic fields clearly marked as non-editable       |
| **Accidental Editing Prevention** | Users cannot accidentally modify merge field syntax |
| **Clean Storage**                 | Server stores clean merge fields without UI markup  |
| **Consistent Behaviour**          | Automatic span management without user intervention |

### Technical Advantages

| Advantage                   | Description                                           |
| --------------------------- | ----------------------------------------------------- |
| **Separation of Concerns**  | Visual markup separate from data storage              |
| **Maintainable Code**       | Clear, focused functions with single responsibilities |
| **Robust Pattern Matching** | Precise regex patterns for reliable field detection   |
| **Performance Optimised**   | Debounced operations and efficient DOM monitoring     |

## Troubleshooting Guide

### Common Issues and Solutions

| Issue                         | Symptoms                    | Solution                                          |
| ----------------------------- | --------------------------- | ------------------------------------------------- |
| **Spans not added on load**   | Fields appear editable      | Check console for initialisation errors           |
| **Spans not removed on save** | Server receives span markup | Verify save integration in `cmp_ai_pop-up_js.amp` |
| **Performance issues**        | Slow editor response        | Check mutation observer frequency                 |
| **Fields not detected**       | New fields not protected    | Verify field name in `DYNAMIC_FIELD_PATTERNS`     |

### Debug Commands

```javascript
// Check current state
debugDynamicFields();

// Add test content
addTestDynamicFields();

// Force span addition
forceAddSpansNow();

// Test regex replacement (IMPORTANT: Use this to verify the fix)
testDynamicFieldRegex();

// Manual testing
DynamicFieldManager.debugCurrentState();
```

## Future Enhancements

### Potential Improvements

| Enhancement                | Description                                      | Priority |
| -------------------------- | ------------------------------------------------ | -------- |
| **Custom Field Support**   | Allow configuration of additional field patterns | Medium   |
| **Visual Indicators**      | Enhanced styling for different field types       | Low      |
| **Performance Monitoring** | Track processing times and optimise              | Low      |
| **Error Recovery**         | Automatic recovery from processing failures      | Medium   |

---

_This documentation follows UK English conventions and provides comprehensive coverage of the Dynamic Field Management System implementation._
