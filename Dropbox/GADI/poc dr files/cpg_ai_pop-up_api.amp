<script runat="server">
Platform.Load("Core","1");

// Set headers for JSON response and CORS
Platform.Response.SetResponseHeader("Content-Type", "application/json;charset=UTF-8");
Platform.Response.SetResponseHeader("Access-Control-Allow-Origin", "*");
Platform.Response.SetResponseHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
Platform.Response.SetResponseHeader("Access-Control-Allow-Headers", "Content-Type");

var biz_env = Platform.Request.GetQueryStringParameter('biz_env');
var content_id = Platform.Request.GetQueryStringParameter('content_id');
var session_id = Platform.Request.GetQueryStringParameter('c');
var username = Platform.Function.Lookup('ent.Users','Username','SessionID',session_id);
var de_llm_results = (biz_env == 'UAT') ? 'ent.LLM_content_results_UAT' : 'ent.LLM_content_results';
var de_eca = (biz_env == 'UAT') ? 'ent.email_contentBlock_areas_UAT' : 'ent.email_contentBlock_areas';
var emailName = Platform.Function.Lookup(de_eca,'EmailName','EventID',content_id);
var content_name = Platform.Function.Lookup(de_eca,'friendly_name','EventID',content_id);


// Handle CORS preflight
if (Platform.Request.Method == "OPTIONS") {
  Platform.Response.Write("{}");
  return;
}

// get page url
var currentPageUrl = Platform.Request.RequestURL();
var protocolEnd = currentPageUrl.indexOf("://");
var baseUrl = '';
if (protocolEnd !== -1) {
  var afterProtocol = currentPageUrl.substring(protocolEnd + 3);
  var firstSlash = afterProtocol.indexOf("/");
  if (firstSlash !== -1) {
    baseUrl = currentPageUrl.substring(0, protocolEnd + 3 + firstSlash);
  } else {
    baseUrl = currentPageUrl; // No path, just domain
  }
} else {
  baseUrl = currentPageUrl; // Fallback if no protocol found
}

// Initialize tracking
var requestId = Platform.Function.GUID();
var startTime = new Date().getTime();
var debugLog = [];
debugLog.push("Request ID: " + requestId + " started");

// Function to log data to de_llm_results data extension
function logToLLMResultsDE(content_id, content_name, emailName, full_prompt, system_prompt, llm_result, event_id, status, debug_log, user_prompt, goal, form_json, type) {
  try {
    var currentDate = new Date();
    var result = Platform.Function.InsertData(
      de_llm_results,
      ["content_id", "content_name", "EmailName", "full_prompt_to_llm", "system_prompt", "llm_result", "created_date", "field", "event_id", "status", "debug_log", "user_prompt", "goal","session_id","username","form_json","type"],
      [content_id, content_name, emailName, full_prompt, system_prompt, llm_result, currentDate, "1", event_id, status, debug_log, user_prompt, goal,session_id,username, form_json || "", type]
    );
    return "Logged to DE: " + result;
  } catch(e) {
    return "Error logging to DE: " + e.message;
  }
}

// Basic logging function
function addLog(debugArr, message) {
  if (debugArr.length > 50) {
    debugArr.shift();
  }
  debugArr.push(message);
  return debugArr;
}

// OPTIMIZATION: Conservative JSON escape function - now mainly used for legacy paths
function escapeForJSON(str) {
  if (!str || typeof str !== 'string') {
    return str;
  }

  debugLog = addLog(debugLog, "PAYLOAD DEBUG - escapeForJSON called (legacy path) - input length: " + str.length);

  // Minimal escaping since we're now using manual payload construction
  var escaped = str
    .replace(/\\/g, '\\\\')    // Escape backslashes first
    .replace(/"/g, '\\"')      // Escape double quotes
    .replace(/\n/g, '\\n')     // Escape newlines
    .replace(/\r/g, '\\r')     // Escape carriage returns
    .replace(/\t/g, '\\t');    // Escape tabs

  debugLog = addLog(debugLog, "PAYLOAD DEBUG - escapeForJSON output length: " + escaped.length);
  debugLog = addLog(debugLog, "PAYLOAD DEBUG - escapeForJSON size increase: " + (escaped.length - str.length) + " chars");

  return escaped;
}

// Create unified system prompt
function createSystemPrompt(tableStructure, apiCallCount, previousContent, debugLog, requestType, formData) {
  // Handle new prompts requests differently
  if (requestType === 'new_prompts') {
    debugLog = addLog(debugLog, "createSystemPrompt: Handling new_prompts request");
    debugLog = addLog(debugLog, "createSystemPrompt: formData passed to createNewPromptsSystemPrompt: " + (formData ? Platform.Function.Stringify(formData) : "null"));
    return createNewPromptsSystemPrompt(formData);
  }

  // Handle existing template editing requests
  if (requestType === 'existing_template') {
    debugLog = addLog(debugLog, "createSystemPrompt: Handling existing_template request");
    return createExistingTemplateSystemPrompt();
  }

  // Extract checkbox states for dynamic class management
  var stackVertical = formData.stackContentVertical === true || formData.stackContentVertical === 'true';
  var extraSpacing = formData.extraSpacingInElements === true || formData.extraSpacingInElements === 'true';

  var forceRowClass = stackVertical ? 'force-row' : '';
  var columnClass = extraSpacing ? 'column' : 'display_block';

  // Define width sets based on column class type
  var widthSet1 = extraSpacing ? '610' : '590';  // 1 column
  var widthSet2 = extraSpacing ? '305' : '285';  // 2 columns
  var widthSet3 = extraSpacing ? '203' : '183';  // 3 columns

  var systemPrompt = 'You are an expert email HTML developer specializing in creating responsive, cross-client compatible email templates.\n\n' +
  'PRIMARY INSTRUCTIONS:\n' +
  '1. You MUST use the provided table structure as your starting point\n' +
  '2. You MUST replace placeholder content with appropriate content based on the user prompt\n' +
  '3. You MUST maintain the exact structure, attributes, and classes of the provided table\n\n';

  'TECHNICAL INSTRUCTIONS:\n' +
  '1. Each column width MUST be calculated based on the table structure provided\n' +
  '2. You MUST preserve ALL attributes, classes, styles, and HTML structure exactly as provided\n' +
  '3. You MUST ALWAYS return the FULL html document. THis means never returning a partial HTML document that cuts off the content and in its place says something like "[rest of the content goes here]".\n' +
  '4. You MUST NOT add, remove, or modify any HTML tags, only replace the [Placeholder content]\n\n';

  // Add table structure to system prompt if available
  if (tableStructure && tableStructure.length > 0) {
    // Check if this is a recursive call (API call count > 1)
    var isRecursiveCall = apiCallCount && parseInt(apiCallCount) > 1;
    debugLog = addLog(debugLog, "Is recursive call: " + isRecursiveCall);

    // CRITICAL INVESTIGATION: Log the table structure being used
    debugLog = addLog(debugLog, "🔍 SYSTEM PROMPT INVESTIGATION - Table structure length: " + tableStructure.length);
    debugLog = addLog(debugLog, "🔍 SYSTEM PROMPT INVESTIGATION - Table structure will be REFERENCED but NOT directly included in system prompt text");

    // For recursive calls, verify we're using the previous content
    if (isRecursiveCall && previousContent) {
      var prevContent = String(previousContent);
      if (tableStructure === prevContent) {
        debugLog = addLog(debugLog, "VERIFICATION: tableStructure is correctly set to previous_content for system prompt");
      } else {
        debugLog = addLog(debugLog, "ERROR: tableStructure is NOT set to previous_content for system prompt");
      }
    }
    systemPrompt += 'CRITICAL INSTRUCTIONS:\n';
    systemPrompt += '1. You MUST use EXACTLY the table structure provided above. Do not create a new table structure.\n';
    systemPrompt += '2. You MUST ONLY replace the [Placeholder content] text with appropriate content.\n';
    systemPrompt += '4. You MUST NEVER introduce width to <img> tags!!\n';
    systemPrompt += '5. Never EVER include footer blocks (social links and unsub links) UNLESS specified in the prompt!!\n';
    systemPrompt += '6. Never place text directly into the <td> tag, instead place the text in a <p> tag that goes in the <td> tag.\n';
    systemPrompt += '6. You MUST NEVER use div tags.\n\n';
    debugLog = addLog(debugLog, "Added table structure to system prompt with critical instructions");
  } else {
    // If no table structure provided, include the generic HTML requirements with dynamic classes
    systemPrompt += 'HTML REQUIREMENTS:\n' +
    '1. CRITICAL: You MUST use these EXACT HTML tags with all attributes and classes:\n\n' +
    '   OUTER TABLE TAG:\n' +
    '   <table border=\"0\" align=\"center\" cellspacing=\"0\" cellpadding=\"0\" width=\"650\"' + (forceRowClass ? ' class=\"' + forceRowClass + '\"' : '') + ' style=\"max-width: 650px; width: 100%;\" bgcolor=\"#FFFFFF\">\n\n' +
    '   OUTER TD TAG:\n' +
    '   <td align=\"center\" valign=\"top\"' + (forceRowClass ? ' class=\"' + forceRowClass + '\"' : '') + ' style=\"padding: 20px;\" width=\"'+widthSet1+'\">\n\n' +
    '   INNER TABLE TAG:\n' +
    '   <table width=\"'+widthSet1+'\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\"' + (forceRowClass ? ' class=\"' + forceRowClass + '\"' : '') + ' style=\"max-width: '+widthSet1+'px; width: 100%;\">\n\n' +
    '   COLUMN TD TAG:\n' +
    '   <td class=\"' + columnClass + '\" align=\"left\" valign=\"top\" width=\"XXX\" style=\"max-width: XXXpx; width: XXXpx;\">\n\n' +
    '2. CRITICAL: For column TD tags, replace XXX with the appropriate width:\n' +
    '   - 1 column: <td class=\"' + columnClass + '\" align=\"left\" valign=\"top\" width=\"' + widthSet1 + '\" style=\"max-width: ' + widthSet1 + 'px; width: ' + widthSet1 + 'px;\">\n' +
    '   - 2 columns: <td class=\"' + columnClass + '\" align=\"left\" valign=\"top\" width=\"' + widthSet2 + '\" style=\"max-width: ' + widthSet2 + 'px; width: ' + widthSet2 + 'px;\">\n' +
    '   - 3 columns: <td class=\"' + columnClass + '\" align=\"left\" valign=\"top\" width=\"' + widthSet3 + '\" style=\"max-width: ' + widthSet3 + 'px; width: ' + widthSet3 + 'px;\">\n\n';
  }

  systemPrompt += '3. WHAT YOU CAN MODIFY:\n' +
  '   - You MUST replace [Placeholder content] with appropriate content (text, images, buttons, links)\n' +
  '   - You CAN add additional CSS properties to style attributes as long as you don\'t add additional padding that is not part of the above table structure and you don\'t remove or change width and max-width values\n' +
  '   - You CAN embed additional elements inside the cells including nested tables\n\n' +
  '4. NESTED TABLES REQUIREMENTS:\n' +
  '   - Nested table tag: <table width=\"XXX\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\"' + (forceRowClass ? ' class=\"' + forceRowClass + '\"' : '') + ' style=\"max-width: XXXpx; width: 100%;\">\n' +
  '   - Nested column tag: <td class=\"' + columnClass + '\" align=\"left\" valign=\"top\" width=\"XXX\" style=\"max-width: XXXpx; width: XXXpx;\">\n' +
  '   - For nested tables, calculate column widths by dividing the parent table width by the number of columns\n\n' +

   'STYLING SPECIFICATIONS:\n' +
  '1. Use consistent styling throughout the email\n' +
  '2. Use appropriate font sizes for headings and body text\n' +
  '3. Maintain proper spacing between elements\n\n' +

  'EMAIL HTML BEST PRACTICES:\n' +
  '1. Use ONLY table tags (<table>, <tr>, and <td>) with inline CSS for layout\n' +
  '2. Preserve ALL <img> and <a> tags and their src and href values\n' +
  '3. Do NOT include DOCTYPE, <html>, <head>, <meta>, <body>, <div>, JavaScript, or forms\n' +
  '4. Never add commentary or explanations in your response - return ONLY the HTML\n';
  '5. You MUST NEVER use div tags.\n\n';
  '6. You MUST NEVER use div tags.\n\n';
  return systemPrompt;
}

// ECMAScript 3 compatible trim function
function trimString(str) {
  if (!str) return str;
  return str.replace(/^\s+|\s+$/g, '');
}

// Create system prompt specifically for existing template editing
function createExistingTemplateSystemPrompt() {
  var systemPrompt = 'You are an expert email HTML developer specializing in editing and improving existing email templates.\n\n';

  systemPrompt += 'TASK: You will receive existing HTML email content that needs to be edited and improved based on the user\'s instructions.\n\n';

  systemPrompt += 'CRITICAL STRUCTURE PRESERVATION:\n';
  systemPrompt += '1. You MUST preserve the EXACT table structure and layout of the existing HTML\n';
  systemPrompt += '2. You MUST maintain ALL table tags (<table>, <tr>, <td>) with their exact attributes\n';
  systemPrompt += '3. You MUST preserve ALL width and max-width values exactly as provided\n';
  systemPrompt += '4. You MUST maintain all existing CSS classes, IDs, and styling attributes\n';
  systemPrompt += '5. You MUST preserve all <img> src attributes and <a> href attributes exactly as provided\n';
  systemPrompt += '6. You MUST preserve the overall email layout and column structure\n';
  systemPrompt += '7. You MUST always return the FULL html structure without cutting off any content\n';
  systemPrompt += '8. You MUST NEVER add, remove, or restructure major HTML elements unless specifically requested\n\n';

  systemPrompt += 'TECHNICAL REQUIREMENTS:\n';
  systemPrompt += '1. You MUST preserve ALL table structures and nested tables exactly as provided\n';
  systemPrompt += '2. You MUST maintain all cellpadding, cellspacing, border, align, and valign attributes\n';
  systemPrompt += '3. You MUST preserve all style attributes including padding, margin, background properties\n';
  systemPrompt += '4. You MUST maintain the exact column widths and table dimensions\n';
  systemPrompt += '5. You MUST NEVER introduce width to <img> tags unless already present\n';
  systemPrompt += '6. You MUST NEVER use div tags - use only table-based layout\n';
  systemPrompt += '7. Never place text directly into <td> tags - use <p> tags inside <td> tags\n\n';

  systemPrompt += 'WHAT YOU CAN MODIFY:\n';
  systemPrompt += '- Text content within existing elements (while preserving HTML structure)\n';
  systemPrompt += '- CSS styling properties (colors, fonts, spacing) without changing layout\n';
  systemPrompt += '- Image alt text and titles\n';
  systemPrompt += '- Add minor styling enhancements that improve design without affecting structure\n';
  systemPrompt += '- Content within table cells while maintaining the cell structure\n\n';

  systemPrompt += 'WHAT YOU MUST PRESERVE:\n';
  systemPrompt += '- All table structures, nested tables, and table hierarchy\n';
  systemPrompt += '- All width, max-width, height, and dimensional values\n';
  systemPrompt += '- All image URLs (src attributes) and link URLs (href attributes)\n';
  systemPrompt += '- All CSS classes, IDs, and structural attributes\n';
  systemPrompt += '- All cellpadding, cellspacing, border, align, valign attributes\n';
  systemPrompt += '- Overall email layout, column structure, and responsive behavior\n\n';

  systemPrompt += 'EMAIL HTML BEST PRACTICES:\n';
  systemPrompt += '1. Use ONLY table tags (<table>, <tr>, and <td>) with inline CSS for layout\n';
  systemPrompt += '2. Preserve ALL <img> and <a> tags and their src and href values\n';
  systemPrompt += '3. Do NOT include DOCTYPE, <html>, <head>, <meta>, <body>, <div>, JavaScript, or forms\n';
  systemPrompt += '4. Never add commentary or explanations in your response - return ONLY the HTML\n';
  systemPrompt += '5. Ensure all changes maintain email client compatibility\n';
  systemPrompt += '6. Maintain proper nesting of tables and preserve all structural relationships\n\n';

  systemPrompt += 'OUTPUT: Return only the modified HTML content with your improvements applied, preserving the exact structure and layout.\n';

  return systemPrompt;
}

// Create system prompt specifically for new prompts
function createNewPromptsSystemPrompt(formData) {
  // DEBUG: Log function entry and formData
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: Function called");
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: formData type: " + typeof formData);
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: formData value: " + (formData ? Platform.Function.Stringify(formData) : "null"));

  // Create simple system prompt that focuses on natural, conversational style
  var systemPrompt = 'You are an expert at creating conversational email template prompts that work well with AI systems.\n\n';

  systemPrompt += 'Your task: Create 3 natural, conversational prompts that tell someone how to create HTML email templates.\n\n';

  // Step 1: Restore form data parsing logic
  var parsedFormData = {};
  if (formData && typeof formData === 'string') {
    debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: formData is string, attempting to parse JSON");
    try {
      parsedFormData = Platform.Function.ParseJSON(formData);
      debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: JSON parsing successful");
    } catch(e) {
      debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: JSON parsing failed: " + e.message);
      // If parsing fails, continue with empty form data
    }
  } else if (formData && typeof formData === 'object') {
    debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: formData is object, using directly");
    parsedFormData = formData;
  } else {
    debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: formData is neither string nor object, using empty object");
  }

  // DEBUG: Log parsed form data
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: parsedFormData: " + Platform.Function.Stringify(parsedFormData));

  // Step 2: Keep form field extraction
  var emailType = parsedFormData.emailType || 'newsletter';
  var layoutStructure = parsedFormData.layoutStructure || 'mixed-layouts';
  var visualStyle = parsedFormData.visualStyle || 'modern-sophisticated';
  var contentElements = parsedFormData.contentElements || 'mixed-media';
  var brandContext = parsedFormData.brandContext || '';

  // DEBUG: Log extracted values
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: emailType extracted: '" + emailType + "'");
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: layoutStructure extracted: '" + layoutStructure + "'");
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: visualStyle extracted: '" + visualStyle + "'");
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: contentElements extracted: '" + contentElements + "'");
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: brandContext extracted: '" + brandContext + "'");
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: brandContext length: " + brandContext.length);
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: brandContext isEmpty: " + (brandContext === ''));
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: brandContext trimmed: '" + trimString(brandContext) + "'");
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: brandContext trimmed length: " + trimString(brandContext).length);

  // Step 3: Simplify mapping logic - concise, clear language
  var simpleEmailType = emailType === 'newsletter' ? 'newsletter' :
                       emailType === 'promotional' ? 'promotional email' :
                       emailType === 'announcement' ? 'announcement' :
                       emailType === 'welcome' ? 'welcome email' :
                       emailType === 'event' ? 'event invitation' :
                       emailType === 'transactional' ? 'transactional email' : 'newsletter';

  var simpleLayout = layoutStructure === 'mixed-layouts' ? 'mixed column layouts' :
                    layoutStructure === 'single-column' ? 'single column layout' :
                    layoutStructure === 'two-column' ? 'two column layout' :
                    layoutStructure === 'three-column' ? 'three column layout' :
                    layoutStructure === 'grid-style' ? 'grid layout' : 'mixed column layouts';

  var simpleVisual = visualStyle === 'modern-sophisticated' ? 'modern and clean' :
                    visualStyle === 'clean-minimal' ? 'clean and minimal' :
                    visualStyle === 'professional-corporate' ? 'professional' :
                    visualStyle === 'bold-engaging' ? 'bold and engaging' :
                    visualStyle === 'elegant-premium' ? 'elegant' : 'modern and clean';

  var simpleContent = contentElements === 'mixed-media' ? 'varied content with images and buttons' :
                     contentElements === 'images-cta' ? 'images with call-to-action buttons' :
                     contentElements === 'text-heavy' ? 'text-heavy with headlines' :
                     contentElements === 'product-showcase' ? 'product showcase with pricing' :
                     contentElements === 'feature-highlights' ? 'feature highlights with icons' : 'varied content with images and buttons';

  // Step 5: Fix brand context integration - ensure it's used in all variations
  var contextDescription = 'creative email designer';
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: Before contextDescription logic - brandContext: '" + brandContext + "'");
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: brandContext && trimString(brandContext): " + (brandContext && trimString(brandContext)));
  if (brandContext && trimString(brandContext)) {
    debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: Using brandContext for contextDescription");
    contextDescription = trimString(brandContext);
  } else if (emailType !== 'newsletter') {
    debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: Using emailType for contextDescription");
    contextDescription = 'creative email designer for ' + simpleEmailType;
  } else {
    debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: Using default contextDescription");
  }
  debugLog = addLog(debugLog, "createNewPromptsSystemPrompt: Final contextDescription: '" + contextDescription + "'");

  // Create dynamic prompt framework based on form data
  var roleSpecialization = brandContext && trimString(brandContext) ?
    'specializing in ' + trimString(brandContext) + ' email templates' :
    'specializing in ' + simpleVisual + ' ' + simpleEmailType + ' templates';

  var taskDescription = 'Generate an HTML ' + simpleEmailType + ' template with inline CSS designed with ' + simpleLayout + ' and ' + simpleVisual + ' styling';

  var contentFocus = simpleContent === 'varied content with images and buttons' ? 'diverse content elements including images and interactive buttons' :
                    simpleContent === 'images with call-to-action buttons' ? 'prominent images paired with compelling call-to-action buttons' :
                    simpleContent === 'text-heavy with headlines' ? 'rich text content with strong headlines and typography' :
                    simpleContent === 'product showcase with pricing' ? 'product displays with pricing information and purchase elements' :
                    simpleContent === 'feature highlights with icons' ? 'feature presentations with supporting icons and visual elements' :
                    'diverse content elements including images and interactive buttons';

  systemPrompt += 'DYNAMIC PROMPT FRAMEWORK: Each prompt must integrate these customization elements:\n';
  systemPrompt += '- Role Specialization: "' + roleSpecialization + '"\n';
  systemPrompt += '- Task Focus: "' + taskDescription + '"\n';
  systemPrompt += '- Content Elements: "' + contentFocus + '"\n';
  systemPrompt += '- Layout Approach: Adapt "' + simpleLayout + '" to each of the 3 layout variations\n';
  systemPrompt += '- Visual Style: Incorporate "' + simpleVisual + '" aesthetic throughout\n\n';

  systemPrompt += 'WORKING EXAMPLE ANALYSIS:\n';
  systemPrompt += 'This prompt works because it uses:\n';
  systemPrompt += '- Conversational, direct language ("Give me", "Really impress me")\n';
  systemPrompt += '- Specific concrete requests ("one, two and three columns mixed across")\n';
  systemPrompt += '- Natural flow without rigid structure\n';
  systemPrompt += '- Action-oriented instructions ("Use image and hyperlink placeholders")\n';
  systemPrompt += '- Clear expectations ("Make sure every element is populated")\n\n';

  systemPrompt += 'SIMPLE REQUIREMENTS:\n';
  systemPrompt += '1. Create 3 natural prompts (150-200 words each) like the working example\n';
  systemPrompt += '2. Use conversational language: "Give me", "Create", "Make sure", "Include"\n';
  systemPrompt += '3. Make them for: 1=balanced layouts, 2=uneven layouts, 3=mixed layouts\n';
  systemPrompt += '4. Keep the same direct, friendly tone as the working example\n';
  systemPrompt += '5. Focus on ' + simpleEmailType + ' with ' + simpleContent + ' and ' + simpleVisual + ' style\n\n';
  if (roleSpecialization) { 
    systemPrmopt += '6. Smartly incorporate this context: '+ roleSpecialization + '\n\n';
  }

  systemPrompt += 'CRITICAL RESTRICTIONS:\n';
  systemPrompt += '1. NEVER specify pixels, percentages, color codes, or any specific measurements\n';
  systemPrompt += '2. NEVER instruct gradients, hover effects, or advanced CSS features\n';
  systemPrompt += '3. FORBIDDEN TERMS: "visual cues", "visual hierarchy", "adaptability", "engagement potential", "interactive elements", "dynamic", "seamlessly", "strategic", "intelligent", "robust", "mirror-image", "clean", "crisp", "aesthetic", "symmetrical balance"\n';
  systemPrompt += '4. NEVER use formal structure like "Role: Task: Overall Layout:"\n';
  systemPrompt += '5. NEVER use specific URLs, dimensions, or static values\n';
  systemPrompt += '6. NEVER mention content topics, culture, or goals - focus solely on layout structure\n';
  systemPrompt += '7. NEVER create structured sections - write as natural conversation\n';
  systemPrompt += '8. Keep language simple and direct, avoid complex phrases\n\n';

  systemPrompt += 'OUTPUT: Just return 3 numbered prompts like:\n';
  systemPrompt += '1. [conversational prompt for balanced layouts]\n';
  systemPrompt += '2. [conversational prompt for uneven layouts]\n';
  systemPrompt += '3. [conversational prompt for mixed layouts]\n\n';

  // Add debugging for testing complete system
  debugLog = addLog(debugLog, "systemPrompt new_prompts:"+Platform.Function.Stringify(systemPrompt));

  return systemPrompt;
}

// Extract base64-encoded text from Claude response
function getClaudeText(responseObj) {
  var text = "";
  debugLog = addLog(debugLog, "getClaudeText: Starting extraction (expecting base64-encoded content)");

  if (!responseObj) {
    debugLog = addLog(debugLog, "getClaudeText: responseObj is null or undefined");
    return text;
  }

  debugLog = addLog(debugLog, "getClaudeText: responseObj exists");

  // Log the structure of the response object
  var responseKeys = [];
  for (var key in responseObj) {
    if (responseObj.hasOwnProperty(key)) {
      responseKeys.push(key);
    }
  }
  debugLog = addLog(debugLog, "getClaudeText: responseObj keys: " + responseKeys.join(", "));

  // Check for the standard Claude API response format: {"content":[{"text":"[base64encoded]"}]}
  if (responseObj.content) {
    debugLog = addLog(debugLog, "getClaudeText: Found content property");

    if (responseObj.content.length) {
      debugLog = addLog(debugLog, "getClaudeText: content is array with length: " + responseObj.content.length);

      if (responseObj.content.length > 0) {
        var firstContentItem = responseObj.content[0];
        debugLog = addLog(debugLog, "getClaudeText: First content item exists");

        // Log the structure of the first content item
        var contentKeys = [];
        for (var contentKey in firstContentItem) {
          if (firstContentItem.hasOwnProperty(contentKey)) {
            contentKeys.push(contentKey);
          }
        }
        debugLog = addLog(debugLog, "getClaudeText: First content item keys: " + contentKeys.join(", "));

        if (firstContentItem.text) {
          text = String(firstContentItem.text);
          debugLog = addLog(debugLog, "getClaudeText: Successfully extracted base64 text, length: " + text.length);

          // Log first 50 characters to verify it looks like base64
          var textPreview = text.substring(0, 50);
          debugLog = addLog(debugLog, "getClaudeText: Base64 text preview (first 50 chars): " + textPreview);

          // Basic validation that this looks like base64 (contains only valid base64 characters)
          // ECMAScript 3 compatible regex pattern
          var cleanText = text.replace(/\s/g, '');
          var base64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
          var isValidBase64 = true;
          for (var i = 0; i < cleanText.length; i++) {
            if (base64Chars.indexOf(cleanText.charAt(i)) === -1) {
              isValidBase64 = false;
              break;
            }
          }
          debugLog = addLog(debugLog, "getClaudeText: Text appears to be valid base64: " + isValidBase64);

        } else {
          debugLog = addLog(debugLog, "getClaudeText: No text property in first content item");
        }
      } else {
        debugLog = addLog(debugLog, "getClaudeText: content array is empty");
      }
    } else {
      debugLog = addLog(debugLog, "getClaudeText: content is not an array or has no length property");
    }
  } else {
    debugLog = addLog(debugLog, "getClaudeText: No content property found");
  }

  // Fallback for older Claude API format (also expecting base64)
  if (!text && responseObj.completion) {
    text = String(responseObj.completion);
    debugLog = addLog(debugLog, "getClaudeText: Used fallback completion property (base64), length: " + text.length);
  }

  if (!text) {
    debugLog = addLog(debugLog, "getClaudeText: No base64 text extracted from response");
  }

  return text;
}

/**
 * Converts all div elements in HTML to email-friendly table structures.
 * You had shared this logic. We do not remove it or any logic.
 */
function convertDivsToTables(html) {
  var divRegex = /<div([^>]*)>([\s\S]*?)<\/div>/gi;
  return html.replace(divRegex, function(match, attributes, content) {
    var styleAttr = extractAttribute(attributes, 'style');
    var classAttr = extractAttribute(attributes, 'class');
    return createTableFromDiv(styleAttr, classAttr, content);
  });
}

/**
 * Extract an attribute value from an HTML attributes string
 */
function extractAttribute(attributes, attrName) {
  var regex = new RegExp(attrName + '\\s*=\\s*["\']([^"\']*)["\']', 'i');
  var match = attributes.match(regex);
  return match ? match[1] : '';
}

/**
 * Create a table structure from div properties
 */
function createTableFromDiv(styleAttr, classAttr, content) {
  var tableHtml = '<table cellspacing="0" cellpadding="0" border="0" width="100%" style="width: 100%;"';
  if (classAttr) {
    tableHtml += ' class="' + classAttr + '"';
  }
  tableHtml += '>\n<tr>\n<td';

  if (isHorizontalDivider(styleAttr)) {
    var fontSizeValue = extractStyleValue(styleAttr, 'font-size') || '1px';
    var lineHeightValue = extractStyleValue(styleAttr, 'line-height') || extractStyleValue(styleAttr, 'height') || '1px';
    var borderColor = extractStyleValue(styleAttr, 'border-color') || extractStyleValue(styleAttr, 'border-bottom-color') || '#606669';

    tableHtml += ' height="' + lineHeightValue + '"';
    tableHtml += ' style="height: ' + lineHeightValue + '; ';
    tableHtml += 'font-size: ' + fontSizeValue + '; ';
    tableHtml += 'line-height: ' + lineHeightValue + '; ';
    tableHtml += 'background-color: ' + borderColor + ';">';
    tableHtml += content || '&nbsp;';
  } else if (styleAttr.indexOf('background') !== -1) {
    var widthValue = extractStyleValue(styleAttr, 'width');
    var heightValue = extractStyleValue(styleAttr, 'height');

    if (widthValue) {
      tableHtml += ' width="' + widthValue + '"';
    }
    if (heightValue) {
      tableHtml += ' height="' + heightValue + '"';
    }
    tableHtml += ' style="' + styleAttr + '">';
    tableHtml += content;
  } else {
    if (styleAttr) {
      var widthVal = extractStyleValue(styleAttr, 'width');
      var heightVal = extractStyleValue(styleAttr, 'height');
      if (widthVal) {
        tableHtml += ' width="' + widthVal + '"';
      }
      if (heightVal) {
        tableHtml += ' height="' + heightVal + '"';
      }
      tableHtml += ' style="' + styleAttr + '">';
    } else {
      tableHtml += '>';
    }
    tableHtml += content;
  }

  tableHtml += '</td>\n</tr>\n</table>';

  return tableHtml;
}

/**
 * Determines if a div appears to be a horizontal divider
 */
function isHorizontalDivider(styleAttr) {
  return (styleAttr.indexOf('border-bottom-width') !== -1 ||
          styleAttr.indexOf('border-bottom-style') !== -1 ||
          styleAttr.indexOf('border-bottom') !== -1) &&
         styleAttr.indexOf('solid') !== -1;
}

/**
 * Extract a style property value from a style attribute
 */
function extractStyleValue(styleAttr, property) {
  var regex = new RegExp(property + '\\s*:\\s*([^;]+)', 'i');
  var match = styleAttr.match(regex);
  return match ? trimWhitespace(match[1]) : '';
}

/**
 * Trim whitespace from a string
 */
function trimWhitespace(str) {
  return str.replace(/^\s+|\s+$/g, '');
}


// Parse the request body
var rawData = Platform.Request.GetPostData();
debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Raw data length: " + (rawData ? rawData.length : 0));

var jsonData;
var response = {};

try {
  jsonData = Platform.Function.ParseJSON(rawData);
  debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Request parsed successfully");

  // Log the keys in the jsonData object to help diagnose issues
  var jsonKeys = [];
  for (var key in jsonData) {
    if (jsonData.hasOwnProperty(key)) {
      jsonKeys.push(key);
    }
  }
  debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - JSON data keys: " + jsonKeys.join(", "));

  // Log sizes of key fields for investigation
  if (jsonData.existing_html) {
    debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - existing_html field size: " + String(jsonData.existing_html).length + " characters");
  }
  if (jsonData.prompt) {
    debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Original prompt field size: " + String(jsonData.prompt).length + " characters");
  }
} catch(e) {
  response.error = "Invalid JSON in request body";
  response.requestId = requestId;
  response.debugLog = debugLog;
  Platform.Response.Write(Platform.Function.Stringify(response));
  return;
}

// Validate the presence of prompt
if (!jsonData || !jsonData.prompt) {
  response.error = "No prompt provided";
  response.requestId = requestId;
  response.debugLog = debugLog;
  Platform.Response.Write(Platform.Function.Stringify(response));
  return;
}

var prompt = String(jsonData.prompt);
debugLog = addLog(debugLog, "Prompt length: " + prompt.length);
debugLog = addLog(debugLog, "Prompt used: " + prompt);

// Extract request type (default to 'new_templates' for backward compatibility)
var requestType = jsonData.type || 'new_templates';
debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Request type: " + requestType);

// Handle existing template content for AI editing
if (requestType === 'existing_template') {
  var existingHtml = "";
  if (jsonData.existing_html) {
    try {
      // Handle HTML content directly without JSON parsing to reduce encoding layers
      debugLog = addLog(debugLog, "PAYLOAD DEBUG - Raw existing_html type: " + typeof jsonData.existing_html);
      debugLog = addLog(debugLog, "PAYLOAD DEBUG - Raw existing_html length: " + String(jsonData.existing_html).length);
      debugLog = addLog(debugLog, "PAYLOAD DEBUG - Raw existing_html preview: " + String(jsonData.existing_html).substring(0, 200));

      // Check if it's already a string (optimized approach) or needs JSON parsing (legacy)
      if (typeof jsonData.existing_html === 'string') {
        // Direct string - optimized approach
        existingHtml = String(jsonData.existing_html);
        debugLog = addLog(debugLog, "PAYLOAD DEBUG - Using direct HTML string (optimized)");
      } else {
        // Try JSON parsing for backward compatibility
        existingHtml = Platform.Function.ParseJSON(jsonData.existing_html);
        debugLog = addLog(debugLog, "PAYLOAD DEBUG - Used JSON parsing (legacy compatibility)");
      }

      debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Final existing HTML length: " + existingHtml.length);
      debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Final existing HTML preview: " + existingHtml.substring(0, 200));

      // PROPER FIX: Include existing HTML in prompt like new_templates subsequent calls do
      debugLog = addLog(debugLog, "✅ PROPER FIX - Including existing HTML in prompt like new_templates chaining");
      debugLog = addLog(debugLog, "✅ PROPER FIX - Original prompt length: " + prompt.length);

      // Use the same format as new_templates subsequent calls (callClaudeForAllGoals)
      prompt = "EXISTING HTML CONTENT TO EDIT:\n" + existingHtml + "\n\nUSER INSTRUCTIONS:\n" + prompt;
      debugLog = addLog(debugLog, "✅ PROPER FIX - Combined prompt length: " + prompt.length);
      debugLog = addLog(debugLog, "✅ PROPER FIX - Size increase: " + (prompt.length - String(jsonData.prompt).length) + " characters");
    } catch(parseErr) {
      debugLog = addLog(debugLog, "PAYLOAD DEBUG - Failed to process existing_html: " + parseErr.message);
      debugLog = addLog(debugLog, "PAYLOAD DEBUG - Attempting fallback to string conversion");
      try {
        existingHtml = String(jsonData.existing_html);
        debugLog = addLog(debugLog, "✅ PROPER FIX - Fallback: Including " + existingHtml.length + " characters of HTML in prompt");
        prompt = "EXISTING HTML CONTENT TO EDIT:\n" + existingHtml + "\n\nUSER INSTRUCTIONS:\n" + prompt;
        debugLog = addLog(debugLog, "✅ PROPER FIX - Fallback successful, prompt length: " + prompt.length);
      } catch(fallbackErr) {
        debugLog = addLog(debugLog, "PAYLOAD DEBUG - Fallback failed: " + fallbackErr.message);
        // Continue with original prompt if all parsing fails
      }
    }
  } else {
    debugLog = addLog(debugLog, "PAYLOAD DEBUG - No existing_html found in existing_template request");
  }
}

// DEBUG: Log form_json details for new_prompts requests
if (requestType === 'new_prompts') {
  debugLog = addLog(debugLog, "DEBUG new_prompts: jsonData.form_json exists: " + (jsonData.form_json ? "YES" : "NO"));
  if (jsonData.form_json) {
    debugLog = addLog(debugLog, "DEBUG new_prompts: jsonData.form_json type: " + typeof jsonData.form_json);
    debugLog = addLog(debugLog, "DEBUG new_prompts: jsonData.form_json content: " + Platform.Function.Stringify(jsonData.form_json));
  }
}

// Extract form_json for storage in DE
var form_json = "";
if (jsonData.form_json) {
  try {
    form_json = Platform.Function.Stringify(jsonData.form_json);
    debugLog = addLog(debugLog, "Form JSON extracted, length: " + form_json.length);
  } catch(formJsonErr) {
    debugLog = addLog(debugLog, "Failed to stringify form_json: " + formJsonErr.message);
    form_json = "";
  }
} else {
  debugLog = addLog(debugLog, "No form_json found in request");
}

// Check if table structure is provided
var tableStructure = null;
var apiCallCount = 1; // Default to 1 for first call

// Check if this is a recursive call
if (jsonData.api_call_count) {
  apiCallCount = parseInt(jsonData.api_call_count);
  debugLog = addLog(debugLog, "Processing API call #" + apiCallCount);
  debugLog = addLog(debugLog, "API call count type: " + typeof jsonData.api_call_count);
  debugLog = addLog(debugLog, "API call count raw value: " + jsonData.api_call_count);

  // For recursive calls (API call count > 1), prioritize using previous content as the table structure
  if (jsonData.previous_content && apiCallCount > 1) {
    var previousContent = String(jsonData.previous_content);
    debugLog = addLog(debugLog, "Previous content found for recursive call, length: " + previousContent.length);
    debugLog = addLog(debugLog, "Previous content type: " + typeof jsonData.previous_content);

    // Check if the previous_content is valid HTML
    if (previousContent.indexOf('<') !== -1 && previousContent.indexOf('>') !== -1) {
      debugLog = addLog(debugLog, "Previous content appears to be HTML");
    } else {
      debugLog = addLog(debugLog, "WARNING: Previous content does not appear to be HTML: " + previousContent.substring(0, 50));
    }

    // Check if the previous_content is actually being passed correctly
    if (previousContent.length < 10) {
      debugLog = addLog(debugLog, "WARNING: Previous content is suspiciously short: " + previousContent);
    } else {
      debugLog = addLog(debugLog, "Previous content type: " + typeof previousContent);
    }

    // Log a sample of the previous content
    if (previousContent.length > 200) {
      debugLog = addLog(debugLog, "Previous content start: " + previousContent.substring(0, 100));
      debugLog = addLog(debugLog, "Previous content end: " + previousContent.substring(previousContent.length - 100));
    }

    // Check if the previous content contains a complete table
    if (previousContent.indexOf('<table') !== -1 && previousContent.indexOf('</table>') !== -1) {
      debugLog = addLog(debugLog, "CRITICAL FIX: Using previous content as table structure for recursive call #" + apiCallCount);

      // Store the original table structure for comparison
      var originalTableStructure = tableStructure;

      // Set the table structure to the previous content
      tableStructure = previousContent;

      // Log the change
      debugLog = addLog(debugLog, "Table structure BEFORE: " + (originalTableStructure ? originalTableStructure.substring(0, 50) : "null"));
      debugLog = addLog(debugLog, "Table structure AFTER: " + tableStructure.substring(0, 50));
    } else {
      debugLog = addLog(debugLog, "Previous content does not contain a complete table, falling back to original table structure");
      // Fall back to the original table structure if provided
      if (jsonData.table_structure) {
        tableStructure = String(jsonData.table_structure);
        debugLog = addLog(debugLog, "Using original table structure as fallback, length: " + tableStructure.length);
      }
    }
  } else {
    // No previous content for recursive call, use original table structure
    debugLog = addLog(debugLog, "No previous content found for recursive call #" + apiCallCount);
    if (jsonData.table_structure) {
      tableStructure = String(jsonData.table_structure);
      debugLog = addLog(debugLog, "Using original table structure, length: " + tableStructure.length);
    }
  }
} else {
  // Initial API call (not recursive)
  debugLog = addLog(debugLog, "Initial API call (not recursive)");
  if (jsonData.table_structure) {
    tableStructure = String(jsonData.table_structure);
    debugLog = addLog(debugLog, "Using original table structure for initial call, length: " + tableStructure.length);
  }
}

// Log the table structure details if available
if (tableStructure && tableStructure.length > 0) {
  // Log the first 100 characters and last 100 characters of the table structure
  if (tableStructure.length > 200) {
    debugLog = addLog(debugLog, "Table structure start: " + tableStructure.substring(0, 100));
    debugLog = addLog(debugLog, "Table structure end: " + tableStructure.substring(tableStructure.length - 100));
  }

  // We'll use this in the system prompt instead of adding it to the user prompt
  debugLog = addLog(debugLog, "Table structure will be used in system prompt");
} else {
  debugLog = addLog(debugLog, "No table structure available for this call");
}

// Temperature logic
var temperature = 0.7;
if (jsonData.temperature) {
  try {
    var tempValue = parseFloat(jsonData.temperature);
    if (!isNaN(tempValue)) {
      temperature = tempValue / 100;
      temperature = Math.max(0, Math.min(1, temperature));
    }
  } catch(e) {
    debugLog = addLog(debugLog, "Invalid temperature value, using default: 0.7");
  }
}

// Model logic
var model = "claude-3-5-haiku@20241022";  // Default model
if (jsonData.model) {
  // Get the model string
  var modelStr = String(jsonData.model);

  // Convert dash format to @ format if needed
  if (modelStr.indexOf('-') !== -1 && modelStr.indexOf('@') === -1) {
    // Find the last dash and replace it with @
    var lastDashIndex = modelStr.lastIndexOf('-');
    if (lastDashIndex !== -1) {
      modelStr = modelStr.substring(0, lastDashIndex) + '@' + modelStr.substring(lastDashIndex + 1);
      debugLog = addLog(debugLog, "Converted model format from dash to @: " + modelStr);
    }
  }

  // Map old model names to new standardized format if needed
  if (modelStr === "claude-3-haiku-20240307" || modelStr === "claude-3-haiku@20240307") {
    model = "claude-3-haiku@20240307";
  } else if (modelStr === "claude-3-5-haiku-20241022" || modelStr === "claude-3-5-haiku@20241022") {
    model = "claude-3-5-haiku@20241022";
  } else if (modelStr === "claude-3-5-sonnet-20241022" || modelStr === "claude-3-5-sonnet-v2@20241022") {
    model = "claude-3-5-sonnet-v2@20241022";
  } else if (modelStr === "claude-3-5-sonnet-20240620" || modelStr === "claude-3-5-sonnet@20240620") {
    model = "claude-3-5-sonnet@20240620";
  } else if (modelStr === "claude-3-7-sonnet-20250219" || modelStr === "claude-3-7-sonnet@20250219") {
    model = "claude-3-7-sonnet@20250219";
  } else {
    // Use the converted model string if it doesn't match any of the above
    model = modelStr;
  }

  debugLog = addLog(debugLog, "Using standardized model format: " + model);
}
debugLog = addLog(debugLog, "Using model: " + model + ", temp: " + temperature);

// Retrieve HTML content if needed
var htmlContent = null;

// Get API credentials from the same source as test_svca_endpoint.amp
var endpoint_url = Platform.Function.Lookup('ent.API_Keys','EndpointURL','Id','nca-datapl-nonprod-econgen');
var api_key = Platform.Function.Lookup('ent.API_Keys','Key','Id','nca-datapl-nonprod-econgen');
var ALLOWED_ORIGIN = baseUrl;

// Check if API key was found
if (!api_key) {
  response.error = "API key not found";
  response.requestId = requestId;
  response.debugLog = debugLog;
  Platform.Response.Write(Platform.Function.Stringify(response));
  return;
}

// Check if endpoint URL was found
if (!endpoint_url) {
  response.error = "Endpoint URL not found";
  response.requestId = requestId;
  response.debugLog = debugLog;
  Platform.Response.Write(Platform.Function.Stringify(response));
  return;
}


debugLog = addLog(debugLog, "Creating API payload (single-call fallback)");

var payloadString;
if (htmlContent) {
  try {
    debugLog = addLog(debugLog, "PAYLOAD OPTIMIZATION: Using htmlContent path (legacy) - applying manual escaping");

    var combined =
      "Below is the HTML to transform. Return only the updated HTML, no commentary.\n\n" +
      htmlContent + "\n\n" +
      "USER PROMPT:\n" + prompt;

    debugLog = addLog(debugLog, "PAYLOAD OPTIMIZATION: Combined prompt length before escaping: " + combined.length);

    // OPTIMIZATION: Manual escaping instead of escapeForJSON to reduce encoding layers
    var escapedPrompt = combined
      .replace(/\\/g, '\\\\')    // Escape backslashes first
      .replace(/"/g, '\\"')      // Escape double quotes
      .replace(/\n/g, '\\n')     // Escape newlines
      .replace(/\r/g, '\\r')     // Escape carriage returns
      .replace(/\t/g, '\\t');    // Escape tabs

    // Create system prompt with table structure if available
    var sysPrompt = createSystemPrompt(tableStructure, apiCallCount, jsonData.previous_content, debugLog, requestType, jsonData.form_json);

    // Escape system prompt manually as well
    var escapedSystem = sysPrompt
      .replace(/\\/g, '\\\\')    // Escape backslashes first
      .replace(/"/g, '\\"')      // Escape double quotes
      .replace(/\n/g, '\\n')     // Escape newlines
      .replace(/\r/g, '\\r')     // Escape carriage returns
      .replace(/\t/g, '\\t');    // Escape tabs

    debugLog = addLog(debugLog, "PAYLOAD OPTIMIZATION: Combined prompt length after escaping: " + escapedPrompt.length);
    debugLog = addLog(debugLog, "PAYLOAD OPTIMIZATION: System prompt length after escaping: " + escapedSystem.length);

    // Check if the system prompt contains the table structure
    var containsTableStructure = sysPrompt.indexOf('TABLE STRUCTURE TO USE:') !== -1;
    debugLog = addLog(debugLog, "Single-call system prompt contains table structure: " + containsTableStructure);

    // Log the system prompt length
    debugLog = addLog(debugLog, "Single-call system prompt length: " + sysPrompt.length);

    // Check if the table structure is in the system prompt
    if (tableStructure && tableStructure.length > 0) {
      var tableStructureInPrompt = sysPrompt.indexOf(tableStructure.substring(0, 50)) !== -1;
      debugLog = addLog(debugLog, "Table structure found in single-call system prompt: " + tableStructureInPrompt);

      // For recursive calls, check if we're using the previous content as the table structure
      if (apiCallCount > 1 && jsonData.previous_content) {
        var previousContentInPrompt = sysPrompt.indexOf(String(jsonData.previous_content).substring(0, 50)) !== -1;
        debugLog = addLog(debugLog, "CRITICAL CHECK: Previous content found in single-call system prompt: " + previousContentInPrompt);

        if (!previousContentInPrompt) {
          debugLog = addLog(debugLog, "WARNING: Previous content not found in single-call system prompt for recursive call #" + apiCallCount);

          // Check if tableStructure is the same as previous_content
          if (tableStructure === String(jsonData.previous_content)) {
            debugLog = addLog(debugLog, "VERIFICATION: tableStructure is the same as previous_content in single-call");
          } else {
            debugLog = addLog(debugLog, "ERROR: tableStructure is NOT the same as previous_content in single-call");

            // Check the first 50 characters of both
            debugLog = addLog(debugLog, "tableStructure first 50 chars: " + tableStructure.substring(0, 50));
            debugLog = addLog(debugLog, "previous_content first 50 chars: " + String(jsonData.previous_content).substring(0, 50));
          }
        }
      }
    }

    // Build JSON manually to avoid over-escaping
    payloadString = '{';
    payloadString += '"model":"' + model + '",';
    payloadString += '"max_tokens":8192,';
    payloadString += '"system":"' + escapedSystem + '",';
    payloadString += '"temperature":' + temperature + ',';
    payloadString += '"top_p":0.7,';
    payloadString += '"messages":[{"role":"user","content":"' + escapedPrompt + '"}]';
    payloadString += '}';

    debugLog = addLog(debugLog, "PAYLOAD OPTIMIZATION: Manual payload created for htmlContent path, length: " + payloadString.length);
  } catch(e) {
    debugLog = addLog(debugLog, "PAYLOAD OPTIMIZATION: Error creating manual payload for htmlContent path: " + (e.message || "unknown"));
    try {
      var fallbackPrompt = "Error processing HTML content. Please respond with a simple HTML block.";
      // Use manual JSON construction for fallback as well
      payloadString = '{';
      payloadString += '"model":"' + model + '",';
      payloadString += '"max_tokens":8192,';
      payloadString += '"temperature":' + temperature + ',';
      payloadString += '"messages":[{"role":"user","content":"' + fallbackPrompt + '"}]';
      payloadString += '}';
      debugLog = addLog(debugLog, "PAYLOAD OPTIMIZATION: Fallback payload created manually for htmlContent path");
    } catch(fallbackErr) {
      response.error = "Failed to create payload";
      response.requestId = requestId;
      response.debugLog = debugLog;
      Platform.Response.Write(Platform.Function.Stringify(response));
      return;
    }
  }
} else {
  try {
    // Create system prompt with table structure if available
    debugLog = addLog(debugLog, "DEBUG: About to call createSystemPrompt with requestType: " + requestType);
    debugLog = addLog(debugLog, "DEBUG: jsonData.form_json being passed: " + (jsonData.form_json ? Platform.Function.Stringify(jsonData.form_json) : "null"));
    var sysPrompt2 = createSystemPrompt(tableStructure, jsonData.api_call_count, jsonData.previous_content, debugLog, requestType, jsonData.form_json);
    debugLog = addLog(debugLog, "DEBUG: createSystemPrompt returned, sysPrompt2 length: " + (sysPrompt2 ? sysPrompt2.length : 0));
    debugLog = addLog(debugLog, "DEBUG: sysPrompt2 first 200 chars: " + (sysPrompt2 ? sysPrompt2.substring(0, 200) : "null"));

    // OPTIMIZATION: Build payload manually to avoid excessive escaping
    debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Building payload manually to reduce encoding");
    debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Request type: " + requestType);
    debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Prompt length before escaping: " + prompt.length);

    // Log comparison between request types
    if (requestType === 'existing_template') {
      debugLog = addLog(debugLog, "✅ EXISTING_TEMPLATE ANALYSIS - Using elegant frontend prompt construction (like new_templates)");
    } else {
      debugLog = addLog(debugLog, "✅ NEW_TEMPLATES ANALYSIS - This is a new_templates request with normal-sized prompt");
    }

    // Escape only the essential characters for JSON
    var escapedPrompt = prompt
      .replace(/\\/g, '\\\\')    // Escape backslashes first
      .replace(/"/g, '\\"')      // Escape double quotes
      .replace(/\n/g, '\\n')     // Escape newlines
      .replace(/\r/g, '\\r')     // Escape carriage returns
      .replace(/\t/g, '\\t');    // Escape tabs

    var escapedSystem = sysPrompt2
      .replace(/\\/g, '\\\\')    // Escape backslashes first
      .replace(/"/g, '\\"')      // Escape double quotes
      .replace(/\n/g, '\\n')     // Escape newlines
      .replace(/\r/g, '\\r')     // Escape carriage returns
      .replace(/\t/g, '\\t');    // Escape tabs

    debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Prompt length after escaping: " + escapedPrompt.length);
    debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - System prompt length after escaping: " + escapedSystem.length);

    // Calculate total content size for comparison
    var totalContentSize = escapedPrompt.length + escapedSystem.length;
    debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Total content size (prompt + system): " + totalContentSize);

    // Build JSON string manually to avoid Platform.Function.Stringify over-escaping
    payloadString = '{';
    payloadString += '"model":"' + model + '",';
    payloadString += '"max_tokens":8192,';
    payloadString += '"system":"' + escapedSystem + '",';
    payloadString += '"temperature":' + temperature + ',';
    payloadString += '"top_p":0.7,';
    payloadString += '"messages":[{"role":"user","content":"' + escapedPrompt + '"}]';
    payloadString += '}';

    debugLog = addLog(debugLog, "🔍 PAYLOAD SIZE INVESTIGATION - Manual payload created, length: " + payloadString.length);

    // Final size analysis for investigation
    if (requestType === 'existing_template') {
      debugLog = addLog(debugLog, "✅ EXISTING_TEMPLATE FINAL SIZE - Payload: " + payloadString.length + " characters");
      debugLog = addLog(debugLog, "✅ EXISTING_TEMPLATE FINAL SIZE - Now using elegant frontend prompt construction");
    } else {
      debugLog = addLog(debugLog, "✅ NEW_TEMPLATES FINAL SIZE - Payload: " + payloadString.length + " characters");
      debugLog = addLog(debugLog, "✅ NEW_TEMPLATES FINAL SIZE - Normal size without HTML prepending");
    }
  } catch(payloadErr) {
    debugLog = addLog(debugLog, "PAYLOAD OPTIMIZATION: Payload creation error: " + (payloadErr.message || "unknown error"));
    debugLog = addLog(debugLog, "PAYLOAD OPTIMIZATION: sysPrompt2 length: " + (sysPrompt2 ? sysPrompt2.length : "null"));
    debugLog = addLog(debugLog, "PAYLOAD OPTIMIZATION: sysPrompt2 first 200 chars: " + (sysPrompt2 ? sysPrompt2.substring(0, 200) : "null"));
    try {
      var fallbackPrompt2 = "Error processing prompt. Please respond with a simple message.";
      // Use manual JSON construction for fallback as well
      payloadString = '{';
      payloadString += '"model":"' + model + '",';
      payloadString += '"max_tokens":8192,';
      payloadString += '"temperature":' + temperature + ',';
      payloadString += '"messages":[{"role":"user","content":"' + fallbackPrompt2 + '"}]';
      payloadString += '}';
      debugLog = addLog(debugLog, "PAYLOAD OPTIMIZATION: Fallback payload created manually");
    } catch(e) {
      response.error = "Failed to create payload";
      response.requestId = requestId;
      response.debugLog = debugLog;
      Platform.Response.Write(Platform.Function.Stringify(response));
      return;
    }
  }
}

try {
  debugLog = addLog(debugLog, "Sending request to Vertex Claude API (single-call fallback)");

  // Add URL parameters to help bypass caching
  var timestamp = new Date().getTime();
  var randomParam = Math.floor(Math.random() * 1000000);
  var endpoint = endpoint_url + "?t=" + timestamp + "&r=" + randomParam;

  var req = new Script.Util.HttpRequest(endpoint);
  req.emptyContentHandling = 0;
  req.retries = 2;
  req.continueOnError = true;
  req.method = "POST";
  req.contentType = "application/json;charset=UTF-8";

  // Set headers
  req.setHeader("Accept", "application/json");
  req.setHeader("Cache-Control", "no-cache");

  // Set Origin header to the allowed origin
  req.setHeader("Origin", ALLOWED_ORIGIN);

  // Set API key header
  req.setHeader("x-api-key", api_key);
  debugLog = addLog(debugLog, "Set x-api-key header");

  req.timeout = 60;
  debugLog = addLog(debugLog, "Set single-call request timeout to 60 seconds");

  req.postData = payloadString;

  // OPTIMIZATION: Comprehensive payload debugging before sending
  debugLog = addLog(debugLog, "🚀 FINAL PAYLOAD DEBUG - Request method: " + req.method);
  debugLog = addLog(debugLog, "🚀 FINAL PAYLOAD DEBUG - Payload size: " + payloadString.length + " characters");

  // CONSOLIDATED DEBUG LOG FOR EASY COPY/PASTE
  var consolidatedDebug = "=== PAYLOAD SIZE INVESTIGATION SUMMARY ===\n";
  consolidatedDebug += "Request Type: " + requestType + "\n";
  consolidatedDebug += "Raw Data Length: " + (rawData ? rawData.length : 0) + "\n";
  if (jsonData.existing_html) {
    consolidatedDebug += "Existing HTML Size: " + String(jsonData.existing_html).length + " characters (ignored - using frontend construction)\n";
  }
  if (jsonData.prompt) {
    consolidatedDebug += "Original Prompt Size: " + String(jsonData.prompt).length + " characters\n";
  }
  consolidatedDebug += "Final Prompt Size: " + prompt.length + " characters\n";
  consolidatedDebug += "System Prompt Size: " + (typeof sysPrompt2 !== 'undefined' ? sysPrompt2.length : 'N/A') + " characters\n";
  consolidatedDebug += "Final Payload Size: " + payloadString.length + " characters\n";
  if (requestType === 'existing_template') {
    consolidatedDebug += "ELEGANT FIX STATUS: ✅ Now using frontend prompt construction like new_templates\n";
    consolidatedDebug += "EXPECTED RESULT: Payload sizes should now be consistent between request types\n";
  }
  consolidatedDebug += "=== END SUMMARY ===";
  debugLog = addLog(debugLog, "📋 CONSOLIDATED DEBUG LOG:\n" + consolidatedDebug);

  // CRITICAL: Log payload samples WITHOUT additional escaping to see the actual content being sent
  debugLog = addLog(debugLog, "🚀 FINAL PAYLOAD DEBUG - ACTUAL payload first 1000 chars (no additional escaping):");
  debugLog = addLog(debugLog, payloadString.substring(0, 1000));
  debugLog = addLog(debugLog, "🚀 FINAL PAYLOAD DEBUG - ACTUAL payload last 500 chars (no additional escaping):");
  debugLog = addLog(debugLog, payloadString.substring(Math.max(0, payloadString.length - 500)));

  // Analyze encoding layers in the final payload
  var encodingAnalysis = {
    doubleQuotes: (payloadString.match(/\\"/g) || []).length,
    tripleBackslashes: (payloadString.match(/\\\\\\/g) || []).length,
    quadrupleBackslashes: (payloadString.match(/\\\\\\\\/g) || []).length,
    htmlEntities: (payloadString.match(/&[a-z]+;/g) || []).length,
    unicodeEscapes: (payloadString.match(/\\u[0-9a-fA-F]{4}/g) || []).length,
    totalBackslashes: (payloadString.match(/\\/g) || []).length,
    totalQuotes: (payloadString.match(/"/g) || []).length
  };
  debugLog = addLog(debugLog, "🚀 FINAL PAYLOAD DEBUG - Encoding analysis: " + Platform.Function.Stringify(encodingAnalysis));

  // Additional analysis to verify the payload is correctly constructed
  debugLog = addLog(debugLog, "🚀 FINAL PAYLOAD DEBUG - Payload starts with correct JSON: " + (payloadString.charAt(0) === '{' ? "YES" : "NO"));
  debugLog = addLog(debugLog, "🚀 FINAL PAYLOAD DEBUG - Payload ends with correct JSON: " + (payloadString.charAt(payloadString.length - 1) === '}' ? "YES" : "NO"));

  // Send the request
  var apiStartTime = new Date().getTime();
  debugLog = addLog(debugLog, "Sending request to API with Script.Util.HttpRequest");
  var resp = req.send();
  var apiEndTime = new Date().getTime();
  debugLog = addLog(debugLog, "API call took " + (apiEndTime - apiStartTime) + "ms");

  if (!resp) {
    debugLog = addLog(debugLog, "No response received");
    response.error = "No response received from API";
    response.requestId = requestId;
    response.debugLog = debugLog;
    Platform.Response.Write(Platform.Function.Stringify(response));
    return;
  }

  var statusCode = 0;
  try {
    statusCode = parseInt(resp.statusCode);
    if (isNaN(statusCode)) statusCode = 0;
  } catch(e) {
    statusCode = 0;
  }
  debugLog = addLog(debugLog, "API response status: " + statusCode);

  // Log error content if status is not 200
  if (statusCode !== 200 && resp.content) {
    debugLog = addLog(debugLog, "Raw error content: " + String(resp.content));
    response.error = "API returned status code " + statusCode;
    response.statusCode = statusCode;
    response.requestId = requestId;
    response.debugLog = debugLog;
    Platform.Response.Write(Platform.Function.Stringify(response));
    return;
  }

  if (statusCode === 200 && resp.content) {
    var respContent = String(resp.content);

    debugLog = addLog(debugLog, "Response length: " + respContent.length);

    var responseObj;
    try {
      // Log the first 200 characters of the response for debugging
      debugLog = addLog(debugLog, "Raw response first 200 chars: " + respContent.substring(0, 200));

      // Use eval() for ECMAScript 3 compatibility as in test_svca_endpoint.amp
      try {
        // Safely wrap the eval in parentheses
        responseObj = eval("(" + respContent + ")");
        debugLog = addLog(debugLog, "Response parsed with eval()");

        // Log the structure of the parsed response
        var parsedKeys = [];
        for (var parsedKey in responseObj) {
          if (responseObj.hasOwnProperty(parsedKey)) {
            parsedKeys.push(parsedKey);
          }
        }
        debugLog = addLog(debugLog, "Parsed response keys: " + parsedKeys.join(", "));

      } catch (evalErr) {
        debugLog = addLog(debugLog, "eval() parsing failed: " + evalErr.message);
        // Try Platform.Function.ParseJSON as fallback
        try {
          responseObj = Platform.Function.ParseJSON(respContent);
          debugLog = addLog(debugLog, "Response parsed with Platform.Function.ParseJSON");

          // Log the structure of the parsed response
          var parsedKeys2 = [];
          for (var parsedKey2 in responseObj) {
            if (responseObj.hasOwnProperty(parsedKey2)) {
              parsedKeys2.push(parsedKey2);
            }
          }
          debugLog = addLog(debugLog, "Parsed response keys (ParseJSON): " + parsedKeys2.join(", "));

        } catch (parseJsonErr) {
          throw new Error("Failed to parse response: " + parseJsonErr.message);
        }
      }
    } catch (parseErr) {
      debugLog = addLog(debugLog, "Failed to parse API response: " + parseErr.message);
      debugLog = addLog(debugLog, "Response content that failed to parse: " + respContent.substring(0, 500));
      response.error = "Failed to parse API response";
      response.requestId = requestId;
      response.debugLog = debugLog;
      Platform.Response.Write(Platform.Function.Stringify(response));
      return;
    }

    var generatedBase64Text = getClaudeText(responseObj);
    debugLog = addLog(debugLog, "Generated base64 text length: " + (generatedBase64Text ? generatedBase64Text.length : 0));

    // Additional debugging for content extraction issues
    if (!generatedBase64Text || generatedBase64Text.length < 1) {
      debugLog = addLog(debugLog, "No valid base64 content in response");

      // Log the full response object structure for debugging
      try {
        var responseStr = Platform.Function.Stringify(responseObj);
        debugLog = addLog(debugLog, "Full response object: " + responseStr.substring(0, 1000));
      } catch (stringifyErr) {
        debugLog = addLog(debugLog, "Failed to stringify response object: " + stringifyErr.message);
      }

      // Check if there's any content at all in the response
      if (responseObj && responseObj.content) {
        debugLog = addLog(debugLog, "Response has content property but extraction failed");
        if (responseObj.content.length) {
          debugLog = addLog(debugLog, "Content array length: " + responseObj.content.length);
          if (responseObj.content.length > 0) {
            try {
              var firstItem = responseObj.content[0];
              var firstItemStr = Platform.Function.Stringify(firstItem);
              debugLog = addLog(debugLog, "First content item: " + firstItemStr);
            } catch (itemErr) {
              debugLog = addLog(debugLog, "Failed to stringify first content item: " + itemErr.message);
            }
          }
        }
      } else {
        debugLog = addLog(debugLog, "Response has no content property");
      }

      response.error = "No valid base64 content in API response";
      response.requestId = requestId;
      response.debugLog = debugLog;
      Platform.Response.Write(Platform.Function.Stringify(response));
      return;
    }

    // Since the content is already base64-encoded from Claude, we don't need to process it
    // Just pass through the base64 content as-is
    var processedBase64Text = generatedBase64Text;

    try {
      // Log the base64 content for debugging (first 100 chars)
      debugLog = addLog(debugLog, "First 100 chars of base64 content: " + generatedBase64Text.substring(0, 100));
      debugLog = addLog(debugLog, "Base64 content is ready to pass through without processing");

      // Validate that the content looks like base64 (ECMAScript 3 compatible)
      var cleanBase64Text = generatedBase64Text.replace(/\s/g, '');
      var base64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
      var isValidBase64 = true;
      for (var i = 0; i < cleanBase64Text.length; i++) {
        if (base64Chars.indexOf(cleanBase64Text.charAt(i)) === -1) {
          isValidBase64 = false;
          break;
        }
      }
      debugLog = addLog(debugLog, "Base64 validation passed: " + isValidBase64);

      if (!isValidBase64) {
        debugLog = addLog(debugLog, "WARNING: Content does not appear to be valid base64");
        // Still proceed but log the warning
      }

    } catch(validationErr) {
      debugLog = addLog(debugLog, "Base64 validation failed: " + validationErr.message);
      // Still proceed with the content as-is
    }

    try {
      // Log the processed base64 text that will be returned
      debugLog = addLog(debugLog, "Final processed base64 text length: " + processedBase64Text.length);
      debugLog = addLog(debugLog, "Final processed base64 text first 100 chars: " + processedBase64Text.substring(0, 100));
      debugLog = addLog(debugLog, "Final processed base64 text last 100 chars: " + processedBase64Text.substring(Math.max(0, processedBase64Text.length - 100)));

      // Check if this is a recursive call
      if (jsonData.api_call_count) {
        debugLog = addLog(debugLog, "Completing recursive call #" + jsonData.api_call_count);
      } else {
        debugLog = addLog(debugLog, "Completing initial API call");
      }

      // The content is already base64-encoded from Claude, so we pass it through directly
      debugLog = addLog(debugLog, "Using base64 content directly from Claude API (no re-encoding needed)");
      var endTime = new Date().getTime();
      response.content = processedBase64Text;  // Use the base64 content directly
      response.requestId = requestId;
      response.processingTime = endTime - startTime;
      response.apiCallTime = apiEndTime - apiStartTime;
      response.debugLog = debugLog;

       // Log to de_llm_results data extension
      try {
        // Get content_name if available
        var content_name = "";
        if (jsonData && jsonData.friendly_name) {
          content_name = String(jsonData.friendly_name);
        }

        // Get user prompt if available
        var user_prompt = "";
        if (jsonData && jsonData.user_prompt) {
          user_prompt = String(jsonData.user_prompt);
          debugLog = addLog(debugLog, "User prompt found: " + user_prompt);
        } else {
          debugLog = addLog(debugLog, "No user prompt found in payload");
        }

        // Get goal if available
        var goal = "";
        if (jsonData && jsonData.goal) {
          goal = String(jsonData.goal);
          debugLog = addLog(debugLog, "Goal found: " + goal);
        } else {
          debugLog = addLog(debugLog, "No goal found in payload");
        }

        // Log the API call details to the data extension
        // Decode the base64 content for storage in the data extension
        var decodedContentForLogging = "";
        try {
          decodedContentForLogging = Platform.Function.Base64Decode(processedBase64Text);
          debugLog = addLog(debugLog, "Successfully decoded base64 content for logging");
        } catch (decodeErr) {
          debugLog = addLog(debugLog, "Failed to decode base64 content for logging: " + decodeErr.message);
          decodedContentForLogging = "Failed to decode base64 content: " + decodeErr.message;
        }

        var logResult = logToLLMResultsDE(
          content_id,
          content_name,
          emailName,
          payloadString,
          sysPrompt,
          decodedContentForLogging,
          requestId,
          "Success",
          Platform.Function.Stringify(debugLog),
          user_prompt,
          goal,
          form_json,
          requestType
        );
        debugLog = addLog(debugLog, logResult);
        response.debugLog = debugLog;
      } catch(logError) {
        debugLog = addLog(debugLog, "Error logging to DE: " + (logError.message || "unknown error"));
        response.debugLog = debugLog;
      }

      Platform.Response.Write(Platform.Function.Stringify(response));
      return;
    } catch(encodeErr) {
      debugLog = addLog(debugLog, "Failed to encode content");
      response.error = "Failed to encode content";
      response.requestId = requestId;
      response.debugLog = debugLog;
      Platform.Response.Write(Platform.Function.Stringify(response));
      return;
    }
  } else {
    debugLog = addLog(debugLog, "Error response from API");
    var errorType = "Unknown";
    var errorDetails = "No details available";
    if (resp.content) {
      try {
        var errorObj = Platform.Function.ParseJSON(String(resp.content));
        if (errorObj && errorObj.error) {
          errorType = errorObj.error.type || "API Error";
          errorDetails = errorObj.error.message || "No error message";
        } else {
          errorDetails = Platform.Function.Stringify(resp.content);
        }
      } catch(parseErr) {
        errorDetails = Platform.Function.Stringify(resp.content);
      }
    }
    if (statusCode === 400) {
      var tmpStr = String(resp.content);
      if (tmpStr.indexOf("token_limit_exceeded") !== -1) {
        errorType = "Token limit exceeded";
      } else if (tmpStr.indexOf("invalid_json") !== -1) {
        errorType = "Invalid JSON";
      }
    } else if (statusCode === 429) {
      errorType = "Rate limit exceeded";
    } else if (statusCode >= 500) {
      errorType = "Service error";
    }
    debugLog = addLog(debugLog, "Error type: " + errorType);

    response.error = "Claude API Error: " + errorType;
    response.status = statusCode;
    response.details = errorDetails;
    response.requestId = requestId;
    response.debugLog = debugLog;

    // Log the failed API call to the data extension
    try {
      // Get content_name if available
      var content_name = "";
      if (jsonData && jsonData.friendly_name) {
        content_name = String(jsonData.friendly_name);
      }

      // Get user prompt if available
      var user_prompt = "";
      if (jsonData && jsonData.user_prompt) {
        user_prompt = String(jsonData.user_prompt);
        debugLog = addLog(debugLog, "User prompt found: " + user_prompt);
      } else {
        debugLog = addLog(debugLog, "No user prompt found in payload");
      }

      // Get goal if available
      var goal = "";
      if (jsonData && jsonData.goal) {
        goal = String(jsonData.goal);
        debugLog = addLog(debugLog, "Goal found: " + goal);
      } else {
        debugLog = addLog(debugLog, "No goal found in payload");
      }

      // Log the error details as the result
      var errorResult = "ERROR: " + errorType + " - " + errorDetails;

      var logResult = logToLLMResultsDE(
        content_id,
        content_name,
        emailName,
        payloadString,
        sysPrompt,
        errorResult,
        requestId,
        "Error",
        Platform.Function.Stringify(debugLog),
        user_prompt,
        goal,
        form_json,
        requestType
      );
      debugLog = addLog(debugLog, logResult);
      response.debugLog = debugLog;
    } catch(logError) {
      debugLog = addLog(debugLog, "Error logging failed API call to DE: " + (logError.message || "unknown error"));
      response.debugLog = debugLog;
    }

    Platform.Response.Write(Platform.Function.Stringify(response));
    return;
  }
} catch (e) {
  var errorMsg = e.message || "Unknown error";
  debugLog = addLog(debugLog, "Exception: " + errorMsg);
  response.error = "Exception during API call";
  response.details = errorMsg;
  response.requestId = requestId;
  response.debugLog = debugLog;

  // Log the exception to the data extension
  try {
    // Get content_name if available
    var content_name = "";
    if (jsonData && jsonData.friendly_name) {
      content_name = String(jsonData.friendly_name);
    }

    // Get user prompt if available
    var user_prompt = "";
    if (jsonData && jsonData.user_prompt) {
      user_prompt = String(jsonData.user_prompt);
      debugLog = addLog(debugLog, "User prompt found: " + user_prompt);
    } else {
      debugLog = addLog(debugLog, "No user prompt found in payload");
    }

    // Get goal if available
    var goal = "";
    if (jsonData && jsonData.goal) {
      goal = String(jsonData.goal);
      debugLog = addLog(debugLog, "Goal found: " + goal);
    } else {
      debugLog = addLog(debugLog, "No goal found in payload");
    }

    // Log the exception details as the result
    var exceptionResult = "EXCEPTION: " + errorMsg;

    var logResult = logToLLMResultsDE(
      content_id,
      content_name,
      emailName,
      payloadString || "",
      sysPrompt,
      exceptionResult,
      requestId,
      "Exception",
      Platform.Function.Stringify(debugLog),
      user_prompt,
      goal,
      form_json,
      requestType
    );
    debugLog = addLog(debugLog, logResult);
    response.debugLog = debugLog;
  } catch(logError) {
    debugLog = addLog(debugLog, "Error logging exception to DE: " + (logError.message || "unknown error"));
    response.debugLog = debugLog;
  }

  Platform.Response.Write(Platform.Function.Stringify(response));
  return;
}

</script>