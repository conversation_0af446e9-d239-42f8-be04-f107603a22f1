<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Preview Radio Button Styling Test</title>
    <link rel="stylesheet" href="cmp_ai_editor.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            padding: 2rem;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
        }
        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }
        .button-group {
            margin-top: 1rem;
        }
        .button-group button {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            padding: 0.5rem 1rem;
            border: 1px solid #007bff;
            background: #007bff;
            color: white;
            border-radius: 0.25rem;
            cursor: pointer;
        }
        .button-group button:hover {
            background: #0056b3;
        }
        .status {
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.9rem;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Content Preview Radio Button Styling Test</h1>
        <p>This page demonstrates the new AI styling for the Content Preview radio button when Edit with AI is triggered.</p>
        
        <div class="test-section">
            <h3>Content Source Selection</h3>
            <div class="form-group mb1vw">
                <label class="form-label-1a2b form-label-margin">
                    <input type="radio" class="radio-input-1a2b radio-input-margin" name="content-source" value="none" checked/>
                    New
                </label>
                <label class="form-label-1a2b form-label-margin">
                    <input type="radio" class="radio-input-1a2b radio-input-margin" name="content-source" disabled value="selection"/>
                    Content Preview
                </label>
            </div>
            
            <div class="button-group">
                <button onclick="simulateEditWithAI()">Simulate "Edit with AI" Trigger</button>
                <button onclick="simulateDisabledState()">Simulate Disabled State</button>
                <button onclick="resetStyling()">Reset Styling</button>
            </div>
            
            <div id="status" class="status" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>Features Demonstrated</h3>
            <ul>
                <li><strong>AI Gradient Border:</strong> Animated rainbow border when AI editing is active</li>
                <li><strong>Pulsing Dot Indicator:</strong> Animated dot with AI colors showing active state</li>
                <li><strong>Disabled State Tooltip:</strong> Hover over disabled radio button to see explanation</li>
                <li><strong>Automatic Styling:</strong> Applied when "Edit with AI" is triggered</li>
                <li><strong>Clean Removal:</strong> Styling removed when switching to other options</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>CSS Classes Applied</h3>
            <ul>
                <li><code>.ai-content-preview-active</code> - Main AI styling class</li>
                <li><code>.content-preview-disabled</code> - Disabled state with tooltip</li>
                <li>Uses same gradient animation as AI buttons (<code>aiGradientBorder</code>)</li>
                <li>Pulse animation similar to Settings tab indicator</li>
            </ul>
        </div>
    </div>

    <script>
        function simulateEditWithAI() {
            // Simulate the Edit with AI trigger
            var $radio = $("input[name='content-source'][value='selection']");
            var $label = $radio.closest('label');
            
            // Enable and select the radio button
            $radio.prop("disabled", false).prop("checked", true);
            
            // Add AI styling
            $label.removeClass('content-preview-disabled')
                   .removeAttr('data-tooltip')
                   .addClass('ai-content-preview-active');
            
            // Remove styling from other labels
            $("input[name='content-source']").not($radio).each(function() {
                $(this).closest('label').removeClass('ai-content-preview-active');
            });
            
            showStatus('AI styling applied! The Content Preview option now shows the animated gradient border and pulsing dot indicator.', 'success');
        }
        
        function simulateDisabledState() {
            var $radio = $("input[name='content-source'][value='selection']");
            var $label = $radio.closest('label');
            
            // Disable the radio button and add tooltip
            $radio.prop("disabled", true).prop("checked", false);
            $label.removeClass('ai-content-preview-active')
                   .addClass('content-preview-disabled')
                   .attr('data-tooltip', 'Right-click on content in the Content Preview and select "Edit with AI" to enable this option');
            
            // Select the "New" option instead
            $("input[name='content-source'][value='none']").prop("checked", true);
            
            showStatus('Disabled state applied! Hover over the Content Preview option to see the tooltip explanation.', 'info');
        }
        
        function resetStyling() {
            // Remove all custom styling
            $("input[name='content-source']").each(function() {
                var $label = $(this).closest('label');
                $label.removeClass('ai-content-preview-active content-preview-disabled')
                       .removeAttr('data-tooltip');
            });
            
            // Reset to default state
            $("input[name='content-source'][value='selection']").prop("disabled", true).prop("checked", false);
            $("input[name='content-source'][value='none']").prop("checked", true);
            
            showStatus('Styling reset to default state.', 'info');
        }
        
        function showStatus(message, type) {
            var $status = $('#status');
            $status.removeClass('success info')
                   .addClass(type)
                   .text(message)
                   .show();
            
            // Hide after 5 seconds
            setTimeout(function() {
                $status.fadeOut();
            }, 5000);
        }
        
        // Initialize with disabled state on page load
        $(document).ready(function() {
            simulateDisabledState();
        });
    </script>
</body>
</html>
