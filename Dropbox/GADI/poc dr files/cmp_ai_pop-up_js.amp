<script>

/* ==================== Global Variables Start ==================== */

var currentClaudeRequest = null;
var currentPrompt = null;
var hasGeneratedContent = false;
var isCancelled = false;
var visitedSteps = [];
let currentStep =  1;
const totalSteps = 3;
const state = { source: null, goals: [] };
var buttonsDisabledByEmptyBlockName = [];
var goalSpecifications = {
  "web_traffic": "Include multiple hyperlinks that strategically encourage readers to click through to the website. Ensure links are contextually relevant and naturally integrated into the content. IMPORTANT: Do not modify the table structure in any way.",
  "excl_content": "Craft content that provides unique insights not found elsewhere. Reimagine the information from a fresh, subscriber-only perspective. Ensure the content feels specially curated and adds distinct value beyond standard public information. IMPORTANT: Do not modify the table structure in any way.",
  "marketing": "Directly and tastefully highlight relevant products or upcoming events within the content. Integrate promotional elements seamlessly without appearing overly sales-oriented. IMPORTANT: Do not modify the table structure in any way.",
  "fix_outlook": "Ensure compatibility with Outlook email clients by using inline CSS, explicit width attributes, and including older HTML attributes like cellspacing, cellpadding, and border. Avoid floating elements, background images, and complex CSS selectors that Outlook doesn't support. IMPORTANT: Do not modify the table structure in any way.",
  "reduce_size": "Optimize content for smaller file sizes by using compressed images, minimizing HTML code, removing unnecessary styling, and keeping content concise while maintaining the intended design and messaging. IMPORTANT: Do not modify the table structure in any way.",
  "optimise_for_esps": "Implement universally compatible design practices that ensure consistent rendering across all major email clients. Use basic HTML elements and universally supported attributes. IMPORTANT: Do not modify the table structure in any way.",
  "beautify": "Enhance the visual appeal with balanced layouts, appropriate white space, complementary color schemes, and visually engaging elements. Apply consistent typography and thoughtful image placement while maintaining readability and professionalism. Ignore changing border radius settings. IMPORTANT: Do not modify the table structure in any way or change the td padding from 15px to something else.",
  "on_brand": "Meticulously adhere to brand guidelines including correct logo usage, specified color palettes, typography standards, and established voice/tone. Ensure all messaging aligns with brand positioning and values. IMPORTANT: Do not modify the table structure in any way.",
  "responsive": "Ensure the content is responsive for mobile devices while maintaining the provided table structure. IMPORTANT: Do not modify the table structure in any way. The table structure already includes mobile responsiveness through its design."
};

// User-friendly goal names for display
var goalDisplayNames = {
  "web_traffic": "Web Traffic",
  "excl_content": "Exclusive Content",
  "marketing": "Marketing Focus",
  "fix_outlook": "Outlook Compatibility",
  "reduce_size": "Size Optimization",
  "optimise_for_esps": "ESP Optimization",
  "beautify": "Visual Enhancement",
  "on_brand": "Brand Consistency",
  "responsive": "Mobile Responsiveness"
};

// Progress tracking variables
var currentProgress = 0;
var totalProgressSteps = 0;
var completedSteps = 0;
var currentModelAttempts = [];
var currentStepName = "";
var stepRetryCount = {}; // Track retry count per step

/* ==================== Global Variables End ==================== */


/* ==================== Utility Functions Start ==================== */

function base64DecodeUnicode(str) {
  return decodeURIComponent(atob(str).split('').map(function(c) {
    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
  }).join(''));
}

// OPTIMIZATION: Convert brand CSS JSON to plain text to eliminate escaping issues
function convertBrandCssToPlainText(brandCssObj) {
  if (!brandCssObj || typeof brandCssObj !== 'object') {
    return 'No brand styling specified';
  }

  var plainText = '';

  function processStyleObject(obj, prefix = '') {
    for (var key in obj) {
      if (obj.hasOwnProperty(key)) {
        var value = obj[key];
        if (typeof value === 'object' && value !== null) {
          plainText += prefix + key.toUpperCase() + ' STYLES:\n';
          processStyleObject(value, prefix + '  ');
        } else {
          plainText += prefix + '- ' + key.replace(/-/g, ' ') + ': ' + value + '\n';
        }
      }
    }
  }

  processStyleObject(brandCssObj);
  return plainText.trim();
}

function extractStep2FormValues() {
  var formValues = {};
  formValues.fromSentence = $("textarea[name='fromSentence']").val() || '';
  formValues.columns = $("input[name='columns']").val() || '1';
  formValues.rows = $("input[name='rows']").val() || '1';
  formValues.brandCssStyle = $("select[name='brandCssStyle']").val() || '';
  formValues.contentSource = $("input[name='content-source']:checked").val() || 'none';
  formValues.capiId = $("select[name='capi_id']").val() || '';
  formValues.cbId = $("select[name='cb_id']").val() || '';
  formValues.creativityLevel = $("input[name='creativityLevel']").val() || '70';
  var selectedGoals = [];
  $('.goal-checkbox:checked').each(function() {
    selectedGoals.push($(this).val());
  });
  formValues.goals = selectedGoals;
  formValues.otherGoalDescription = $("textarea[name='otherGoalDescription']").val() || '';
  formValues.stackContentVertical = $("#stack_content_vertical").is(":checked");
  formValues.extraSpacingInElements = $("#extra_spacing_in_elements").is(":checked");
  formValues.aiModel = $("select[name='aiModel']").val() || 'claude-3-5-haiku@20241022';
  return formValues;
}

function base64DecodeUTF8(base64) {
  try {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return new TextDecoder('utf-8').decode(bytes);
  } catch (e) {
    console.error("Error in base64DecodeUTF8:", e);
    return base64DecodeUnicode(base64);
  }
}

function base64DecodeUnicode(str) {
  try {
    return decodeURIComponent(atob(str).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
    }).join(''));
  } catch (e) {
    console.error("Error in base64DecodeUnicode:", e);
    return atob(str);
  }
}

function sanitizeUTF8(str) {
  if (!str) return str;
  let result = "";
  for (let i = 0; i < str.length; i++) {
    const code = str.charCodeAt(i);
    if (code >= 0xD800 && code <= 0xDBFF) {
      if (i + 1 < str.length) {
        const next = str.charCodeAt(i + 1);
        if (next >= 0xDC00 && next <= 0xDFFF) {
          result += str[i] + str[i + 1];
          i++;
          continue;
        }
      }
      result += "\uFFFD";
    } else if (code >= 0xDC00 && code <= 0xDFFF) {
      result += "\uFFFD";
    } else {
      result += str[i];
    }
  }
  return result;
}

function prepareJsonSafePrompt(promptObj) {
  try {
    if (typeof promptObj.prompt === 'string') {
      promptObj.prompt = sanitizeUTF8(promptObj.prompt);
    }
    const jsonString = JSON.stringify(promptObj);
    JSON.parse(jsonString); 
    return jsonString;
  } catch (e) {
    console.error("JSON preparation error:", e);
    const safeObj = {
      prompt: typeof promptObj.prompt === 'string' ?
        promptObj.prompt.replace(/[^\x20-\x7E\t\n\r]/g, '') :
        "Error: Unable to process prompt text",
      temperature: promptObj.temperature || 0.7,
      model: promptObj.model || "claude-3-5-haiku@20241022"
    };
    if (promptObj.capi_id) safeObj.capi_id = String(promptObj.capi_id);
    if (promptObj.cb_id) safeObj.cb_id = String(promptObj.cb_id);
    if (promptObj.content_source) safeObj.content_source = String(promptObj.content_source);
    return JSON.stringify(safeObj);
  }
}

/* ==================== Utility Functions End ==================== */


/* ==================== Progress and Model Management Functions Start ==================== */

// Helper function to abbreviate model names
function abbreviateModelName(modelName) {
  var abbreviations = {
    'claude-3-7-sonnet@20250219': 'Sonnet 3.7',
    'claude-3-5-sonnet-v2@20241022': 'Sonnet 3.5v2',
    'claude-3-5-sonnet@20240620': 'Sonnet 3.5',
    'claude-3-5-haiku@20241022': 'Haiku 3.5',
    'claude-3-haiku@20240307': 'Haiku 3.0',
    'claude-3-opus@20240229': 'Opus 3.0'
  };
  return abbreviations[modelName] || modelName;
}

function initializeProgress(selectedGoals) {
  // Calculate total steps: base prompt + (check & fix only if goals exist) + selected goals
  if (selectedGoals.length === 0) {
    totalProgressSteps = 1; // Only base content generation
  } else {
    totalProgressSteps = 2 + selectedGoals.length; // base + check&fix + goals
  }
  completedSteps = 0;
  currentProgress = 0;
  currentModelAttempts = [];
  stepRetryCount = {}; // Reset retry tracking

  updateProgressDisplay();
  clearModelList();
}

function updateProgressDisplay() {
  var percentage = totalProgressSteps > 0 ? Math.round((completedSteps / totalProgressSteps) * 100) : 0;
  currentProgress = percentage;

  // Update progress circle with smooth animation
  var progressCircle = document.querySelector('.ai-progress-circle');
  var progressText = document.querySelector('.ai-progress-text');

  if (progressCircle && progressText) {
    var degrees = (percentage / 100) * 360;

    // Create smooth gradient transition
    progressCircle.style.background = `conic-gradient(
      from 0deg,
      #007bff 0deg,
      #007bff ${degrees}deg,
      #e9ecef ${degrees}deg,
      #e9ecef 360deg
    )`;

    // Show step numbers instead of percentage
    var stepText = `${completedSteps}/${totalProgressSteps}`;

    // Animate the text change
    progressText.style.transform = 'scale(1.1)';
    setTimeout(function() {
      progressText.textContent = stepText;
      progressText.style.transform = 'scale(1)';
    }, 150);

    // Add completion effect when all steps done
    if (completedSteps >= totalProgressSteps) {
      progressCircle.style.background = `conic-gradient(
        from 0deg,
        #28a745 0deg,
        #28a745 360deg
      )`;
      progressText.style.color = '#28a745';
      progressText.style.fontWeight = 'bold';
    }
  }
}

function addModelAttempt(modelName, status, goalName) {
  // status can be: 'processing', 'success', 'error'
  var modelList = document.getElementById('ai-model-list');
  if (!modelList) return;

  // Initialize retry count for this step if not exists
  if (!stepRetryCount[goalName]) {
    stepRetryCount[goalName] = 0;
  }

  // Determine if this is a retry (any attempt after the first)
  var isRetry = stepRetryCount[goalName] > 0;

  // Create model entry
  var entry = document.createElement('div');
  entry.className = 'ai-model-entry ' + status;

  var abbreviatedModel = abbreviateModelName(modelName);

  // Get user-friendly goal name with special handling for system goals
  var friendlyGoalName;
  if (goalDisplayNames[goalName]) {
    friendlyGoalName = goalDisplayNames[goalName];
  } else if (goalName === 'base_content') {
    friendlyGoalName = 'Base Content';
  } else if (goalName === 'check_fix') {
    friendlyGoalName = 'Content Optimization';
  } else if (goalName === 'single_generation') {
    friendlyGoalName = 'Content Generation';
  } else {
    friendlyGoalName = goalName || 'content';
  }

  var actionText = isRetry ? 'Retrying' : 'Using';
  var statusText = '';
  var displayText = '';

  if (status === 'processing') {
    displayText = `${actionText} goal <strong>"${friendlyGoalName}"</strong> with <strong>${abbreviatedModel}</strong>`;
  } else if (status === 'success') {
    statusText = ` - ${friendlyGoalName} (success)`;
    displayText = `${actionText} ${abbreviatedModel}${statusText}`;
  } else if (status === 'error') {
    statusText = ' (error)';
    displayText = `${actionText} ${abbreviatedModel}${statusText}`;
  }

  entry.innerHTML = `
    <span class="ai-model-name">${displayText}</span>
    <span class="ai-model-status ${status}"></span>
  `;

  // Add to the beginning of the list for upward movement
  modelList.insertBefore(entry, modelList.firstChild);
  currentModelAttempts.unshift({
    element: entry,
    status: status,
    model: modelName,
    goalName: goalName,
    isRetry: isRetry,
    stepNumber: completedSteps + 1
  });

  // Increment retry count for this step
  stepRetryCount[goalName]++;

  // Animate list if more than 3 entries
  if (currentModelAttempts.length > 3) {
    animateModelList();
  }
}

function updateModelStatus(modelName, newStatus) {
  var attempt = currentModelAttempts.find(a => a.model === modelName && a.status === 'processing');
  if (attempt) {
    attempt.status = newStatus;
    attempt.element.className = 'ai-model-entry ' + newStatus;

    var abbreviatedModel = abbreviateModelName(modelName);
    var friendlyGoalName = goalDisplayNames[attempt.goalName] || attempt.goalName || 'content';

    // Handle special goal names
    if (attempt.goalName === 'base_content') {
      friendlyGoalName = 'Base Content';
    } else if (attempt.goalName === 'check_fix') {
      friendlyGoalName = 'Content Optimization';
    } else if (attempt.goalName === 'single_generation') {
      friendlyGoalName = 'Content Generation';
    }

    // Update the text with final status summary
    var displayText = '';

    if (newStatus === 'success') {
      displayText = `${attempt.stepNumber}. Generated content for "${friendlyGoalName}" with ${abbreviatedModel}`;
    } else if (newStatus === 'error') {
      displayText = `${attempt.stepNumber}. Failed to generate content for "${friendlyGoalName}" with ${abbreviatedModel}`;
    }

    var modelNameSpan = attempt.element.querySelector('.ai-model-name');
    if (modelNameSpan) {
      modelNameSpan.textContent = displayText;
    }

    var statusIndicator = attempt.element.querySelector('.ai-model-status');
    if (statusIndicator) {
      statusIndicator.className = 'ai-model-status ' + newStatus;
    }
  }
}

function animateModelList() {
  // No need to remove entries - let the container handle scrolling
  // The CSS max-height and overflow-y will handle showing only 3 rows
}

function clearModelList() {
  var modelList = document.getElementById('ai-model-list');
  if (modelList) {
    modelList.innerHTML = '';
    modelList.style.transform = 'translateY(0)';
  }
  currentModelAttempts = [];
}

function updateStatusMessage(message) {
  var statusElement = document.getElementById('loading-message');
  if (statusElement) {
    statusElement.textContent = message;
  }
}

// Removed updateCurrentGoalDisplay - goal is now part of status message

function incrementProgress(stepName) {
  completedSteps++;
  currentStepName = stepName;
  updateProgressDisplay();

  // Update status message with user-friendly names
  var friendlyStepName = stepName;
  if (goalDisplayNames[stepName]) {
    friendlyStepName = goalDisplayNames[stepName];
  } else if (stepName === 'base_content') {
    friendlyStepName = 'Base Content';
  } else if (stepName === 'check_fix') {
    friendlyStepName = 'Content Optimization';
  } else if (stepName === 'single_generation') {
    friendlyStepName = 'Content Generation';
  }

  var statusMsg = `✓ ${friendlyStepName}`;
  if (completedSteps < totalProgressSteps) {
    var remaining = totalProgressSteps - completedSteps;
    statusMsg += ` (${remaining} step${remaining !== 1 ? 's' : ''} remaining)`;
  } else {
    statusMsg = "🎉 All content generated successfully!";
  }

  updateStatusMessage(statusMsg);
}

/* ==================== Progress and Model Management Functions End ==================== */


/* ==================== API Calls Start ==================== */

var apiCallCount = 0; 
function isLastModelHaiku(modelToUse, index, fallbackModels) {
  // Return true if current model is Haiku 3.5 - this means stop trying other models when Haiku fails
  return modelToUse === "claude-3-5-haiku@20241022";
}

function hasPriorResults(outputs) {
  // Check if there are any successful outputs (non-error content) in the outputs array
  if (!outputs || outputs.length === 0) return false;

  // Look for outputs that don't start with "Error on" which indicates successful content
  return outputs.some(output =>
    output &&
    typeof output === 'string' &&
    output.trim() !== '' &&
    !output.startsWith('Error on')
  );
}

function callClaude(userPrompt) {
  isCancelled = false;
  userPrompt = preprocessPrompt(userPrompt);
  var originalModel = $("select[name='aiModel']").val();
  var fallbackModels = [];
  if (originalModel === "claude-3-7-sonnet@20250219") {
    fallbackModels = [
      "claude-3-7-sonnet@20250219",
      "claude-3-5-sonnet-v2@20241022",
      "claude-3-5-sonnet@20240620",
      "claude-3-5-haiku@20241022"
    ];
  } else if (originalModel === "claude-3-5-haiku@20241022") {
    fallbackModels = [
      "claude-3-5-haiku@20241022"
    ];
  } else if (originalModel === "claude-3-5-sonnet-v2@20241022") {
    fallbackModels = [
      "claude-3-5-sonnet-v2@20241022",
      "claude-3-5-sonnet@20240620",
      "claude-3-5-haiku@20241022"
    ];
  } else if (originalModel === "claude-3-5-sonnet@20240620") {
    fallbackModels = [
      "claude-3-5-sonnet@20240620",
      "claude-3-5-haiku@20241022"
    ];
  } else {
    fallbackModels = [originalModel];
  }
  return new Promise(function(resolve, reject) {
    function attempt(index) {
      if (isCancelled) {
        console.log("Request was cancelled - stopping further attempts");
        reject(new Error("Request cancelled"));
        return;
      }
      if (index >= fallbackModels.length) {
        reject(new Error("All Claude model attempts failed."));
        return;
      }
      var modelToUse = fallbackModels[index];
      // Don't check isLastModelHaiku here - let the API call happen first
      apiCallCount++;
      if (apiCallCount == 0) {
        console.log(`API call count: ${apiCallCount} - Calling Claude API with prompt: ${userPrompt} using model: ${modelToUse}`);
      } else {
        console.log(`API call count: ${apiCallCount} - Retrying with model: ${modelToUse}`);
      }
      currentPrompt = userPrompt;

      // Add model attempt to the list
      addModelAttempt(modelToUse, 'processing', currentStepName);

      // Create status message based on current step
      var friendlyStepName = currentStepName;
      if (goalDisplayNames[currentStepName]) {
        friendlyStepName = goalDisplayNames[currentStepName];
      } else if (currentStepName === 'base_content') {
        friendlyStepName = 'Base Content';
      } else if (currentStepName === 'check_fix') {
        friendlyStepName = 'Content Optimization';
      } else if (currentStepName === 'single_generation') {
        friendlyStepName = 'Content Generation';
      }

      updateStatusMessage(`🤖 Processing step "${friendlyStepName}" with ${abbreviateModelName(modelToUse)}...`);

      $("#loading-state").show();
      $("#error-state").hide();
      var cloudPageURL = 'https://cloud.e.newsdigitalmedia.com.au/dr_%%=v(@appStateEnvironment)=%%_ai_pop-up_api?sys_env=%%=v(@environmentAppCentre)=%%&c=%%=v(@cookie)=%%&feature_branch=%%=v(@feature_branch)=%%&biz_env=%%=v(@appStateEnvironment)=%%&content_id=%%=v(@de_eca_EventID)=%%';
      console.log('Making request to:', cloudPageURL);
      var sanitizedPrompt = sanitizeUTF8(userPrompt);
      var contentSource = $("input[name='content-source']:checked").val();
      var cType = 'application/json;charset=UTF-8';
      const numRows = parseInt($("input[name='rows']").val()) || 0;
      const numColumns = parseInt($("input[name='columns']").val()) || 0;
      let tableStructure = "";
      // Skip table structure generation for selection mode (existing_template)
      if (contentSource !== 'selection' && numRows > 0 && numColumns > 0) {
        const stackVertical = $("#stack_content_vertical").is(":checked");
        tableStructure = generateTableStructure(numRows, numColumns, stackVertical);
        // console.log("Generated table structure for API call:", tableStructure);
      }
      var userInputPrompt = $("textarea[name='fromSentence']").val().trim();
      console.log("User prompt from fromSentence field:", userInputPrompt);
      var formValues = extractStep2FormValues();
      console.log("Extracted form values:", formValues);

      // Determine request type based on content source
      var requestType = 'new_templates';
      var requestData = {
        type: requestType,
        prompt: sanitizedPrompt,
        user_prompt: userInputPrompt,
        temperature: $("input[name='creativityLevel']").val(),
        model: modelToUse,
        capi_id: $("select[name='capi_id']").val(),
        cb_id: $("select[name='cb_id']").val(),
        content_source: contentSource,
        table_structure: tableStructure,
        form_json: formValues
      };

      // Handle existing template content for AI editing
      if (contentSource === 'selection' && window.selectedForAIEditingElement) {
        // Validate that the selected element is still in the DOM and highlighted
        const isElementInDOM = document.contains(window.selectedForAIEditingElement);
        const hasActiveHighlight = document.querySelector('.content-wrapper .ai-highlight-wrapper');



        if (!isElementInDOM || !hasActiveHighlight) {
          console.error("Selected element is no longer valid or highlighted");
          show_client_side_message('Selected element is no longer valid. Please select an element again.', 'error');
          // Reset to "New" mode
          $("input[name='content-source'][value='none']").prop('checked', true).trigger('change');
          return;
        }

        requestType = 'existing_template';
        requestData.type = requestType;

        // Get the current content from the DOM (not from the stored reference)
        const currentElement = hasActiveHighlight ? hasActiveHighlight.firstElementChild : window.selectedForAIEditingElement;
        var existingHtml = currentElement.outerHTML || '';

        // REVERT TO ORIGINAL WORKING METHOD: Send HTML as separate parameter
        requestData.existing_html = existingHtml;

        console.log("🔍 PAYLOAD DEBUG - Including current HTML content for AI editing");
        console.log("🔍 PAYLOAD DEBUG - Original HTML length:", existingHtml.length);
        console.log("🔍 PAYLOAD DEBUG - Content preview:", existingHtml.substring(0, 200) + "...");
        console.log("🔍 PAYLOAD DEBUG - HTML encoding check - contains quotes:", existingHtml.includes('"'));
        console.log("🔍 PAYLOAD DEBUG - HTML encoding check - contains backslashes:", existingHtml.includes('\\'));

        // Update the reference to point to the current element
        window.selectedForAIEditingElement = currentElement;
      }

      // OPTIMIZATION: Add comprehensive payload debugging
      const payloadString = JSON.stringify(requestData);
      console.log("🚀 PAYLOAD DEBUG - Final request payload:");
      console.log("🚀 PAYLOAD DEBUG - Payload size:", payloadString.length, "characters");
      console.log("🚀 PAYLOAD DEBUG - Payload preview (first 500 chars):", payloadString.substring(0, 500));
      console.log("🚀 PAYLOAD DEBUG - Payload preview (last 500 chars):", payloadString.substring(Math.max(0, payloadString.length - 500)));

      // Check for encoding issues in the payload
      const encodingIssues = {
        doubleQuotes: (payloadString.match(/\\"/g) || []).length,
        tripleBackslashes: (payloadString.match(/\\\\\\/g) || []).length,
        quadrupleBackslashes: (payloadString.match(/\\\\\\\\/g) || []).length,
        htmlEntities: (payloadString.match(/&[a-z]+;/g) || []).length
      };
      console.log("🚀 PAYLOAD DEBUG - Encoding analysis:", encodingIssues);

      currentClaudeRequest = $.ajax({
        url: cloudPageURL,
        type: 'POST',
        data: payloadString,
        contentType: cType,
        dataType: 'text',
        success: function(rawResponse) {
          console.log('Successful raw response received');
          console.log('Response length:', rawResponse ? rawResponse.length : 0);
          currentClaudeRequest = null;
          if (!rawResponse || rawResponse.trim() === '') {
            console.error('Empty response received from server using model ' + modelToUse);
            isCancelled = false;
            $("#loading-state").hide();
            $("#error-state").show();
            if (isLastModelHaiku(modelToUse, index, fallbackModels)) {
              console.error("Haiku 3.5 model failed. Aborting API call.");
              $("#modal-error-message").text("The AI couldn't process the template. This may be because the instructions are too complex, vague, or confusing, or too many rows were selected, or if 'Edit with AI' was used on a content area that is too large. Please retry with a simpler or clearer prompt, or a prompt with fewer rows, or a smaller content area when 'Edit with AI' is active. Alternatively, you can generate a new prompt from scratch with AI using the 'Generate a prompt' button located on top of the prompt text area in the top left corner of the screen.");
              reject(new Error("Haiku 3.5 model failed. API call aborted."));
              return;
            }
            // Update model status to error
            updateModelStatus(modelToUse, 'error');

            var nextModel = fallbackModels[index+1];
            if (nextModel) {
              var friendlyStepName = currentStepName;
              if (goalDisplayNames[currentStepName]) {
                friendlyStepName = goalDisplayNames[currentStepName];
              } else if (currentStepName === 'base_content') {
                friendlyStepName = 'Base Content';
              } else if (currentStepName === 'check_fix') {
                friendlyStepName = 'Content Optimization';
              } else if (currentStepName === 'single_generation') {
                friendlyStepName = 'Content Generation';
              }
              updateStatusMessage(`🤖 Processing step "${friendlyStepName}" with ${abbreviateModelName(nextModel)}...`);
              $("#modal-error-message").text('Empty response using model ' + modelToUse + '. Retrying with model: ' + nextModel + '.');
              attempt(index + 1);
            } else {
              $("#modal-error-message").text('Empty response using model ' + modelToUse + '. No further models available.');
              reject(new Error('Empty response. No further models available.'));
            }
            return;
          }
          try {
            var data = JSON.parse(rawResponse);
            console.log('Parsed response:', data);
            console.log('Raw response from server:', rawResponse);
            if (data.form_message && data.message_color === 'danger') {
              console.error('Access error detected:', data.form_message);
              console.log('Raw data object:', data);
              var cleanErrorMessage = data.form_message;
              if (cleanErrorMessage.includes('<strong>null</strong>')) {
                cleanErrorMessage = cleanErrorMessage.replace('<strong>null</strong>', 'your account');
              }
              console.log('Cleaned error message:', cleanErrorMessage);
              $("#loading-state").hide();
              $("#error-state").show();
              $("#modal-error-message").html(cleanErrorMessage); 
              console.log("About to call showRefreshButton() for access error");
              showRefreshButton();
              console.log("showRefreshButton() called for access error");
              $("#loadingModal-1a2b").modal('show');
              console.log('About to reject with error:', cleanErrorMessage);
              reject(new Error(cleanErrorMessage));
              return;
            }
            if ((data.error === 'Session expired' && data.message) ||
                (data.message && data.message.includes('session has expired'))) {
              console.error('Session expired error detected:', data.message || data.error);
              var sessionErrorMessage = data.message || data.error;
              $("#loading-state").hide();
              $("#error-state").show();
              $("#modal-error-message").text(sessionErrorMessage);
              console.log("About to call showRefreshButton() for session error");
              showRefreshButton();
              console.log("showRefreshButton() called for session error");
              $("#loadingModal-1a2b").modal('show');
              reject(new Error(sessionErrorMessage));
              return;
            }
            if (data.debugLog) {
              console.log('Debug Log from API response:');
              console.log(data.debugLog);
            } else {
              console.log('No debugLog found in API response');
            }
            if (data.content) {
              console.log('Content found in API response, attempting to decode...');
              let decodedContent;
              try {
                decodedContent = base64DecodeUTF8(data.content);
                console.log('Successfully decoded content with UTF-8 decoder');
              } catch (decodeError) {
                console.warn('UTF-8 decode failed, falling back to Unicode decoder:', decodeError);
                try {
                  decodedContent = base64DecodeUnicode(data.content);
                  console.log('Successfully decoded content with Unicode decoder');
                } catch (unicodeError) {
                  console.error('Both decoders failed:', unicodeError);
                  decodedContent = "DECODING ERROR: " + unicodeError.message;
                }
              }
              console.log('Decoded HTML Content:');
              console.log(decodedContent);
            } else {
              console.log('No content field found in API response');
            }
            if (data.error && data.status === 529) {
              console.warn('Claude API is overloaded (Status 529) for model ' + modelToUse, data);
              isCancelled = false;
              $("#loading-state").hide();
              $("#error-state").show();
              if (isLastModelHaiku(modelToUse, index, fallbackModels)) {
                console.error("Haiku 3.5 model failed. Aborting API call.");
                $("#modal-error-message").text("The AI couldn't process the template. This may be because the instructions are too complex, vague, or confusing, or too many rows were selected, or if 'Edit with AI' was used on a content area that is too large. Please retry with a simpler or clearer prompt, or a prompt with fewer rows, or a smaller content area when 'Edit with AI' is active. Alternatively, you can generate a new prompt from scratch with AI using the 'Generate a prompt' button located on top of the prompt text area in the top left corner of the screen.");
                reject(new Error("Haiku 3.5 model failed. API call aborted."));
                return;
              }
              // Update model status to error
              updateModelStatus(modelToUse, 'error');

              var nextModel = fallbackModels[index+1];
              if (nextModel) {
                var friendlyStepName = currentStepName;
                if (goalDisplayNames[currentStepName]) {
                  friendlyStepName = goalDisplayNames[currentStepName];
                } else if (currentStepName === 'base_content') {
                  friendlyStepName = 'Base Content';
                } else if (currentStepName === 'check_fix') {
                  friendlyStepName = 'Content Optimization';
                } else if (currentStepName === 'single_generation') {
                  friendlyStepName = 'Content Generation';
                }
                updateStatusMessage(`🤖 Processing step "${friendlyStepName}" with ${abbreviateModelName(nextModel)}...`);
                $("#modal-error-message").text('Claude API is overloaded using model ' + modelToUse + '. Retrying with model: ' + nextModel + '.');
                attempt(index + 1);
              } else {
                $("#modal-error-message").text('Claude API is overloaded using model ' + modelToUse + '. No further models available.');
                reject(new Error('Claude API is overloaded. No further models available.'));
              }
              return;
            }
            if (data.error) {
              console.error('Error from server using model ' + modelToUse + ':', data.error);
              isCancelled = false;
              $("#loading-state").hide();
              $("#error-state").show();
              if (isLastModelHaiku(modelToUse, index, fallbackModels)) {
                console.error("Haiku 3.5 model failed. Aborting API call.");
                $("#modal-error-message").text("The AI couldn't process the template. This may be because the instructions are too complex, vague, or confusing, or too many rows were selected, or if 'Edit with AI' was used on a content area that is too large. Please retry with a simpler or clearer prompt, or a prompt with fewer rows, or a smaller content area when 'Edit with AI' is active. Alternatively, you can generate a new prompt from scratch with AI using the 'Generate a prompt' button located on top of the prompt text area in the top left corner of the screen.");
                reject(new Error("Haiku 3.5 model failed. API call aborted."));
                return;
              }
              // Update model status to error
              updateModelStatus(modelToUse, 'error');

              var nextModel = fallbackModels[index+1];
              if (nextModel) {
                var friendlyStepName = currentStepName;
                if (goalDisplayNames[currentStepName]) {
                  friendlyStepName = goalDisplayNames[currentStepName];
                } else if (currentStepName === 'base_content') {
                  friendlyStepName = 'Base Content';
                } else if (currentStepName === 'check_fix') {
                  friendlyStepName = 'Content Optimization';
                } else if (currentStepName === 'single_generation') {
                  friendlyStepName = 'Content Generation';
                }
                updateStatusMessage(`🤖 Processing step "${friendlyStepName}" with ${abbreviateModelName(nextModel)}...`);
                $("#modal-error-message").text(`Claude API Error using model ${modelToUse}: ${data.error}. Retrying with model: ${nextModel}.`);
                attempt(index + 1);
              } else {
                $("#modal-error-message").text(`Claude API Error using model ${modelToUse}: ${data.error}. No further models available.`);
                reject(new Error(`Claude API Error: ${data.error}. No further models available.`));
              }
              return;
            }
            if (!data.content) {
              console.error('No content field in response for model ' + modelToUse + ':', data);
              isCancelled = false;
              $("#loading-state").hide();
              $("#error-state").show();
              if (isLastModelHaiku(modelToUse, index, fallbackModels)) {
                console.error("Haiku 3.5 model failed. Aborting API call.");
                $("#modal-error-message").text("The AI couldn't process the template. This may be because the instructions are too complex, vague, or confusing, or too many rows were selected, or if 'Edit with AI' was used on a content area that is too large. Please retry with a simpler or clearer prompt, or a prompt with fewer rows, or a smaller content area when 'Edit with AI' is active. Alternatively, you can generate a new prompt from scratch with AI using the 'Generate a prompt' button located on top of the prompt text area in the top left corner of the screen.");
                reject(new Error("Haiku 3.5 model failed. API call aborted."));
                return;
              }
              // Update model status to error
              updateModelStatus(modelToUse, 'error');

              var nextModel = fallbackModels[index+1];
              if (nextModel) {
                var friendlyStepName = currentStepName;
                if (goalDisplayNames[currentStepName]) {
                  friendlyStepName = goalDisplayNames[currentStepName];
                } else if (currentStepName === 'base_content') {
                  friendlyStepName = 'Base Content';
                } else if (currentStepName === 'check_fix') {
                  friendlyStepName = 'Content Optimization';
                } else if (currentStepName === 'single_generation') {
                  friendlyStepName = 'Content Generation';
                }
                updateStatusMessage(`🤖 Processing step "${friendlyStepName}" with ${abbreviateModelName(nextModel)}...`);
                $("#modal-error-message").text('No content received from Claude using model ' + modelToUse + '. Retrying with model: ' + nextModel + '.');
                attempt(index + 1);
              } else {
                $("#modal-error-message").text('No content received from Claude using model ' + modelToUse + '. No further models available.');
                reject(new Error('No content received from Claude. No further models available.'));
              }
              return;
            }
            let decodedContent;
            try {
              console.log('Attempting to decode base64 content');
              try {
                console.log('Attempting UTF-8 decode of base64 content');
                decodedContent = base64DecodeUTF8(data.content);
                console.log('Successfully decoded content as UTF-8');
              } catch (unicodeError) {
                console.error('Unicode decode error:', unicodeError);
                decodedContent = base64DecodeUnicode(data.content);
                console.log('Falling back to legacy decode method');
              }
              console.log('Successfully decoded content');
              decodedContent = sanitizeUTF8(decodedContent);
              console.log('Final decoded HTML content:');
              console.log(decodedContent);
              $("select[name='aiModel']").val(originalModel);

              // Update model status to success
              updateModelStatus(modelToUse, 'success');

              resolve(decodedContent);
            } catch (decodeError) {
              console.error('Base64 decode error:', decodeError);
              isCancelled = false;
              $("#loading-state").hide();
              $("#error-state").show();
              if (isLastModelHaiku(modelToUse, index, fallbackModels)) {
                console.error("Haiku 3.5 model failed. Aborting API call.");
                $("#modal-error-message").text("The AI couldn't process the template. This may be because the instructions are too complex, vague, or confusing, or too many rows were selected, or if 'Edit with AI' was used on a content area that is too large. Please retry with a simpler or clearer prompt, or a prompt with fewer rows, or a smaller content area when 'Edit with AI' is active. Alternatively, you can generate a new prompt from scratch with AI using the 'Generate a prompt' button located on top of the prompt text area in the top left corner of the screen.");
                reject(new Error("Haiku 3.5 model failed. API call aborted."));
                return;
              }
              var nextModel = fallbackModels[index+1];
              if (nextModel) {
                $("#modal-error-message").text('Failed to decode response content using model ' + modelToUse + '. Retrying with model: ' + nextModel + '.');
                attempt(index + 1);
              } else {
                $("#modal-error-message").text('Failed to decode response content using model ' + modelToUse + '. No further models available.');
                reject(new Error('Failed to decode response content. No further models available.'));
              }
              return;
            }
            $("select[name='aiModel']").val(originalModel);
          } catch (parseError) {
            console.error('JSON parse error for model ' + modelToUse + ':', parseError);
            console.error('Raw response that caused parse error:', rawResponse);
            currentClaudeRequest = null;
            isCancelled = false;
            $("#loading-state").hide();
            $("#error-state").show();
            if (isLastModelHaiku(modelToUse, index, fallbackModels)) {
              console.error("Haiku 3.5 model failed. Aborting API call.");
              $("#modal-error-message").text("The AI couldn't process the template. This may be because the instructions are too complex, vague, or confusing, or too many rows were selected, or if 'Edit with AI' was used on a content area that is too large. Please retry with a simpler or clearer prompt, or a prompt with fewer rows, or a smaller content area when 'Edit with AI' is active. Alternatively, you can generate a new prompt from scratch with AI using the 'Generate a prompt' button located on top of the prompt text area in the top left corner of the screen.");
              reject(new Error("Haiku 3.5 model failed. API call aborted."));
              return;
            }
            var nextModel = fallbackModels[index+1];
            if (nextModel) {
              $("#modal-error-message").text(`Invalid JSON returned by Claude endpoint using model ${modelToUse}: ${parseError.message}. Retrying with model: ${nextModel}.`);
              attempt(index + 1);
            } else {
              $("#modal-error-message").text(`Invalid JSON returned by Claude endpoint using model ${modelToUse}: ${parseError.message}. No further models available.`);
              reject(new Error(`JSON parse error: ${parseError.message}. No further models available.`));
            }
          }
        },
        error: function(xhr, status, error) {
          console.error('AJAX error using model ' + modelToUse + ':', status, error);
          console.error('XHR response:', xhr.responseText);
          currentClaudeRequest = null;
          if (status === 'abort') {
            console.log('Request was canceled by user');
            reject(new Error('Request canceled'));
            isCancelled = true;
            return;
          }
          isCancelled = false;
          if (isLastModelHaiku(modelToUse, index, fallbackModels)) {
            console.error("Haiku 3.5 model failed. Aborting API call.");
            $("#loading-state").hide();
            $("#error-state").show();
            $("#modal-error-message").text("The AI couldn't process the template. This may be because the instructions are too complex, vague, or confusing, or too many rows were selected, or if 'Edit with AI' was used on a content area that is too large. Please retry with a simpler or clearer prompt, or a prompt with fewer rows, or a smaller content area when 'Edit with AI' is active. Alternatively, you can generate a new prompt from scratch with AI using the 'Generate a prompt' button located on top of the prompt text area in the top left corner of the screen.");
            reject(new Error("Haiku 3.5 model failed. API call aborted."));
            return;
          }
          var nextModel = fallbackModels[index+1];
          if (nextModel) {
            console.log(`Falling back to next model: ${nextModel}`);
            $("#loading-state").hide();
            $("#error-state").show();
            let errorMessage = '';
            if (xhr.status === 0) {
              errorMessage = `The AI couldn't process the template. This may be because the instructions are too complex, vague, or confusing, or too many rows were selected. Retrying with model: ${nextModel}.`;
            } else if (xhr.status >= 500) {
              errorMessage = `Server error (${xhr.status}) with model ${modelToUse}. Retrying with model: ${nextModel}.`;
            } else if (xhr.status >= 400) {
              errorMessage = `Request error (${xhr.status}) with model ${modelToUse}. Retrying with model: ${nextModel}.`;
            } else {
              errorMessage = `Unknown error using model ${modelToUse}: ${error}. Retrying with model: ${nextModel}.`;
            }
            $("#modal-error-message").text(errorMessage);
            attempt(index + 1);
          } else {
            $("#loading-state").hide();
            $("#error-state").show();
            if (xhr.status === 0) {
              $("#modal-error-message").text("The AI couldn't process the template. This may be because the instructions are too complex, vague, or confusing, or too many rows were selected, or if 'Edit with AI' was used on a content area that is too large. Please retry with a simpler or clearer prompt, or a prompt with fewer rows, or a smaller content area when 'Edit with AI' is active. Alternatively, you can generate a new prompt from scratch with AI using the 'Generate a prompt' button located on top of the prompt text area in the top left corner of the screen.");
              reject(new Error("The AI couldn't process the template. Please retry with a simpler or clearer prompt, or a prompt with fewer rows."));
            } else {
              $("#modal-error-message").text(`Error using model ${modelToUse}: ${error}. No further models available.`);
              reject(new Error(`AJAX Error using model ${modelToUse}: ${error}. No further models available.`));
            }
          }
        }
      });
    }
    attempt(0);
  });
}

/* ==================== API Calls End ==================== */


/* ==================== Helper Functions for Error Modal Buttons ==================== */

function showNormalErrorButtons() {
  $("#retry-api-btn").hide();
  $("#close-api-btn").show();
  $("#refresh-page-btn").hide();
}

function showRefreshButton() {
  console.log("showRefreshButton() called");
  $("#refresh-page-btn").remove();
  $("#retry-api-btn").hide();
  $("#close-api-btn").hide();
  console.log("Retry and close buttons hidden");
  var buttonContainer = $("#error-state .mt-3");
  if (buttonContainer.length > 0) {
    buttonContainer.html('<button type="button" id="refresh-page-btn" class="btn btn-primary" tabindex="0">Refresh Page</button>');
    console.log("Refresh button created in existing button container");
  } else {
    $("#error-state").append('<div class="mt-3"><button type="button" id="refresh-page-btn" class="btn btn-primary" tabindex="0">Refresh Page</button></div>');
    console.log("Refresh button created in new button container");
  }
  $("#refresh-page-btn").show().css({
    'display': 'inline-block',
    'visibility': 'visible',
    'opacity': '1'
  });
  setTimeout(function() {
    var refreshBtn = $("#refresh-page-btn");
    console.log("Refresh button final check - exists:", refreshBtn.length > 0, "visible:", refreshBtn.is(":visible"));
    if (refreshBtn.length > 0 && !refreshBtn.is(":visible")) {
      console.log("Button exists but not visible - forcing visibility");
      refreshBtn.show().css('display', 'inline-block');
    }
  }, 100);
}

/* ==================== Helper Function for Chaining Goals ==================== */

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Helper function to build dynamic class strings based on checkbox states
function buildDynamicClasses() {
  const stackVertical = $("#stack_content_vertical").is(":checked");
  const extraSpacing = $("#extra_spacing_in_elements").is(":checked");

  const forceRowClass = stackVertical ? "force-row" : "";
  const columnClass = extraSpacing ? "column" : "display_block";

  return {
    forceRow: forceRowClass,
    column: columnClass,
    containerForceRow: stackVertical ? "container force-row" : "container"
  };
}

function generateTableStructure(rows, columns, stackVertical) {
  rows = parseInt(rows) || 1;
  columns = parseInt(columns) || 1;
  const outerTableWidth = 650;
  const innerTableWidth = 610;

  // Get dynamic classes based on checkbox states
  const classes = buildDynamicClasses();

  // Define width sets: with column class vs with display_block class
  const widthSets = {
    column: { 1: 610, 2: 305, 3: 203 },
    display_block: { 1: 590, 2: 285, 3: 183 }
  };

  let columnWidth = classes.column === "column" ? widthSets.column[columns] : widthSets.display_block[columns];
  // Fallback for more than 3 columns
  if (!columnWidth) {
    let baseWidth = Math.floor(innerTableWidth / columns);
    columnWidth = classes.column === "column" ? baseWidth : baseWidth - 20;
  }

  let tableHtml = `<table border="0" align="center" cellpadding="0" width="${outerTableWidth}" cellspacing="0"${classes.forceRow ? ` class="${classes.forceRow}"` : ''} style="max-width: ${outerTableWidth}px; width: 100%;" bgcolor="#FFFFFF">\n`;
  tableHtml += '  <tr>\n';
  tableHtml += `    <td align="center" valign="top"${classes.forceRow ? ` class="${classes.forceRow}"` : ''} style="padding: 20px;" width="${innerTableWidth}">\n`;
  for (let r = 0; r < rows; r++) {
    tableHtml += `      <table width="${innerTableWidth}" border="0" cellpadding="0" cellspacing="0"${classes.forceRow ? ` class="${classes.forceRow}"` : ''} style="max-width: ${innerTableWidth}px; width: 100%;">\n`;
    tableHtml += '        <tr>\n';
    for (let c = 0; c < columns; c++) {
      tableHtml += `          <td class="${classes.column}" align="left" valign="top" width="${columnWidth}" style="max-width: ${columnWidth}px; width: ${columnWidth}px;">\n`;
      tableHtml += `          <table width="100%" border="0" cellpadding="15" cellspacing="0"${classes.containerForceRow ? ` class="${classes.containerForceRow}"` : ''}><tbody><tr><td style="padding: 15px;">`;
      tableHtml += '            [Placeholder content]\n';
      tableHtml += '          </td></tr></table>'
      tableHtml += '          </td>\n';
    }
    tableHtml += '        </tr>\n';
    tableHtml += '      </table>\n';
    if (r < rows - 1) {
      tableHtml += '\n';
    }
  }
  tableHtml += '    </td>\n';
  tableHtml += '  </tr>\n';
  tableHtml += '</table>';
  return tableHtml;
}

function displayGeneratedContent() {
  if (!window.generatedContent) {
    console.error("No generated content available");
    return;
  }
  console.log("Displaying generated content in preview container only (not updating editor)");
  const { finalResultObj, selectedGoals } = window.generatedContent;
  let tabNavHtml = '<ul class="nav nav-tabs" id="outputTabs" role="tablist">';
  tabNavHtml += `<li class="nav-item">
    <a class="nav-link active" id="tab-step0" data-toggle="tab" href="#output-step0" role="tab" aria-controls="output-step0" aria-selected="true">Base Prompt</a>
  </li>`;
  if (finalResultObj.outputs.length > 1) {
    tabNavHtml += `<li class="nav-item">
      <a class="nav-link" id="tab-step1" data-toggle="tab" href="#output-step1" role="tab" aria-controls="output-step1" aria-selected="false">Check &amp; Fix</a>
    </li>`;
  }
  for (let i = 0; i < selectedGoals.length; i++) {
    let tabIndex = i + 2;
    let tabName = "Prompt Chain: " + selectedGoals[i];
    // Check if this tab has an error by looking at the corresponding output
    let hasError = false;
    if (tabIndex < finalResultObj.outputs.length) {
      const output = finalResultObj.outputs[tabIndex];
      hasError = output && output.startsWith('Error on goal');
    }
    // Add error indicator to tab name if there's an error
    const tabDisplayName = hasError ? `${tabName} ⚠️` : tabName;
    tabNavHtml += `<li class="nav-item">
      <a class="nav-link ${hasError ? 'text-warning' : ''}" id="tab-step${tabIndex}" data-toggle="tab" href="#output-step${tabIndex}" role="tab" aria-controls="output-step${tabIndex}" aria-selected="false">${tabDisplayName}</a>
    </li>`;
  }
  tabNavHtml += '</ul>';
  $(".tab-navigation-wrapper").remove(); 
  $("#content-preview-container").before('<div class="tab-navigation-wrapper">' + tabNavHtml + '</div>');
  let tabContentHtml = '<div class="tab-content" id="outputTabsContent">';
  let content0 = $.trim(finalResultObj.outputs[0] || "");
  tabContentHtml += `<div class="tab-pane fade show active" id="output-step0" role="tabpanel" aria-labelledby="tab-step0">
    ${content0}
  </div>`;
  if (finalResultObj.outputs.length > 1) {
    let content1 = $.trim(finalResultObj.outputs[1] || "");
    tabContentHtml += `<div class="tab-pane fade" id="output-step1" role="tabpanel" aria-labelledby="tab-step1">
      ${content1}
    </div>`;
  }
  for (let i = 0; i < selectedGoals.length; i++) {
    let tabIndex = i + 2;
    let content = "";
    if (tabIndex < finalResultObj.outputs.length) {
      const rawContent = finalResultObj.outputs[tabIndex] || "No output available.";
      // Check if this is an error message
      if (rawContent.startsWith('Error on goal')) {
        // Style error messages nicely
        content = `<div class="alert alert-warning" role="alert">
          <h6 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Goal Processing Failed</h6>
          <p class="mb-0">${rawContent}</p>
          <hr>
          <p class="mb-0 small text-muted">The previous steps completed successfully and their results are available in the other tabs.</p>
        </div>`;
      } else {
        content = $.trim(rawContent);
      }
    } else {
      content = "No output available for this prompt chain.";
    }
    tabContentHtml += `<div class="tab-pane fade" id="output-step${tabIndex}" role="tabpanel" aria-labelledby="tab-step${tabIndex}">
      ${content}
    </div>`;
  }
  tabContentHtml += '</div>';
  $("#content-preview-container").html(tabContentHtml);
  console.log("Content preview container updated, but contentPreview div is NOT updated");
}

function getTableStructure() {
  const rows = parseInt($("input[name='rows']").val()) || 1;
  const columns = parseInt($("input[name='columns']").val()) || 1;
  const stackVertical = $("#stack_content_vertical").is(":checked");
  return generateTableStructure(rows, columns, stackVertical);
}
async function checkAndFixOutput(initialOutput) {
  console.log(`Starting Check & Fix step for content source: ${$("input[name='content-source']:checked").val()}`);
  var contentSource = $("input[name='content-source']:checked").val();

  // Skip table structure validation for selection mode - preserve original content structure
  if (contentSource === 'selection') {
    console.log('Selection mode detected - using content preservation logic instead of table structure validation');
    var fixPrompt = initialOutput + "\n\n" +
        "**IMPORTANT:** The output must include every `<img>` and `<a>` element exactly as in the original HTML. " +
        "Do not remove or alter any image URLs, hyperlink URLs, or text copy.";
    try {
      let fixedOutput = await callClaude(fixPrompt);
      return (fixedOutput && fixedOutput.trim() !== "") ? fixedOutput : initialOutput;
    } catch(err) {
      // If this is a Haiku 3.5 error, throw it to show the error modal
      if (err.message && (err.message.includes("Haiku 3.5 model failed") || err.message.includes("The AI couldn't process the template"))) {
        throw err;
      }
      $("#error-message-container")
         .html("An error occurred while checking/fixing content preservation: " + err.message)
         .show();
      return initialOutput;
    }
  }

  var numRows = $("input[name='rows']").val();
  var numColumns = $("input[name='columns']").val();
  var stackVertical = $("#stack_content_vertical").is(":checked");
  if (numColumns && numRows) {
    const tableStructure = getTableStructure();
    const cellPadding = 20;
    const outerTableWidth = 650;
    const innerTableWidth = 610;

    // Use the same width sets as generateTableStructure
    const classes = buildDynamicClasses();
    const widthSets = {
      column: { 1: 610, 2: 305, 3: 203 },
      display_block: { 1: 590, 2: 285, 3: 183 }
    };

    let columnWidth = classes.column === "column" ? widthSets.column[numColumns] : widthSets.display_block[numColumns];
    if (!columnWidth) {
      let baseWidth = Math.floor(innerTableWidth / numColumns);
      columnWidth = classes.column === "column" ? baseWidth : baseWidth - 20;
    }
    let fixPrompt = "CURRENT HTML TABLE STRUCTURE:\n" +
      initialOutput + "\n\n" +
      "STRUCTURE REQUIREMENTS:\n" +
      "1. The table MUST have EXACTLY " + numRows + " rows and EXACTLY " + numColumns + " columns\n" +
      "2. You MUST maintain the exact nesting structure: outer table > outer td > inner table > tr > column td\n" +
      "3. Column widths MUST be calculated by dividing 610 by the number of columns\n" +
      "4. For this table with " + numColumns + " columns, each column width should be " + columnWidth + "px\n\n" +
      "IMPORTANT REQUIREMENTS:\n" +
      "1. Use EXACTLY the HTML table structure provided above\n" +
      "2. Do NOT create a new table structure\n" +
      "3. Maintain all HTML tags, attributes, classes, and styles\n" +
      "4. You CAN update the content inside the table cells\n" +
      "5. You CAN add additional CSS properties as long as width and max-width values remain unchanged\n" +
      "6. Return ONLY the corrected HTML table without any commentary or explanations";
    try {
      let fixedOutput = await callClaude(fixPrompt);
      return (fixedOutput && fixedOutput.trim() !== "") ? fixedOutput : initialOutput;
    } catch(err) {
      // If this is a Haiku 3.5 error, throw it to show the error modal
      if (err.message && (err.message.includes("Haiku 3.5 model failed") || err.message.includes("The AI couldn't process the template"))) {
        throw err;
      }
      $("#error-message-container")
         .html("An error occurred while checking/fixing the table layout: " + err.message)
         .show();
      return initialOutput;
    }
  }
  else if (contentSource === 'capi' || contentSource === 'existing') {
    var fixPrompt = initialOutput + "\n\n" +
        "**IMPORTANT:** The output must include every `<img>` and `<a>` element exactly as in the original HTML. " +
        "Do not remove or alter any image URLs, hyperlink URLs, or text copy.";
    try {
      let fixedOutput = await callClaude(fixPrompt);
      return (fixedOutput && fixedOutput.trim() !== "") ? fixedOutput : initialOutput;
    } catch(err) {
      // If this is a Haiku 3.5 error, throw it to show the error modal
      if (err.message && (err.message.includes("Haiku 3.5 model failed") || err.message.includes("The AI couldn't process the template"))) {
        throw err;
      }
      $("#error-message-container")
         .html("An error occurred while checking/fixing content preservation: " + err.message)
         .show();
      return initialOutput;
    }
  }
  return initialOutput;
}
async function callClaudeForAllGoals(basePrompt, goals) {
  let outputs = [];
  let baseOutput;
  try {
    if (isCancelled) return { outputs: outputs };
    currentStepName = "base_content";
    updateStatusMessage("🚀 Creating your base content...");
    baseOutput = await callClaude(basePrompt);
    baseOutput = extractTableHtml(baseOutput);
    outputs.push(baseOutput);
    incrementProgress("base_content");
  } catch (err) {
    // If this is a Haiku 3.5 error, throw it immediately to show the error modal
    if (err.message && (err.message.includes("Haiku 3.5 model failed") || err.message.includes("The AI couldn't process the template"))) {
      throw err;
    }
    outputs.push("Error on base prompt: " + err.message);
    baseOutput = "";
    incrementProgress("base_content");
    if (isCancelled) return { outputs: outputs };
  }
  let fixedOutput;
  try {
    currentStepName = "check_fix";
    updateStatusMessage("🔧 Optimizing and refining content...");
    fixedOutput = await checkAndFixOutput(baseOutput);
    fixedOutput = extractTableHtml(fixedOutput);
    outputs.push(fixedOutput);
    incrementProgress("check_fix");
    isCancelled = false;
  } catch(err) {
    // If this is a Haiku 3.5 error, throw it immediately to show the error modal
    if (err.message && (err.message.includes("Haiku 3.5 model failed") || err.message.includes("The AI couldn't process the template"))) {
      throw err;
    }
    outputs.push("Error on check & fix: " + err.message);
    fixedOutput = baseOutput;
    incrementProgress("check_fix");
    if (isCancelled) return { outputs: outputs };
  }
  const uniqueGoals = [...new Set(goals)];
  for (let i = 0; i < uniqueGoals.length; i++) {
    if (isCancelled) return { outputs: outputs };
    const currentGoal = uniqueGoals[i];
    currentStepName = currentGoal;
    const friendlyGoalName = goalDisplayNames[currentGoal] || currentGoal;
    updateStatusMessage(`✨ Applying ${friendlyGoalName}...`);
    const goalSpec = goalSpecifications[currentGoal] || currentGoal;
    let goalPrompt = "CURRENT HTML TABLE STRUCTURE:\n" +
                     fixedOutput +
                     "\n\nGOAL INSTRUCTION:\n" +
                     "Update the content within the table according to the following goal: " + goalSpec;
    if (currentGoal === 'on_brand') {
      const brandCssStyleStr = $("select[name='brandCssStyle']").val();
      if (brandCssStyleStr && brandCssStyleStr !== 'Optional') {
        // OPTIMIZATION: Convert brand CSS JSON to plain text to eliminate escaping issues
        try {
          const brandCssObj = JSON.parse(brandCssStyleStr);
          const plainTextBrandCss = convertBrandCssToPlainText(brandCssObj);
          goalPrompt += "\n\nBRAND STYLING SPECIFICATIONS:\n" + plainTextBrandCss;
          console.log('🚀 BRAND CSS DEBUG - Converted brand CSS to plain text for on_brand goal');
          console.log('🚀 BRAND CSS DEBUG - Original JSON length:', brandCssStyleStr.length);
          console.log('🚀 BRAND CSS DEBUG - Plain text length:', plainTextBrandCss.length);
          console.log('🚀 BRAND CSS DEBUG - Size reduction:', (brandCssStyleStr.length - plainTextBrandCss.length), 'characters');
        } catch (parseError) {
          // Fallback to original string if parsing fails
          goalPrompt += "\n\nBRAND STYLING SPECIFICATIONS:\n" + brandCssStyleStr;
          console.log('🚀 BRAND CSS DEBUG - Failed to parse brand CSS, using original string:', parseError.message);
        }
      }
    }
    goalPrompt += "\n\nIMPORTANT REQUIREMENTS:\n" +
                  "1. Use EXACTLY the HTML table structure provided above\n" +
                  "2. Do NOT create a new table structure\n" +
                  "3. Maintain all HTML tags, attributes, classes, and styles\n" +
                  "4. You CAN update the content inside the table cells\n" +
                  "5. You CAN add additional CSS properties as long as width and max-width values remain unchanged\n" +
                  "6. Return ONLY the complete HTML table without any commentary or explanations";
    try {
      let goalOutput = await callClaude(goalPrompt);
      goalOutput = extractTableHtml(goalOutput);
      outputs.push(goalOutput);
      fixedOutput = goalOutput;
      incrementProgress(currentGoal);
    } catch (err) {
      // If this is a Haiku 3.5 error, check if we have prior results
      if (err.message && (err.message.includes("Haiku 3.5 model failed") || err.message.includes("The AI couldn't process the template"))) {
        // If we have prior successful results, add error to outputs and continue to return partial results
        if (hasPriorResults(outputs)) {
          outputs.push("Error on goal (" + currentGoal + "): " + err.message);
          incrementProgress(currentGoal);
          console.log("Haiku 3.5 failed on goal '" + currentGoal + "' but prior results exist, returning partial results");
          return { outputs: outputs, hasPartialResults: true, lastError: err.message };
        } else {
          // No prior results, throw error to show error modal
          throw err;
        }
      }
      outputs.push("Error on goal (" + currentGoal + "): " + err.message);
      incrementProgress(currentGoal);
      if (isCancelled) return { outputs: outputs };
    }
  }
  return { outputs: outputs };
}

function preprocessPrompt(prompt) {
  if (prompt.includes("Use EXACTLY the following table structure as a reference:")) {
    const tableStructureStart = prompt.indexOf("Use EXACTLY the following table structure as a reference:") +
                               "Use EXACTLY the following table structure as a reference:".length;
    let tableStructureEnd = prompt.indexOf("CRITICAL:", tableStructureStart);
    if (tableStructureEnd === -1) {
      tableStructureEnd = prompt.indexOf("\n\n", tableStructureStart + 100); 
    }
    if (tableStructureEnd !== -1) {
      const tableStructure = prompt.substring(tableStructureStart, tableStructureEnd).trim();
      const enhancedPrompt = prompt.replace(
        "Use EXACTLY the following table structure as a reference:",
        "HTML REQUIREMENTS:\n\nYou MUST use these EXACT HTML tags with all attributes and classes. Replace '[Placeholder content]' with appropriate content:\n\n"
      );
      return enhancedPrompt;
    }
  }
  return prompt;
}

function extractTableHtml(htmlString) {
  if (!htmlString) return "";
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlString.trim();
  const tableElement = tempDiv.querySelector('table');
  return tableElement ? tableElement.outerHTML : htmlString;
}

$("#move_cb_to_editor").click(function() {
  $('#editModeBtn').click();
  var activeTable = $("#outputTabsContent .tab-pane.active").find("table").first().prop('outerHTML');

  // Temporarily pause DynamicFieldManager to prevent conflicts during content injection
  if (typeof window.parent.DynamicFieldManager !== 'undefined') {
    window.parent.DynamicFieldManager.isProcessing = true;
  }

  // Apply domain link syndication processing based on checkbox state
  let processedContent = activeTable;
  const autoSyndicateChecked = $("input[name='auto_syndicate_domain_link']").is(':checked');
  if (typeof DomainLinkSyndicationManager !== 'undefined') {
    // When loading content into editor, we want to reverse syndication for editing
    // If checkbox is checked, content should show domain links for editing (reverse of save logic)
    processedContent = DomainLinkSyndicationManager.processDomainLinkSyndication(activeTable, !autoSyndicateChecked);
    debugLog("Content loading - domain link processing", {
      autoSyndicateChecked: autoSyndicateChecked,
      reversedForEditing: !autoSyndicateChecked,
      hadSiteLinks: DomainLinkSyndicationManager.hasSiteLinks(activeTable),
      hadDomainLinks: DomainLinkSyndicationManager.hasDomainLinks(activeTable)
    });
  }

  $(".content-wrapper").html(processedContent);

  // Re-enable DynamicFieldManager and trigger span processing after content injection
  setTimeout(function() {
    if (typeof window.parent.DynamicFieldManager !== 'undefined') {
      window.parent.DynamicFieldManager.isProcessing = false;
      // Check if the injected content has dynamic fields that need spans
      const contentWrapper = document.querySelector('.content-wrapper');
      if (contentWrapper) {
        const content = contentWrapper.innerHTML;
        const hasUnwrappedFields = /\[\[\[(?:Weekday|DOTM|DSFX|MonthName|TimeOfDay|FirstName|LastName|EmailAddress|PostCode|BrandName|MastheadName)\]\]\](?!<\/span>)/.test(content);
        if (hasUnwrappedFields) {
          console.log('🔄 AI content injection detected unwrapped dynamic fields, triggering span addition');
          window.parent.DynamicFieldManager.addVisualDynamicFieldSpans();
        }
      }
    }
  }, 150);

  if (typeof showresetButton === 'function') {
    showresetButton();
  }
  $("#aiModal-1a2b").modal('hide');
  setTimeout(function() {
    if ($("#aiModal-1a2b").is(":visible")) {
      $("#aiModal-1a2b").removeClass('show').css('display', 'none');
      $(".modal-backdrop").remove();
      $("body").removeClass('modal-open').css('overflow', '');
      $("body").css('padding-right', '');
    }
    $("#get_started_ai_cb_button").css('display', 'none');
  }, 100);
  console.log('Moved active table content to the email editor.');
});
$("#append_in_editor").click(function() {
  $('#editModeBtn').click();
  var activeTable = $("#outputTabsContent .tab-pane.active").find("table").first();
  if (!activeTable.length) {
    console.error('No table found in active tab');
    show_client_side_message('No table found in active tab', 'warning');
    return;
  }
  try {
    let content = activeTable.prop('outerHTML');

    // Apply domain link syndication processing based on checkbox state
    const autoSyndicateChecked = $("input[name='auto_syndicate_domain_link']").is(':checked');
    if (typeof DomainLinkSyndicationManager !== 'undefined') {
      // When loading content into editor, we want to reverse syndication for editing
      // If checkbox is checked, content should show domain links for editing (reverse of save logic)
      content = DomainLinkSyndicationManager.processDomainLinkSyndication(content, !autoSyndicateChecked);
      debugLog("Content appending - domain link processing", {
        autoSyndicateChecked: autoSyndicateChecked,
        reversedForEditing: !autoSyndicateChecked,
        hadSiteLinks: DomainLinkSyndicationManager.hasSiteLinks(activeTable.prop('outerHTML')),
        hadDomainLinks: DomainLinkSyndicationManager.hasDomainLinks(activeTable.prop('outerHTML'))
      });
    }

    const contentWrapper = document.querySelector('.content-wrapper');
    if (!contentWrapper) {
      console.error('Content wrapper not found');
      show_client_side_message('Content wrapper not found', 'warning');
      return;
    }

    // Temporarily pause DynamicFieldManager to prevent conflicts during content appending
    if (typeof window.parent.DynamicFieldManager !== 'undefined') {
      window.parent.DynamicFieldManager.isProcessing = true;
    }

    const lastTable = contentWrapper.querySelector('table:last-child');
    if (lastTable) {
      const newRow = document.createElement('tr');
      const colCount = lastTable.querySelector('tr')?.children.length || 1;
      const newCell = document.createElement('td');
      newCell.setAttribute('colspan', colCount);
      newCell.appendChild(document.createRange().createContextualFragment(content));
      newRow.appendChild(newCell);
      lastTable.appendChild(newRow);
      show_client_side_message('Content added as a new row in the last table', 'success');
    } else {
      contentWrapper.appendChild(document.createRange().createContextualFragment(content));
      show_client_side_message('Content added to the bottom of the editor', 'success');
    }

    // Re-enable DynamicFieldManager and trigger span processing after content appending
    setTimeout(function() {
      if (typeof window.parent.DynamicFieldManager !== 'undefined') {
        window.parent.DynamicFieldManager.isProcessing = false;
        // Check if the appended content has dynamic fields that need spans
        const updatedContent = contentWrapper.innerHTML;
        const hasUnwrappedFields = /\[\[\[(?:Weekday|DOTM|DSFX|MonthName|TimeOfDay|FirstName|LastName|EmailAddress|PostCode|BrandName|MastheadName)\]\]\](?!<\/span>)/.test(updatedContent);
        if (hasUnwrappedFields) {
          console.log('🔄 AI content append detected unwrapped dynamic fields, triggering span addition');
          window.parent.DynamicFieldManager.addVisualDynamicFieldSpans();
        }
      }
    }, 150);

    if (typeof showresetButton === 'function') {
      showresetButton();
    }
    if (typeof updateByteCount === 'function') {
      updateByteCount();
    }
  } catch (error) {
    console.error('Error appending content:', error);
    show_client_side_message('An error occurred while appending content: ' + error.message, 'error');
  }
  $("#aiModal-1a2b").modal('hide');
  setTimeout(function() {
    if ($("#aiModal-1a2b").is(":visible")) {
      $("#aiModal-1a2b").removeClass('show').css('display', 'none');
      $(".modal-backdrop").remove();
      $("body").removeClass('modal-open').css('overflow', '');
      $("body").css('padding-right', '');
    }
    $("#get_started_ai_cb_button").css('display', 'none');
  }, 100);
  console.log('Added active table content to the bottom of the editor.');
});
$("#generateBlockBtn").click(async function() {
  if ($(this).prop('disabled')) return;
  isCancelled = false;
  $("#error-modal").modal('hide');
  $("#error-state").hide();
  currentPrompt = updatePromptRequest();
  var selectedGoals = [];
  $(".goal-checkbox:checked").each(function() {
    selectedGoals.push($(this).val());
  });

  // Initialize progress system
  initializeProgress(selectedGoals);

  $("#loading-state").show();
  $("#error-state").hide();
  var selectedModel = $("select[name='aiModel']").val();
  var modelName = $("select[name='aiModel'] option:selected").text();
  updateStatusMessage("🔄 Preparing your content generation...");
  $("#loadingModal-1a2b").modal('show');

  // Prevent backdrop click from closing the modal
  $("#loadingModal-1a2b").off('hide.bs.modal.backdrop').on('hide.bs.modal', function(e) {
    if (e.target === this && !$(this).data('allow-close')) {
      e.preventDefault();
      e.stopPropagation();
      return false;
    }
  });
  $("#content-preview-container").html('<div class="text-center mt-4"><div class="spinner-border text-primary" role="status"><span class="sr-only">Loading content...</span></div><p class="mt-2">Generating content...</p><p class="text-muted">Content will be available to view after generation is complete.</p></div>');
  $(".tab-navigation-wrapper").remove();
  try {
    let finalResultObj;
    if (selectedGoals.length === 0) {
      currentStepName = "single_generation";
      updateStatusMessage("🎨 Creating your content...");
      try {
        const finalResult = await callClaude(currentPrompt);
        const cleanResult = extractTableHtml(finalResult);
        finalResultObj = { outputs: [cleanResult] };
        incrementProgress("single_generation");
      } catch (claudeError) {
        console.error("Error from callClaude:", claudeError);
        $("#loading-state").hide();
        $("#error-state").show();
        // Show user-friendly message for Haiku errors, technical message for others
        if (claudeError.message && (claudeError.message.includes("Haiku 3.5 model failed") || claudeError.message.includes("The AI couldn't process the template"))) {
          $("#modal-error-message").text("The AI couldn't process the template. This may be because the instructions are too complex, vague, or confusing, or too many rows were selected, or if 'Edit with AI' was used on a content area that is too large. Please retry with a simpler or clearer prompt, or a prompt with fewer rows, or a smaller content area when 'Edit with AI' is active. Alternatively, you can generate a new prompt from scratch with AI using the 'Generate a prompt' button located on top of the prompt text area in the top left corner of the screen.");
          // Hide "Go To Results" button since base content generation failed with Haiku 3.5 (no prior results possible in single goal scenario)
          $("#goToResultBtn").hide();
        } else {
          $("#modal-error-message").text(claudeError.message || "An unknown error occurred");
        }
        showNormalErrorButtons();
        throw claudeError;
      }
    } else {
      try {
        finalResultObj = await callClaudeForAllGoals(currentPrompt, selectedGoals);
      } catch (claudeError) {
        console.error("Error from callClaudeForAllGoals:", claudeError);
        $("#loading-state").hide();
        $("#error-state").show();
        // Show user-friendly message for Haiku errors, technical message for others
        if (claudeError.message && (claudeError.message.includes("Haiku 3.5 model failed") || claudeError.message.includes("The AI couldn't process the template"))) {
          $("#modal-error-message").text("The AI couldn't process the template. This may be because the instructions are too complex, vague, or confusing, or too many rows were selected, or if 'Edit with AI' was used on a content area that is too large. Please retry with a simpler or clearer prompt, or a prompt with fewer rows, or a smaller content area when 'Edit with AI' is active. Alternatively, you can generate a new prompt from scratch with AI using the 'Generate a prompt' button located on top of the prompt text area in the top left corner of the screen.");
          // For multi-goal scenarios, check if we have prior results before hiding the button
          // This error handler should only be reached if callClaudeForAllGoals threw an error (no partial results)
          $("#goToResultBtn").hide();
        } else {
          $("#modal-error-message").text(claudeError.message || "An unknown error occurred");
        }
        showNormalErrorButtons();
        throw claudeError;
      }
    }

    // Check if we got partial results with a Haiku error
    if (finalResultObj && finalResultObj.hasPartialResults) {
      console.log("Processing partial results due to Haiku 3.5 failure on later goal");
      // Store the partial results
      window.generatedContent = {
        finalResultObj: finalResultObj,
        selectedGoals: selectedGoals
      };
      $("#loadingModal-1a2b").data('allow-close', true).modal('hide');
      $("#error-message-container").hide();
      hasGeneratedContent = true;

      // Show success message but mention the partial failure
      show_client_side_message(
        'Content generated successfully with partial results! Some goals failed but prior content is available.',
        'warning'
      );

      displayGeneratedContent();

      // Show/hide buttons based on request type
      var contentSource = $("input[name='content-source']:checked").val();
      var isExistingTemplate = (contentSource === 'selection' && window.selectedForAIEditingElement);
      console.log('Partial results - Content source:', contentSource, 'Is existing template:', isExistingTemplate);

      if (isExistingTemplate) {
        $('#replace_selection_step3').show();
        $('#move_cb_to_editor').hide();
        console.log('Showing Replace Selection button and hiding Overwrite Content Preview for existing template editing (partial results)');
      } else {
        $('#replace_selection_step3').hide();
        $('#move_cb_to_editor').show();
        console.log('Hiding Replace Selection button and showing Overwrite Content Preview for new content generation (partial results)');
      }

      moveToStep(3);
      $("#generateBlockBtn").prop('disabled', false);
      return; // Exit early to avoid the normal success flow
    }
    window.generatedContent = {
      finalResultObj: finalResultObj,
      selectedGoals: selectedGoals
    };
    $("#loadingModal-1a2b").data('allow-close', true).modal('hide');
    $("#error-message-container").hide();
    hasGeneratedContent = true;
    show_client_side_message(
      'Content generated successfully! Content is ready to be added to the editor.',
      'success'
    );
    displayGeneratedContent();

    // Show/hide buttons based on request type
    var contentSource = $("input[name='content-source']:checked").val();
    var isExistingTemplate = (contentSource === 'selection' && window.selectedForAIEditingElement);
    console.log('Content source:', contentSource, 'Is existing template:', isExistingTemplate);

    if (isExistingTemplate) {
      $('#replace_selection_step3').show();
      $('#move_cb_to_editor').hide();
      console.log('Showing Replace Selection button and hiding Overwrite Content Preview for existing template editing');

      // Maintain AI styling for Content Preview radio button
      if (typeof maintainAIContentPreviewStyling === 'function') {
        maintainAIContentPreviewStyling();
      }
    } else {
      $('#replace_selection_step3').hide();
      $('#move_cb_to_editor').show();
      console.log('Hiding Replace Selection button and showing Overwrite Content Preview for new content generation');
    }

    moveToStep(3);
    $("#generateBlockBtn").prop('disabled', false);
  } catch (err) {
    console.error(err);
    if (err.message && (
        err.message.includes("Claude API Error") ||
        err.message.includes("Empty response") ||
        (err.message.includes("No content received") && !err.message.includes("access") && !err.message.includes("environment")) ||
        err.message.includes("Failed to decode") ||
        err.message.includes("JSON parse error") ||
        err.message.includes("AJAX Error") ||
        err.message.includes("API is overloaded") ||
        err.message.includes("Haiku 3.5 model failed") ||
        err.message.includes("The AI couldn't process the template")
    ) && !err.message.includes("Session expired") && !err.message.includes("session has expired")) {
      $("#generateBlockBtn").prop('disabled', false);
      $("#submitButtonExplanation").hide();
    } else {
      if (err.message && (
          err.message.includes("access") ||
          err.message.includes("Session expired") ||
          err.message.includes("session has expired") ||
          err.message.includes("environment")
      )) {
        console.log("Access/Session error - keeping in modal:", err.message);
        $("#generateBlockBtn").prop('disabled', false);
        $("#submitButtonExplanation").hide();
      } else {
        console.log("Other error - hiding modal and showing in container:", err.message);
        $("#loadingModal-1a2b").data('allow-close', true).modal('hide');
        $("#error-message-container")
          .html("An error occurred: " + err.message)
          .css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1'
          })
          .show();
        $("#generateBlockBtn").prop('disabled', false);
        $("#submitButtonExplanation").hide();
        console.error("Error displayed in container:", err.message);
      }
    }
  }
});
$("#goToResultBtn").click(function(){
  moveToStep(3);
});
updateFormState();
validateScreen1();
validateScreen2();

// Handle initial content loading with domain link syndication
function handleInitialContentLoad() {
  const contentWrapper = $('.content-wrapper');
  if (contentWrapper.length && contentWrapper.html().trim()) {
    const autoSyndicateChecked = $("input[name='auto_syndicate_domain_link']").is(':checked');

    debugLog("Initial content load - domain link syndication check", {
      autoSyndicateChecked: autoSyndicateChecked,
      contentSample: contentWrapper.html().substring(0, 200)
    });

    // When auto-syndicate is checked, we want to show [[[SiteLink]]] in the editor (wrapped in spans)
    // When auto-syndicate is unchecked, we want to show actual domain links (no spans)
    if (!autoSyndicateChecked && typeof DomainLinkSyndicationManager !== 'undefined') {
      const currentContent = contentWrapper.html();
      if (DomainLinkSyndicationManager.hasSiteLinks(currentContent)) {
        // Temporarily pause DynamicFieldManager to prevent conflicts
        let originalProcessingState = false;
        if (typeof DynamicFieldManager !== 'undefined') {
          originalProcessingState = DynamicFieldManager.isProcessing;
          DynamicFieldManager.isProcessing = true;
        }

        const processedContent = DomainLinkSyndicationManager.replaceSiteLinkWithDomainLink(currentContent);
        contentWrapper.html(processedContent);

        // Re-enable DynamicFieldManager and trigger span processing for any remaining dynamic fields
        setTimeout(function() {
          if (typeof DynamicFieldManager !== 'undefined') {
            DynamicFieldManager.isProcessing = originalProcessingState;
            // Check if the updated content has dynamic fields that need spans (excluding SiteLink since we converted it)
            const content = contentWrapper.html();
            const hasUnwrappedFields = /\[\[\[(?:Weekday|DOTM|DSFX|MonthName|TimeOfDay|FirstName|LastName|EmailAddress|PostCode|BrandName|MastheadName)\]\]\](?!<\/span>)/.test(content);
            if (hasUnwrappedFields && !DynamicFieldManager.isProcessing) {
              console.log('🔄 Initial load domain link syndication detected unwrapped dynamic fields, triggering span addition');
              DynamicFieldManager.addSpansToContent();
            }
          }
        }, 100);

        debugLog("Initial content load - converted SiteLinks to domain links for editing", {
          autoSyndicateChecked: autoSyndicateChecked,
          hadSiteLinks: true,
          hadSpannedSiteLinks: /<span[^>]*class="[^"]*mceNonEditable[^"]*"[^>]*>\[\[\[SiteLink\]\]\]<\/span>/.test(currentContent),
          nowHasDomainLinks: DomainLinkSyndicationManager.hasDomainLinks(processedContent)
        });
      }
    } else if (autoSyndicateChecked) {
      debugLog("Initial content load - keeping SiteLinks as-is for syndicated editing", {
        autoSyndicateChecked: autoSyndicateChecked,
        hasSiteLinks: typeof DomainLinkSyndicationManager !== 'undefined' ? DomainLinkSyndicationManager.hasSiteLinks(contentWrapper.html()) : false
      });
    }
  }
}

$(document).ready(function() {
if (window.initializationComplete) return;
window.initializationComplete = true;

// Initialize brand consistency checkbox visibility on page load
// This ensures the checkbox starts in the correct state based on the initial Brand Style value
function initializeBrandConsistencyVisibility() {
  var brandStyleValue = $("select[name='brandCssStyle']").val();
  var brandConsistencyContainer = $("#brand-consistency-container");

  console.log("🔍 BRAND CONSISTENCY DEBUG - Initializing visibility");
  console.log("🔍 BRAND CONSISTENCY DEBUG - Brand style value:", brandStyleValue);
  console.log("🔍 BRAND CONSISTENCY DEBUG - Brand style value type:", typeof brandStyleValue);
  console.log("🔍 BRAND CONSISTENCY DEBUG - Brand style value length:", brandStyleValue ? brandStyleValue.length : 'null/undefined');
  console.log("🔍 BRAND CONSISTENCY DEBUG - Container found:", brandConsistencyContainer.length > 0);

  // Show checkbox only if a brand style is actually selected (not empty string)
  if (brandStyleValue && brandStyleValue !== "") {
    console.log("🔍 BRAND CONSISTENCY DEBUG - Showing checkbox");
    brandConsistencyContainer.attr('style', 'display: inline-flex !important;');
  } else {
    console.log("🔍 BRAND CONSISTENCY DEBUG - Hiding checkbox");
    brandConsistencyContainer.attr('style', 'display: none !important;');
  }
}

// Initialize Content Preview radio button styling on page load
function initializeContentPreviewStyling() {
  var hasContent = $.trim($(".content-wrapper").html()) !== "";
  var $selectionRadio = $("input[name='content-source'][value='selection']");
  var $selectionLabel = $selectionRadio.closest('label');

  if (!hasContent) {
    $selectionLabel.addClass('content-preview-disabled')
                   .attr('data-tooltip', 'Right-click on content in the Content Preview and select "Edit with AI" to enable this option');
  }
}

// Call the initialization functions
initializeContentPreviewStyling();
initializeBrandConsistencyVisibility();

// Function to maintain AI styling when content is being edited
function maintainAIContentPreviewStyling() {
  var contentSource = $("input[name='content-source']:checked").val();
  var $selectionRadio = $("input[name='content-source'][value='selection']");
  var $selectionLabel = $selectionRadio.closest('label');

  if (contentSource === 'selection' && window.selectedForAIEditingElement) {
    $selectionLabel.addClass('ai-content-preview-active');
  }
}

// Export function globally for use in other parts of the application
window.maintainAIContentPreviewStyling = maintainAIContentPreviewStyling;
$("#desktop-view-btn, #mobile-view-btn")
  .off("click")
  .on("click", function() {
    console.log("View toggle clicked: " + $(this).attr("id"));
    const isDesktop = $(this).attr("id") === "desktop-view-btn";
    $("#desktop-view-btn, #mobile-view-btn").removeClass("active");
    $(this).addClass("active");
    if (isDesktop) {
      $("#content-preview-container")
        .removeClass("mobile-view_scaled_7")
        .addClass("desktop-view");
      $("iframe").removeClass("mobileViewClass");
    } else {
      $("#content-preview-container")
        .removeClass("desktop-view")
        .addClass("mobile-view_scaled_7");
      $("iframe").addClass("mobileViewClass");
    }
    localStorage.setItem("previewView", isDesktop ? "desktop" : "mobile");
  });
  const savedView = localStorage.getItem("previewView") || "desktop";
  if (savedView === "mobile") {
    $("#mobile-view-btn").click();
    $("iframe").addClass("mobileViewClass");
  } else {
    $("#desktop-view-btn").click();
    $("iframe").removeClass("mobileViewClass");
  // Retry button functionality removed - users should modify their prompt instead
  // $("#retry-api-btn").on("click", function() { ... });
  $("#close-api-btn").on("click", function() {
    $("#loadingModal-1a2b").data('allow-close', true).modal('hide');
    isCancelled = false;
    $("#loading-state").show();
    $("#error-state").hide();
  });
  $(document).on("click", "#refresh-page-btn", function() {
    console.log("Refresh page button clicked - refreshing parent window");
    if (window.parent && window.parent !== window) {
      window.parent.location.reload();
    } else {
      window.location.reload();
    }
  });
  }
});

/* ==================== Event Handlers End ==================== */


/* ==================== Async Form Submission Handler Start ==================== */

async function handleSubmit(event) {
  event.preventDefault();
  var form = event.target;
  var formDataObj = {};
  let codeText = $("#json-code").text().trim();
  if (codeText) {
      try {
        let codeObj = JSON.parse(codeText);
        formDataObj = codeObj;
        console.log("Successfully parsed #json-code. Final merged formDataObj =>", formDataObj);
      } catch (e) {
        console.warn("Could not parse JSON from #json-code. Using only #settings-container data:", e);
      }
  }
  formDataObj['generatedContent'] = $("#content-preview-container").html();

  // OPTIMIZATION: Add debug logging for form data preparation
  console.log("🚀 FORM DATA DEBUG - Form data object:", formDataObj);
  console.log("🚀 FORM DATA DEBUG - Generated content length:", formDataObj['generatedContent'] ? formDataObj['generatedContent'].length : 0);

  var formDataJson = JSON.stringify(formDataObj);
  console.log("🚀 FORM DATA DEBUG - Form data JSON size:", formDataJson.length, "characters");
  console.log("🚀 FORM DATA DEBUG - Form data JSON preview (first 500 chars):", formDataJson.substring(0, 500));

  var jsonInput = form.querySelector('input[name="formDataJson"]');
  if (!jsonInput) {
    jsonInput = document.createElement('input');
    jsonInput.type = 'hidden';
    jsonInput.name = 'formDataJson';
    form.appendChild(jsonInput);
  }
  jsonInput.value = formDataJson;
  console.log('Form submission prevented');
  console.log('🚀 FORM DATA DEBUG - Final form data json length:', formDataJson.length);
}

function show_client_side_message(message,message_color) {
		$('#client_side_message').html('<div id="appC_message_2" class="alert alert-dismissible fade show alert-' + message_color + '" role="alert" style="display:block;">' + message + '<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button></div>');
}

function refreshIframebyId(iframe_id) {
  $("#" + iframe_id).attr('src', ($('#' + iframe_id).attr('src')));
}
async function add_or_edit_ai_cb_api(action_area, src_secret, id, type, posInEmail, event) {
  event.preventDefault();
  event.stopPropagation();
  $("#saveModal").modal('show');

  // Prevent backdrop click from closing the modal
  $("#saveModal").off('hide.bs.modal.backdrop').on('hide.bs.modal', function(e) {
    if (e.target === this && !$(this).data('allow-close')) {
      e.preventDefault();
      e.stopPropagation();
      return false;
    }
  });
  try {
    const timeoutId = setTimeout(() => {
      $("#saveModal").data('allow-close', true).modal('hide');
      show_client_side_message("Request timed out. The server may be experiencing issues.", "danger");
    }, 30000); 
    let data = new FormData(event.target);
    let formJSON = Object.fromEntries(data.entries());
    formJSON.friendly_name = $("input[name='friendly_name']").val() || "";
    if (action_area == 'copy_or_duplicate_from_other_email') {
      var id = data.getAll("content_block_id").toString();
      var duplicate_or_reference = data.getAll("duplicate_or_reference").toString();
      var action_area = duplicate_or_reference;
      var posInEmail = data.getAll("PositionInEmail").toString();
      var use_ref_cb_hide = data.getAll("use_ref_cb_hide").toString();
    } else {
      var newPos = data.getAll("PositionInEmail").toString();
      var ref_cb = data.getAll("ref_cb").toString();
      formJSON.hide_after_date = $("input[name='hide_after_date']").val() || "";
      formJSON.hide_before_date = $("input[name='hide_before_date']").val() || "";
      formJSON.use_hide_after_date = $("input[name='use_hide_after_date']").is(':checked') ? "1" : "";
      formJSON.use_hide_before_date = $("input[name='use_hide_before_date']").is(':checked') ? "1" : "";
      formJSON.marketing = $("input[name='marketing']").is(':checked') ? "1" : "";
      formJSON.hide_from_prem_subs = $("input[name='hide_from_prem_subs']").is(':checked') ? "1" : "";
      var selectedDays = [];
      $('input[name="hide_set_of_days"]:checked').each(function() {
        selectedDays.push($(this).val());
      });
      formJSON.hide_set_of_days = selectedDays.join(',');
      var make_backup = data.getAll("make_backup").toString();
      var src_preview = data.getAll("src").toString();
      var domain_link_masthead = data.getAll("domain_link_masthead").toString();
      var create_or_update_backup = data.getAll("create_or_update_backup").toString();
      var backup_id = data.getAll("backup_id").toString();
      var de_eca_use_more_cts = (data.getAll("de_eca_use_more_cts").toString() == 'true') ? '1': '0';
      var edit_eca_use_more_cts = data.getAll("use_more_cts").toString();
      var block_section = data.getAll("block_section").toString();
      var disable_utm_appending = $("input[name='disable_utm_appending']").is(':checked') ? '1' : '0';
      var auto_syndicate_domain_link = $("input[name='auto_syndicate_domain_link']").is(':checked') ? '1' : '0';
    }
    formJSON.disable_utm_appending = disable_utm_appending;
    formJSON.auto_syndicate_domain_link = auto_syndicate_domain_link;
    // Add checkbox states for dynamic class management
    formJSON.stackContentVertical = $("#stack_content_vertical").is(":checked");
    formJSON.extraSpacingInElements = $("#extra_spacing_in_elements").is(":checked");
    if (data.getAll("backup_name").toString()) {
      formJSON.friendly_name = data.getAll("backup_name").toString();
    }

   const apiUrl = 'https://cloud.e.newsdigitalmedia.com.au/gh_%%=v(@appStateEnvironment)=%%_add_edit_ai_cb_api?biz_env=%%=v(@appStateEnvironment)=%%&sys_env=%%=v(@environmentAppCentre)=%%&c=%%=v(@cookie)=%%&action_area='+action_area+'&id='+id+'&cbPos='+posInEmail+'&secret='+src_secret+'&use_ref_cb_hide='+use_ref_cb_hide+'&make_backup='+make_backup+'&create_or_update_backup='+create_or_update_backup+'&backup_id='+backup_id+'&feature_branch=%%=v(@feature_branch)=%%';

    // OPTIMIZATION: Add comprehensive save payload debugging
    const savePayload = JSON.stringify(formJSON);
    console.log("🚀 SAVE PAYLOAD DEBUG - Making API call to:", apiUrl);
    console.log("🚀 SAVE PAYLOAD DEBUG - Form JSON object:", formJSON);
    console.log("🚀 SAVE PAYLOAD DEBUG - Payload size:", savePayload.length, "characters");
    console.log("🚀 SAVE PAYLOAD DEBUG - Payload preview (first 500 chars):", savePayload.substring(0, 500));
    console.log("🚀 SAVE PAYLOAD DEBUG - Payload preview (last 500 chars):", savePayload.substring(Math.max(0, savePayload.length - 500)));
    const controller = new AbortController();
    const signal = controller.signal;
    let response = await fetch(apiUrl, {
      method: 'POST',
      body: savePayload,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      },
      signal: signal
    }).catch(error => {
      clearTimeout(timeoutId);
      $("#saveModal").data('allow-close', true).modal('hide');
      let errorMessage = "Network error occurred.";
      if (error.name === 'AbortError') {
        errorMessage = "The request was aborted due to timeout.";
      }
      show_client_side_message(errorMessage, "danger");
      console.error("Fetch error:", error);
      throw error; 
    });
    clearTimeout(timeoutId);
    if (!response.ok) {
      $("#saveModal").data('allow-close', true).modal('hide');
      let errorText = "";
      try {
        errorText = await response.text();
      } catch (e) {
        errorText = "No additional error information available";
      }
      const statusMessage = `Server error: ${response.status} ${response.statusText}`;
      show_client_side_message(statusMessage, "danger");
      console.error(statusMessage, errorText);
      console.error("Response details:", {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries([...response.headers.entries()]),
        errorText: errorText
      });
      return; 
    }
    let resp;
    try {
      resp = await response.json();
    } catch (jsonError) {
      $("#saveModal").data('allow-close', true).modal('hide');
      show_client_side_message("Failed to parse server response. The server may be returning an invalid format.", "danger");
      console.error("JSON parse error:", jsonError);
      return; 
    }
    let rm = resp.form_message ? resp.form_message : "Couldn't perform this action. The SFMC team has been notified and will get back to you shortly.";
    let rc = (resp.message_color != 'danger' && resp.message_color != 'success' && resp.message_color != 'warning') ? 'danger' : resp.message_color;
    if (rm == 'Invalid user session detected. Reloading app...') {
      $('#saveModalMessage').text(rm);
      setTimeout(() => {
        parent.location.reload(true);
        $("#saveModal").data('allow-close', true).modal('hide');
      }, 3000);
    } else {
      $("#saveModal").data('allow-close', true).modal('hide');
    }
    if (resp.message == 'reloadpage') {
      show_client_side_message('User session expired. Logging out...', 'warning');
      setTimeout(() => {
        window.location.reload(true);
      }, 1500);
    } else {
      if (action_area == 'edit' || action_area == 'decouple') {
        $('#save_block_message_' + id).css("display", "none");
        show_client_side_message(rm, rc);
        if (resp.message_color == 'success') {
          $('#duplicate_cb'+id).css("display", "inline-block");
          $('#share_cb'+id).css("display", "inline-block");
          $('#backup_settings'+id).css("display", "block");
          if (action_area == 'edit' && make_backup == 1) {
            $('#backup'+id).css("display", "unset");
            $('#show_back_up_preview_link'+id).css("display", "inline");
            $('#make_backup_checkbox'+id).prop("checked", false);
          }

          // Update content-wrapper to reflect domain link syndication after successful save
          if (auto_syndicate_domain_link == '1' && typeof DomainLinkSyndicationManager !== 'undefined') {
            const contentWrapper = $('.content-wrapper');
            if (contentWrapper.length && contentWrapper.html().trim()) {
              const currentContent = contentWrapper.html();
              const processedContent = DomainLinkSyndicationManager.replaceDomainLinkWithSiteLink(currentContent);

              if (processedContent !== currentContent) {
                // Temporarily pause DynamicFieldManager to prevent conflicts
                let originalProcessingState = false;
                if (typeof DynamicFieldManager !== 'undefined') {
                  originalProcessingState = DynamicFieldManager.isProcessing;
                  DynamicFieldManager.isProcessing = true;
                }

                contentWrapper.html(processedContent);

                // Re-enable DynamicFieldManager and trigger span processing
                setTimeout(function() {
                  if (typeof DynamicFieldManager !== 'undefined') {
                    DynamicFieldManager.isProcessing = originalProcessingState;
                    // Check if the updated content has dynamic fields that need spans
                    const content = contentWrapper.html();
                    const hasUnwrappedFields = /\[\[\[(?:Weekday|DOTM|DSFX|MonthName|TimeOfDay|FirstName|LastName|EmailAddress|PostCode|BrandName|MastheadName)\]\]\](?!<\/span>)/.test(content);
                    if (hasUnwrappedFields && !DynamicFieldManager.isProcessing) {
                      console.log('🔄 Post-save domain link syndication detected unwrapped dynamic fields, triggering span addition');
                      DynamicFieldManager.addSpansToContent();
                    }
                  }
                }, 100);

                debugLog("Post-save content update", {
                  message: "Content-wrapper updated to show [[[SiteLink]]] after successful save",
                  replacements: DomainLinkSyndicationManager.hasDomainLinks(currentContent) ? "domain links → [[[SiteLink]]]" : "none"
                });
              }
            }
          }
        }
        if (((posInEmail != newPos || !newPos || action_area == 'backup' || make_backup == 1) ||
            de_eca_use_more_cts != edit_eca_use_more_cts && block_section == 1) ||
            auto_syndicate_domain_link == '1') {
          if (action_area == 'decouple' || action_area == 'backup' || auto_syndicate_domain_link == '1') {
            var action_area = 'edit';
          }
          if (resp.message_color === 'success') {
            $('.modall').modal('hide');
          }
        } else {
          $('.modall').modal('hide');
        }
      } else {
        if (resp.message_color != 'warning') {
          show_client_side_message(rm, rc);
        } else {
          show_client_side_message(rm, rc);
          refreshIframebyId('emailPreviewIframe');
          $("#emailPreviewIframe").css("display", "block");
          $('.modall').modal('hide');
        }
      }
    }
  } catch (e) {
    $("#saveModal").data('allow-close', true).modal('hide');
    const errorMessage = "An unexpected error occurred: " + (e.message || e);
    show_client_side_message(errorMessage, "danger");
    console.error("Error in add_or_edit_ai_cb_api:", e);
    $("#emailPreviewIframe").css("display", "none");
    $('.modall').modal('hide');
    $("#loading_preview_right_view").hide();
    $("#error_right_view").css("display", "block");
  }
}

$(document).ready(function() {
  // Handle initial content loading with domain link syndication
  // Delay to ensure DynamicFieldManager has processed content first (it runs after 100ms)
  setTimeout(function() {
    handleInitialContentLoad();
  }, 200);

  function checkForHighlightedElements() {
    const contentPreview = document.getElementById('contentPreview');
    if (!contentPreview) return;
    const highlightedElements = contentPreview.querySelectorAll('.highlighted-from-editor');
    const saveBtn = document.getElementById('saveButton');
    if (!saveBtn) return;
    let warningMessage = document.getElementById('editor-selection-warning');
    if (!warningMessage) {
      warningMessage = document.createElement('div');
      warningMessage.id = 'editor-selection-warning';
      warningMessage.style.color = '#dc3545';
      warningMessage.style.fontWeight = 'bold';
      warningMessage.style.marginRight = '10px';
      warningMessage.style.display = 'none';
      if (saveBtn && saveBtn.parentNode) {
        saveBtn.parentNode.insertBefore(warningMessage, saveBtn);
      }
    }
    if (typeof checkSaveButtonState === 'function') {
      checkSaveButtonState();
    }
  }
  const contentPreview = document.getElementById('contentPreview');
  if (contentPreview) {
    const observer = new MutationObserver(function(mutations) {
      checkForHighlightedElements();
    });
    observer.observe(contentPreview, {
      childList: true,     
      subtree: true,       
      attributes: true,    
      attributeFilter: ['class'] 
    });
    checkForHighlightedElements();
  }
  $("#saveButton").on("click", function() {
    try {
      debugLog("Save Button Clicked", "Starting form submission process");
      const form = document.getElementById('send_prompt_to_llm');
      if (!form) {
        throw new Error("Form element not found");
      }
      const contentWrapperHTML = $(".content-wrapper").html() || "";
      if (!contentWrapperHTML.trim()) {
        show_client_side_message("No content to save. Please generate content first.", "warning");
        return;
      }
      if ($('#emailPreviewContainer').is(':visible')) {
        $('#emailPreviewLoadingOverlay').show();
        const iframe = document.getElementById('emailPreviewIframe');
        if (iframe) {
          if (!iframe.src) {
            if (typeof loadEmailPreviewIframe === 'function') {
              loadEmailPreviewIframe();
            }
          } else {
            if (typeof refreshEmailPreviewIframe === 'function') {
              refreshEmailPreviewIframe();
            }
          }
        }
      }
      const action_area = $("input[name='action_area']").val() || "add";
      const src_secret = $("input[name='src_secret']").val() || "";
      const id = $("input[name='de_eca_EventID']").val() || "";
      const type = "ai_free_form_block";
      const posInEmail = $("select[name='PositionInEmail']").val() || "1";
      debugLog("Form Parameters", { action_area, id, type, posInEmail });
      updateHideDaysField();
      let processedContent = contentWrapperHTML;

      // UTM processing
      if (typeof UTMManager !== 'undefined') {
        processedContent = UTMManager.ensureHttpsUrls(processedContent);
        processedContent = UTMManager.addUTMTagsToContent(processedContent);
      }

      // Domain Link Syndication processing
      const autoSyndicateChecked = $("input[name='auto_syndicate_domain_link']").is(':checked');
      if (typeof DomainLinkSyndicationManager !== 'undefined') {
        processedContent = DomainLinkSyndicationManager.processDomainLinkSyndication(processedContent, autoSyndicateChecked);
        debugLog("Domain Link Syndication", {
          enabled: autoSyndicateChecked,
          hasDomainLinks: DomainLinkSyndicationManager.hasDomainLinks(contentWrapperHTML),
          hasSiteLinks: DomainLinkSyndicationManager.hasSiteLinks(contentWrapperHTML)
        });
      }

      // Dynamic Field processing
      if (typeof DynamicFieldManager !== 'undefined') {
        // Ensure DynamicFieldManager is not processing during save to prevent conflicts
        const originalProcessingState = DynamicFieldManager.isProcessing;
        DynamicFieldManager.isProcessing = true;

        processedContent = DynamicFieldManager.removeDynamicFieldSpansFromContent(processedContent);

        // Restore original processing state
        DynamicFieldManager.isProcessing = originalProcessingState;
        console.log('🔄 Dynamic field spans removed for save operation');
      }
      if (!$("#freeText_content").length) {
        $("<input>").attr({
          type: "hidden",
          id: "freeText_content",
          name: "freeText_content"
        }).val(processedContent).appendTo(form);
      } else {
        $("#freeText_content").val(processedContent);
      }
      debugLog("Content Length", contentWrapperHTML.length);
      const eventObj = {
        preventDefault: function() {},
        stopPropagation: function() {},
        target: form
      };
      add_or_edit_ai_cb_api(action_area, src_secret, id, type, posInEmail, eventObj);
    } catch (error) {
      $("#saveModal").data('allow-close', true).modal('hide');
      if (window.saveTimeoutId) {
        clearTimeout(window.saveTimeoutId);
      }
      showErrorModal(
        "Save Error",
        "Error preparing form submission: " + error.message,
        error
      );
    }
  });
  $("#save_block_and_exit").on("click", function(event) {
    event.preventDefault();
    $("#saveButton").click();
  });
  function updateHideDaysField() {
    try {
      const selectedDays = [];
      $('input[name="hide_set_of_days"]:checked').each(function() {
        selectedDays.push($(this).val());
      });
      $('#hide_set_of_days_hidden').val(selectedDays.join(','));
      debugLog("Selected Days", selectedDays);
    } catch (error) {
      console.error("Error updating hide days field:", error);
    }
  }
  $("#cancelSaveButton").on("click", function() {
    $("#saveModal").data('allow-close', true).modal('hide');
    if (window.saveTimeoutId) {
      clearTimeout(window.saveTimeoutId);
    }
    show_client_side_message("Save operation canceled.", "info");
  });
});

/**
 * Show a custom formatted message in the console for debugging
 * @param {string} label - Label for the debug message
 * @param {any} data - Data to display
 */
function debugLog(label, data) {
  console.log(
    `%c${label}`,
    'background: #3498db; color: white; padding: 2px 6px; border-radius: 2px; font-weight: bold;',
    data
  );
}

/**
 * Display a client-side message with a timeout
 * @param {string} message - Message to display
 * @param {string} messageColor - Color class for the message (success, danger, warning, info)
 * @param {number} timeout - Time in milliseconds before the message disappears (0 = never)
 */
function show_client_side_message(message, messageColor, timeout = 5000) {
  const alertDiv = '<div id="appC_message_2" class="alert alert-dismissible fade show alert-' +
    messageColor + '" role="alert" style="display:block;">' +
    message +
    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
    '<span aria-hidden="true">&times;</span></button></div>';
  $('#client_side_message').html(alertDiv);
  console.log(
    `%cClient Message (${messageColor})`,
    'background: #2c3e50; color: white; padding: 2px 6px; border-radius: 2px;',
    message
  );
  if (timeout > 0) {
    setTimeout(() => {
      $('#appC_message_2').alert('close');
    }, timeout);
  }
}

/**
 * Force display an error modal with detailed information
 * @param {string} title - Modal title
 * @param {string} message - Error message
 * @param {object} details - Additional error details (optional)
 */
function showErrorModal(title, message, details = null) {
  if (!$('#errorModal').length) {
    $('body').append(`
      <div class="modal fade" id="errorModal" tabindex="-1" role="dialog" aria-labelledby="errorModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
          <div class="modal-content">
            <div class="modal-header bg-danger text-white">
              <h5 class="modal-title" id="errorModalLabel">Error</h5>
              <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
              <div class="alert alert-danger" id="errorModalContent"></div>
              <div id="errorDetails" style="display: none;">
                <hr>
                <h6>Technical Details:</h6>
                <pre id="errorDetailContent" style="max-height: 200px; overflow-y: auto;"></pre>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      </div>
    `);
  }
  $('#errorModalLabel').text(title);
  $('#errorModalContent').text(message);
  if (details) {
    $('#errorDetailContent').text(JSON.stringify(details, null, 2));
    $('#errorDetails').show();
  } else {
    $('#errorDetails').hide();
  }
  $('#errorModal').modal('show');
  console.error('Error:', message, details);
}

/* ==================== Async Form Submission Handler End ==================== */


/* ==================== Form Submission & Update Functions Start ==================== */

function updateJSONCodeView() {
  var formDataObj = {};
  $("form").find("input, select, textarea").each(function() {
    var name = $(this).attr("name");
    if (name) {
      if ($(this).attr("type") === "radio") {
        if ($(this).is(":checked")) { formDataObj[name] = $(this).val(); }
      } else if ($(this).attr("type") === "checkbox") {
        if (!$(this).hasClass("day-checkbox") && !$(this).hasClass("goal-checkbox")) {
          if ($(this).is(":checked")) { formDataObj[name] = $(this).val(); }
        }
      } else {
        formDataObj[name] = $(this).val();
      }
    }
  });
  // OPTIMIZATION: Add debug logging for JSON code view
  const jsonCodeString = JSON.stringify(formDataObj, null, 2).trim();
  console.log("🚀 JSON CODE DEBUG - Form data object size:", Object.keys(formDataObj).length, "properties");
  console.log("🚀 JSON CODE DEBUG - JSON string length:", jsonCodeString.length, "characters");
  $("#json-code").text(jsonCodeString);
}

function validateScreen1() {
  var friendly_name = $(".main-block-name").val().trim();
  var nextButton = $("#nextButtonScreen1");
  if (friendly_name === "") {
    nextButton.prop('disabled', true);
    $("#nextButtonExplanation").text("Please provide an Internal Block Name.");
  } else {
    nextButton.prop('disabled', false);
    $("#nextButtonExplanation").text("");
  }
}

function updateFormState() {
  if (currentStep === 2) {
    state.source = $("input[name='content-source']:checked").val();
  }
  updateJSONCodeView();
  updatePromptRequest();
}

function validateScreen2() {
  var contentSource = $("input[name='content-source']:checked").val();
  var valid = true;
  var explanation = "";
  if (contentSource === "selection") {
    return; 
  }
  if (contentSource === "none") {
    var promptVal = $("textarea[name='fromSentence']").val().trim();
    if (promptVal === "") {
      valid = false;
      explanation = "Please provide a prompt when 'new' is selected.";
    }
  }
  if (contentSource === "capi") {
    var capiVal = $("#capi_id_dropdown").val();
    console.log("CAPI select value:", capiVal);
    if (!capiVal) {
      valid = false;
      explanation = "Please select a CAPI option from the dropdown.";
    }
  }
  else if (contentSource === "existing") {
    var existingVal = $("#cb_id_dropdown").val();
    console.log("Existing select value:", existingVal);
    if (!existingVal) {
      valid = false;
      explanation = "Please select a block from the dropdown.";
    }
  }
  $('#generateBlockBtn').prop('disabled', !valid);
  if (explanation === "") {
    $("#submitButtonExplanation").hide();
  } else {
    $("#submitButtonExplanation").show().text(explanation);
  }
}

function updatePromptRequest() {
  var friendly_name = $(".main-block-name").val().trim();
  friendly_name = friendly_name || "auto_gen_example";
  var tone = $("select[name='writingStyle']").val();
  var readerLevel = $("select[name='readerLevel']").val();
  var numColumns = $("input[name='columns']").val();
  var numRows = $("input[name='rows']").val();
  var numTables = $("input[name='tables']").val();
  var creativityLevel = $("input[name='creativityLevel']").val();
  var additionalPrompt = $("textarea[name='fromSentence']").val().trim();
  var contentSource = $("input[name='content-source']:checked").val();
  var contentType = "new Content";
  var promptText = ""; 
  if (contentSource === "capi") {
    contentType = "CAPI Content";
    promptText = "# Create an email content block with the data points inside the provided blob (collection). The below instructions explain how you are to create the content block.\n\n";
  } else if (contentSource === "existing") {
    contentType = "Existing Content Block";
    promptText = "# Create an email content block with the data points inside the provided blob (html block). The below instructions explain how you are to create the content block.\n\n";
  } else if (contentSource === "selection") {
    contentType = "Selected Content";
    var selectedElement = window.selectedForAIEditingElement;
    var selectedHtml = selectedElement ? selectedElement.outerHTML : "";
    if (!selectedHtml) {
      console.warn("updatePromptRequest: window.selectedForAIEditingElement not found when source is 'selection'.");
      selectedHtml = "<!-- Error: No element selected for AI editing -->";
    }
    // For existing template editing, use a more focused prompt
    promptText = "# Edit and improve the provided email content based on the instructions below.\n\n";
    promptText += "INSTRUCTIONS:\n";
  } else { 
    contentType = "new Content";
    promptText = "# Create an email content block with the below instructions!\n\n";
    promptText += "PRIMARY INSTRUCTIONS:\n";
  }
  if (additionalPrompt) {
    promptText += "   - " + additionalPrompt + "\n";
  } else if (contentSource === "none") {
      promptText += "   - Generate content based on technical and styling requirements.\n";
  } else if (contentSource === "selection" && !additionalPrompt) {
      promptText += "   - Improve the content design, readability, and visual appeal while maintaining the existing structure.\n";
  } else if (!additionalPrompt) {
     promptText += "   - [No additional user instructions provided]\n";
  }
  // Skip table structure generation for selection mode
  if(tone || readerLevel || (numRows && numColumns && contentSource !== 'selection')) {
    promptText += "\nTECHNICAL INSTRUCTIONS:\n";
    if (numRows && numColumns && contentSource !== 'selection') {
      const cellPadding = 15;

      // Get dynamic classes for prompt generation
      const classes = buildDynamicClasses();
      const containerClass = classes.containerForceRow || "container";

      // Use the same width sets as generateTableStructure
      const widthSets = {
        column: { 1: 610, 2: 305, 3: 203 },
        display_block: { 1: 590, 2: 285, 3: 183 }
      };

      let columnWidth = classes.column === "column" ? widthSets.column[numColumns] : widthSets.display_block[numColumns];
      if (!columnWidth) {
        let baseWidth = Math.floor(610 / numColumns);
        columnWidth = classes.column === "column" ? baseWidth : baseWidth - 20;
      }

      const imageWidth = Math.max(columnWidth - 51, 50); // TD width - total spacing (30px padding + 21px additional)
      const maxWidth = columnWidth - 30; // TD width - padding only for max-width

      promptText += "   - CRITICAL: Each email-cell must have zero padding and contain a nested table with class '" + containerClass + "' that handles the " + cellPadding + "px padding. The table's child tds MUST have the same padding value "+ cellPadding +"px applied in a style attribute.\n";
      promptText += "   - Preserve ALL <img> and <a> tags...\n";
      const stackVertical = $("#stack_content_vertical").is(":checked");
      const tableStructure = generateTableStructure(numRows, numColumns, stackVertical);
      promptText += "\nTABLE STRUCTURE:\n";
      promptText += tableStructure + "\n\n";
    }
  }
  var brandCssStyleStr = $("select[name='brandCssStyle']").val();
  if (brandCssStyleStr && brandCssStyleStr !== 'Optional') {
    // OPTIMIZATION: Convert brand CSS JSON to plain text to eliminate escaping issues
    try {
      const brandCssObj = JSON.parse(brandCssStyleStr);
      const plainTextBrandCss = convertBrandCssToPlainText(brandCssObj);
      promptText += "\nSTYLING SPECIFICATIONS:\n";
      promptText += plainTextBrandCss + "\n\n";
      console.log('🚀 BRAND CSS DEBUG - Converted brand CSS to plain text in updatePromptRequest');
      console.log('🚀 BRAND CSS DEBUG - Original JSON length:', brandCssStyleStr.length);
      console.log('🚀 BRAND CSS DEBUG - Plain text length:', plainTextBrandCss.length);
      console.log('🚀 BRAND CSS DEBUG - Size reduction:', (brandCssStyleStr.length - plainTextBrandCss.length), 'characters');
    } catch (parseError) {
      // Fallback to original string if parsing fails
      promptText += "\nSTYLING SPECIFICATIONS:\n";
      promptText += brandCssStyleStr + "\n\n";
      console.log('🚀 BRAND CSS DEBUG - Failed to parse brand CSS in updatePromptRequest, using original string:', parseError.message);
    }
  }
  $("#prompt-request-content").text(promptText);
  return promptText;
}

/* ==================== Form Submission & Update Functions End ==================== */


/* ==================== Navigation Functions Start ==================== */

function moveToStep(step) {
  let displayStep = step;
  if (visitedSteps.indexOf(step) === -1) {
    visitedSteps.push(step);
  }
  $('.step-indicator-1a2b').each(function () {
    const stepNum = parseInt($(this).data('step'));
    if (stepNum < step) { $(this).removeClass('active').addClass('completed'); }
    else if (stepNum === step) { $(this).removeClass('completed').addClass('active'); }
    else { $(this).removeClass('active completed'); }
  });
  $('.step-panel-1a2b').removeClass('active');
  $('.step-panel-1a2b[data-step="' + step + '"]').addClass('active');
  currentStep = step;
  updateFormState();
  if (hasGeneratedContent && currentStep === 2) {
    $("#goToResultBtn").show();
  } else {
    $("#goToResultBtn").hide();
  }
  if (currentStep === 1) { validateScreen1(); }
  if (currentStep === 2) { validateScreen2(); }
}

/* ==================== Navigation Functions End ==================== */


/* ==================== Event Handlers Start ==================== */

$('.step-indicator-1a2b').click(function () {
  const step = parseInt($(this).data('step'));
  if (visitedSteps.indexOf(step) !== -1) {
    moveToStep(step);
  }
});
$("#nextButtonScreen1").click(function () {
  if (currentStep < totalSteps) { moveToStep(currentStep + 1); }
});
$('.back-button-1a2b').click(function () {
  // Only apply to main form buttons, not modal buttons
  if (!$(this).closest('#promptImprovementModal').length) {
    if (currentStep > 1) { moveToStep(currentStep - 1); }
  }
});
$("input[name='content-source']").change(function () {
  var selectedValue = $(this).val();

  // Remove AI styling from all content-source labels first
  $("input[name='content-source']").each(function() {
    $(this).closest('label').removeClass('ai-content-preview-active');
  });

  // Show/hide columns and rows fields based on content source
  if (selectedValue === 'selection') {
    $(".columns-rows-fields").addClass('hidden-for-selection');
  } else {
    $(".columns-rows-fields").removeClass('hidden-for-selection');
  }

  if (selectedValue === 'existing') {
    $("#existingBlockSelection").show();
    $("#capiSelection").hide();
    $("#capi_id_dropdown").val("");
    $("select[name='capi_id']").val("");
    $("#generateBlockBtn").prop('disabled', false);
    $("#submitButtonExplanation").hide();
  } else if (selectedValue === 'capi') {
    $("#capiSelection").show();
    $("#existingBlockSelection").hide();
    $("#cb_id_dropdown").val("");
    $("select[name='cb_id']").val("");
    $("#generateBlockBtn").prop('disabled', false);
    $("#submitButtonExplanation").hide();
  } else if (selectedValue === 'none') {
    $("#existingBlockSelection").hide();
    $("#capiSelection").hide();
    $("#cb_id_dropdown, #capi_id_dropdown").val("");
    $("select[name='cb_id'], select[name='capi_id']").val("");
    $("#generateBlockBtn").prop('disabled', false);
    $("#submitButtonExplanation").hide();
  } else if (selectedValue === 'selection') {
    $("#existingBlockSelection").hide();
    $("#capiSelection").hide();
    $("#generateBlockBtn").prop('disabled', true);
    $("#submitButtonExplanation").show().text("Right-click on a block in the editor & click \"Edit with AI\"");
  }
  updateFormState();
  validateScreen2();
});
$("#capi_id_dropdown").on("change", function(){
  $("select[name='capi_id']").val($(this).val());
  updateFormState();
  validateScreen2();
});
$("#cb_id_dropdown").on("change", function(){
  $("select[name='cb_id']").val($(this).val());
  updateFormState();
  validateScreen2();
});

// Handle Brand Style select field changes to show/hide brand consistency checkbox
// This ensures the brand consistency checkbox is only visible when a brand style is selected
$("select[name='brandCssStyle']").on("change", function(){
  var selectedValue = $(this).val();
  var brandConsistencyContainer = $("#brand-consistency-container");

  console.log("🔍 BRAND CONSISTENCY DEBUG - Brand style changed");
  console.log("🔍 BRAND CONSISTENCY DEBUG - New value:", selectedValue);
  console.log("🔍 BRAND CONSISTENCY DEBUG - New value type:", typeof selectedValue);
  console.log("🔍 BRAND CONSISTENCY DEBUG - New value length:", selectedValue ? selectedValue.length : 'null/undefined');

  // Show checkbox only if a brand style is actually selected (not empty string which is "Optional")
  if (selectedValue && selectedValue !== "") {
    console.log("🔍 BRAND CONSISTENCY DEBUG - Showing checkbox on change");
    // Show brand consistency checkbox when a brand style is selected
    brandConsistencyContainer.attr('style', 'display: inline-flex !important;');
  } else {
    console.log("🔍 BRAND CONSISTENCY DEBUG - Hiding checkbox on change");
    // Hide brand consistency checkbox when "Optional" is selected (empty value)
    brandConsistencyContainer.attr('style', 'display: none !important;');
    // Uncheck the brand consistency checkbox when hiding it to prevent confusion
    $("#on_brand").prop("checked", false);
  }
});
$('.day-checkbox').change(function () {
  let selectedDays = [];
  $('input[name="hide_set_of_days"]:checked').each(function () {
    selectedDays.push($(this).val());
  });
  $('#hide_set_of_days_hidden, #hide_set_of_days_hidden_popup').val(selectedDays.join(','));
  updateFormState();
});
$('.goal-checkbox').change(function () {
  var selectedGoals = [];
  $('.goal-checkbox:checked').each(function () {
    selectedGoals.push($(this).val());
  });
  // OPTIMIZATION: Add debug logging for goals serialization
  const goalsJsonString = JSON.stringify(selectedGoals);
  console.log("🚀 GOALS DEBUG - Selected goals array:", selectedGoals);
  console.log("🚀 GOALS DEBUG - Goals JSON string length:", goalsJsonString.length, "characters");
  $("#goalsHidden").val(goalsJsonString);
  if ($('#goal_other').is(':checked')) {
    $("#otherGoalContainer").slideDown();
  } else {
    $("#otherGoalContainer").slideUp();
  }
  updateFormState();
});
$("#settings-container").find("select, textarea, input").on("input change", updateFormState);
$("#block-settings").find("select, textarea, input").on("input change", function() {
  if (typeof window.parent.settingsTabFieldsChanged !== 'undefined') {
    window.parent.settingsTabFieldsChanged = true;
  }
  if (typeof settingsTabFieldsChanged !== 'undefined') {
    settingsTabFieldsChanged = true;
  }
  console.log('Settings tab field changed:', this.name || this.id);
});

// Auto-syndicate domain link checkbox change handler
$("input[name='auto_syndicate_domain_link']").on('change', function() {
  const isChecked = $(this).is(':checked');
  const contentWrapper = $('.content-wrapper');

  debugLog("Auto-syndicate checkbox changed", { checked: isChecked });

  if (contentWrapper.length && contentWrapper.html().trim()) {
    let currentContent = contentWrapper.html();

    if (typeof DomainLinkSyndicationManager !== 'undefined') {
      const processedContent = DomainLinkSyndicationManager.processDomainLinkSyndication(currentContent, isChecked);

      // Only update if content actually changed
      if (processedContent !== currentContent) {
        // Temporarily pause DynamicFieldManager to prevent conflicts during content update
        let originalProcessingState = false;
        if (typeof DynamicFieldManager !== 'undefined') {
          originalProcessingState = DynamicFieldManager.isProcessing;
          DynamicFieldManager.isProcessing = true;
        }

        contentWrapper.html(processedContent);

        // Re-enable DynamicFieldManager and trigger span processing after content update
        setTimeout(function() {
          if (typeof DynamicFieldManager !== 'undefined') {
            DynamicFieldManager.isProcessing = originalProcessingState;
            // Check if the updated content has dynamic fields that need spans
            const content = contentWrapper.html();
            const hasUnwrappedFields = /\[\[\[(?:Weekday|DOTM|DSFX|MonthName|TimeOfDay|FirstName|LastName|EmailAddress|PostCode|BrandName|MastheadName)\]\]\](?!<\/span>)/.test(content);
            if (hasUnwrappedFields && !DynamicFieldManager.isProcessing) {
              console.log('🔄 Domain link syndication detected unwrapped dynamic fields, triggering span addition');
              DynamicFieldManager.addSpansToContent();
            }
          }
        }, 100);

        debugLog("Content updated by domain link syndication", {
          hadDomainLinks: DomainLinkSyndicationManager.hasDomainLinks(currentContent),
          hadSiteLinks: DomainLinkSyndicationManager.hasSiteLinks(currentContent),
          nowHasDomainLinks: DomainLinkSyndicationManager.hasDomainLinks(processedContent),
          nowHasSiteLinks: DomainLinkSyndicationManager.hasSiteLinks(processedContent)
        });
      } else {
        debugLog("No domain link changes needed", {
          hasDomainLinks: DomainLinkSyndicationManager.hasDomainLinks(currentContent),
          hasSiteLinks: DomainLinkSyndicationManager.hasSiteLinks(currentContent)
        });
      }
    } else {
      console.warn('🔗 DomainLinkSyndicationManager not available');
    }
  } else {
    debugLog("No content in wrapper to process", {});
  }
});
$(".main-block-name").on("input", function(){
  updateFormState();
  validateScreen1();
});
$("textarea[name='fromSentence']").on("input change", function() {
  updateFormState();
  if ($("input[name='content-source']:checked").val() === "none") {
      validateScreen2();
  }
});
$("#goToResultBtn").click(function(){
  moveToStep(3);
});
$("#cancel-api-btn").click(function() {
  if (currentClaudeRequest) {
    currentClaudeRequest.abort();
    currentClaudeRequest = null;
    $("#loadingModal-1a2b").data('allow-close', true).modal('hide');
    $("#generateBlockBtn").prop("disabled", false);
    $("#additionalInfoText").hide();
    $("#submitButtonExplanation").hide();
  }
});
$("#send_prompt_to_llm").on("submit", handleSubmit);

// Step Navigation Functions for Main Form
function showStep(stepNumber) {
  // Update step indicators
  $('.step-indicator-1a2b').removeClass('active');
  $(`.step-indicator-1a2b[data-step="${stepNumber}"]`).addClass('active');

  // Update step panels
  $('.step-panel-1a2b').removeClass('active');
  $(`.step-panel-1a2b[data-step="${stepNumber}"]`).addClass('active');
}

// Modal Step Navigation Functions (separate from main form)
function showModalStep(stepNumber) {
  console.log('Showing modal step:', stepNumber);

  // Update modal step panels only
  $('.modal-step-panel').removeClass('active');
  $(`.modal-step-panel[data-modal-step="${stepNumber}"]`).addClass('active');

  // Show/hide appropriate buttons
  if (stepNumber === 1) {
    // Step 1: Always show Generate button, show Go to Results if results exist
    $("#generatePromptSuggestions").show();
    if ($("#promptSuggestionsRow").children().length > 0) {
      $("#goToResultsBtn").show();
    } else {
      $("#goToResultsBtn").hide();
    }
  }
}

// Prompt Improvement Modal Functions
$("#NewPromptBtn").click(function() {
  // Check if we have existing results before deciding which step to show
  const hasExistingResults = $("#promptSuggestionsRow").children().length > 0;

  if (hasExistingResults) {
    // If results exist, go directly to results step
    showModalStep(2);
  } else {
    // If no results, start at step 1
    showModalStep(1);
  }

  // Open the modal
  $("#promptImprovementModal").modal('show');
  validatePromptImprovementForm();
});

// Back to step 1 button
$("#backToStep1").click(function() {
  showModalStep(1);
});

// Go to results button
$("#goToResultsBtn").click(function() {
  showModalStep(2);
});

// Close button handler
$("#promptModalClose").click(function() {
  $("#promptImprovementModal").modal('hide');
});

// Cancel button handlers
$("#cancelModalBtn, #cancelModalBtn2").click(function() {
  $("#promptImprovementModal").modal('hide');
});

// Character counter for brand context
$("#brandContext").on('input', function() {
  const charCount = $(this).val().length;
  $("#brandCharCount").text(charCount);
});

// Validate prompt improvement form
function validatePromptImprovementForm() {
  const currentPrompt = $("textarea[name='fromSentence']").val().trim();
  const layoutStructure = $("#layoutStructure").val();
  const visualStyle = $("#visualStyle").val();
  const contentElements = $("#contentElements").val();
  const brandContext = $("#brandContext").val().trim();
  const emailType = $("#emailType").val();

  // With smart defaults, we always have enough to generate prompts
  // Check if we have smart defaults OR custom values OR existing prompt
  const hasSmartDefaults = layoutStructure || visualStyle || contentElements;
  const hasCustomValues = brandContext !== '' || emailType !== '';
  const hasExistingPrompt = currentPrompt !== '';

  const generateBtn = $("#generatePromptSuggestions");
  const goToResultsBtn = $("#goToResultsBtn");
  const validationMsg = $("#promptValidationMessage");

  // Always enable since we have smart defaults
  if (hasSmartDefaults || hasCustomValues || hasExistingPrompt) {
    generateBtn.prop('disabled', false);
    goToResultsBtn.prop('disabled', false);
    validationMsg.hide();
  } else {
    // This should rarely happen with smart defaults
    generateBtn.prop('disabled', true);
    goToResultsBtn.prop('disabled', true);
    validationMsg.text('Ready to generate! The form has smart defaults, or you can customize the fields above.');
    validationMsg.show();
  }
}

// Add event listeners for form validation
$('.prompt-improvement-field').on('change input', validatePromptImprovementForm);

// Generate prompt suggestions
$("#generatePromptSuggestions").click(async function() {
  if ($(this).prop('disabled')) return;

  // Show loading state
  $("#promptGenerationLoading").show();
  $("#promptSuggestionsResults").hide();
  $(this).prop('disabled', true);

  try {
    // Collect ONLY the pop-up form fields for intelligent processing
    const formData = {
      emailType: $("#emailType").val(),
      layoutStructure: $("#layoutStructure").val(),
      visualStyle: $("#visualStyle").val(),
      contentElements: $("#contentElements").val(),
      brandContext: $("#brandContext").val(),
      currentPrompt: $("textarea[name='fromSentence']").val()
    };

    // Debug: Log collected pop-up form data
    console.log('Collected pop-up form data for new prompts API:', formData);

    // Simple prompt - let the backend handle the intelligent customization using form_json
    // Step 11: Extract form field values and convert them to simple, clear language
    const emailType = formData.emailType || 'newsletter';
    const layoutStructure = formData.layoutStructure || 'mixed-layouts';
    const visualStyle = formData.visualStyle || 'modern-sophisticated';
    const contentElements = formData.contentElements || 'mixed-media';
    const brandContext = formData.brandContext || '';
    const currentPrompt = formData.currentPrompt || '';

    // Step 12: Build user prompt using simplified form language instead of complex form values
    // Map complex form values to simple prompt language
    const simpleEmailType = emailType === 'newsletter' ? 'newsletter' :
                           emailType === 'promotional' ? 'promotional email' :
                           emailType === 'announcement' ? 'announcement' :
                           emailType === 'welcome' ? 'welcome email' :
                           emailType === 'event' ? 'event invitation' :
                           emailType === 'transactional' ? 'transactional email' : 'newsletter';

    const simpleLayout = layoutStructure === 'mixed-layouts' ? 'mixed column layouts' :
                        layoutStructure === 'single-column' ? 'single column layout' :
                        layoutStructure === 'two-column' ? 'two column layout' :
                        layoutStructure === 'three-column' ? 'three column layout' :
                        layoutStructure === 'grid-style' ? 'grid layout' : 'mixed column layouts';

    const simpleVisual = visualStyle === 'modern-sophisticated' ? 'modern and clean' :
                        visualStyle === 'clean-minimal' ? 'clean and minimal' :
                        visualStyle === 'professional-corporate' ? 'professional' :
                        visualStyle === 'bold-engaging' ? 'bold and engaging' :
                        visualStyle === 'elegant-premium' ? 'elegant' : 'modern and clean';

    const simpleContent = contentElements === 'mixed-media' ? 'varied content with images and buttons' :
                         contentElements === 'images-cta' ? 'images with call-to-action buttons' :
                         contentElements === 'text-heavy' ? 'text-heavy with headlines' :
                         contentElements === 'product-showcase' ? 'product showcase with pricing' :
                         contentElements === 'feature-highlights' ? 'feature highlights with icons' : 'varied content with images and buttons';

    // Use the working example as the template with minimal guidance
    let promptText = `Here's a working email template prompt that generates great results:

"You are a creative email editor for an Australian news brand. Give me a visually styling content block template filled with rows that have one, two and three columns mixed across. The embedded elements should have different background colours, smart use of border colours in certain corners, links and image placeholders and call to action buttons. Really impress me with the overall design and adhere to the column and row instructions! Really keep it modern looking though and don't overwhelm. Use image and hyperlink placeholders for visual variety and also borders scarcely as needed. Aim for a varied template with a good mix of all the above elements. Make sure every element is populated with some kind of placeholder content."

Create 3 similar prompts for ${simpleEmailType} templates. Keep the same conversational, direct style but vary the layout focus:

1. Make one focused on balanced, even column layouts
2. Make one focused on uneven, asymmetrical layouts
3. Make one that mixes both approaches

Each should be natural and conversational like the example, around 100-150 words.`;

    // Add current prompt context if provided
    if (currentPrompt.trim()) {
      promptText += ` Incorporate ideas from: ${currentPrompt.trim()}`;
    }

    // Step 36: Log simplified form value mappings for debugging
    console.log('Form field mappings:', {
      original: { emailType, layoutStructure, visualStyle, contentElements },
      simplified: { simpleEmailType, simpleLayout, simpleVisual, simpleContent }
    });

    // Step 37-39: Verify user prompt uses simple, clear language and reasonable length
    console.log('Final user prompt length:', promptText.length);
    console.log('Final user prompt:', promptText);

    // Make API call with 'new_prompts' type and ALL form data
    const result = await makeNewPromptsAPICall(promptText, formData);

    if (result.success) {
      displayPromptSuggestions(result.suggestions);
    } else {
      throw new Error(result.error || 'Failed to generate suggestions');
    }

  } catch (error) {
    console.error('Error generating new prompts:', error);
    alert('Error generating new prompts: ' + error.message);
  } finally {
    $("#promptGenerationLoading").hide();
    $(this).prop('disabled', false);
  }
});

// Make API call for new prompts
async function makeNewPromptsAPICall(promptText, formData = {}) {
  try {
    const requestData = {
      type: 'new_prompts',
      prompt: promptText,
      user_prompt: promptText,
      temperature: 30,
      model: 'claude-3-5-sonnet-v2@20241022',
      capi_id: '',
      cb_id: '',
      content_source: 'none',
      table_structure: '',
      form_json: formData  // Send pop-up form data to backend for intelligent processing
    };

    // Use the exact same API URL pattern as the working Claude calls
    const apiUrl = 'https://cloud.e.newsdigitalmedia.com.au/dr_%%=v(@appStateEnvironment)=%%_ai_pop-up_api?sys_env=%%=v(@environmentAppCentre)=%%&c=%%=v(@cookie)=%%&feature_branch=%%=v(@feature_branch)=%%&biz_env=%%=v(@appStateEnvironment)=%%&content_id=%%=v(@de_eca_EventID)=%%';

    // OPTIMIZATION: Add comprehensive payload debugging for new_prompts
    const newPromptsPayload = JSON.stringify(requestData);
    console.log("🚀 NEW_PROMPTS PAYLOAD DEBUG - Request data:", requestData);
    console.log("🚀 NEW_PROMPTS PAYLOAD DEBUG - Payload size:", newPromptsPayload.length, "characters");
    console.log("🚀 NEW_PROMPTS PAYLOAD DEBUG - Payload preview (first 500 chars):", newPromptsPayload.substring(0, 500));
    console.log("🚀 NEW_PROMPTS PAYLOAD DEBUG - Payload preview (last 500 chars):", newPromptsPayload.substring(Math.max(0, newPromptsPayload.length - 500)));

    return new Promise((resolve, reject) => {
      $.ajax({
        url: apiUrl,
        type: 'POST',
        data: newPromptsPayload,
        contentType: 'application/json;charset=UTF-8',
        dataType: 'text',
        success: function(rawResponse) {
          console.log('New prompts API - Successful raw response received');
          console.log('Response length:', rawResponse ? rawResponse.length : 0);

          if (!rawResponse || rawResponse.trim() === '') {
            console.error('Empty response received from prompt improvement API');
            reject(new Error('Empty response received from API'));
            return;
          }

          try {
            const data = JSON.parse(rawResponse);
            console.log('New prompts API - Parsed response:', data);

            // Handle error responses like the working Claude API
            if (data.form_message && data.message_color === 'danger') {
              console.error('Access error detected:', data.form_message);
              reject(new Error(data.form_message));
              return;
            }

            if ((data.error === 'Session expired' && data.message) ||
                (data.message && data.message.includes('session has expired'))) {
              console.error('Session expired error detected:', data.message || data.error);
              reject(new Error(data.message || data.error));
              return;
            }

            if (data.error) {
              console.error('Error from new prompts API:', data.error);
              reject(new Error(data.error));
              return;
            }

            if (!data.content) {
              console.error('No content field in new prompts API response:', data);
              reject(new Error('No content received from API'));
              return;
            }

            // Decode the base64 content using the same decoders as working Claude API
            let decodedContent;
            try {
              decodedContent = base64DecodeUTF8(data.content);
              console.log('Successfully decoded new prompts content');
            } catch (decodeError) {
              console.warn('UTF-8 decode failed, falling back to Unicode decoder:', decodeError);
              try {
                decodedContent = base64DecodeUnicode(data.content);
                console.log('Successfully decoded content with Unicode decoder');
              } catch (unicodeError) {
                console.error('Both decoders failed:', unicodeError);
                reject(new Error('Failed to decode response content'));
                return;
              }
            }

            console.log('Decoded new prompts content:', decodedContent);
            console.log('Content length:', decodedContent.length);
            console.log('First 500 characters:', decodedContent.substring(0, 500));
            const suggestions = parsePromptSuggestions(decodedContent);
            console.log('Parsed suggestions:', suggestions);
            console.log('Number of suggestions found:', suggestions.length);
            resolve({ success: true, suggestions: suggestions });

          } catch (parseError) {
            console.error('JSON parse error in prompt improvement API:', parseError);
            console.error('Raw response that caused parse error:', rawResponse);
            reject(new Error('Invalid JSON response from API'));
          }
        },
        error: function(xhr, status, error) {
          console.error('AJAX error in new prompts API:', status, error);
          console.error('XHR response:', xhr.responseText);

          if (status === 'abort') {
            reject(new Error('Request canceled'));
            return;
          }

          let errorMessage = '';
          if (xhr.status === 0) {
            errorMessage = 'Network error occurred';
          } else if (xhr.status >= 500) {
            errorMessage = `Server error (${xhr.status})`;
          } else if (xhr.status >= 400) {
            errorMessage = `Request error (${xhr.status})`;
          } else {
            errorMessage = `Unknown error: ${error}`;
          }

          reject(new Error(errorMessage));
        }
      });
    });

  } catch (error) {
    console.error('API call setup error:', error);
    return { success: false, error: error.message };
  }
}

// Parse prompt suggestions from API response
function parsePromptSuggestions(content) {
  console.log('Parsing content:', content.substring(0, 200));
  const suggestions = [];

  // Method 1: Try to find numbered prompts with regex (ORIGINAL WORKING VERSION)
  const numberedMatches = content.match(/(\d+)\.\s*([\s\S]*?)(?=\d+\.\s*|$)/g);
  if (numberedMatches && numberedMatches.length > 0) {
    console.log('Found numbered matches:', numberedMatches.length);
    numberedMatches.forEach((match, index) => {
      const cleanMatch = match.replace(/^\d+\.\s*/, '').trim();
      if (cleanMatch && index < 3) {
        suggestions.push({
          number: index + 1,
          text: cleanMatch
        });
      }
    });
  }

  // Method 2: If no numbered matches, try splitting by numbers (ORIGINAL)
  if (suggestions.length === 0) {
    console.log('No numbered matches found, trying split method');
    const parts = content.split(/\d+\.\s*/);
    for (let i = 1; i < parts.length && i <= 3; i++) {
      const part = parts[i].trim();
      if (part) {
        suggestions.push({
          number: i,
          text: part
        });
      }
    }
  }

  // Method 3: If still no matches, treat entire content as one suggestion (ORIGINAL)
  if (suggestions.length === 0) {
    console.log('No structured content found, using entire content as single suggestion');
    const cleanContent = content.trim();
    if (cleanContent) {
      suggestions.push({
        number: 1,
        text: cleanContent
      });
    }
  }

  console.log('Final parsed suggestions:', suggestions);
  return suggestions.slice(0, 3); // Ensure max 3 suggestions
}

// Display prompt suggestions in step 2
function displayPromptSuggestions(suggestions) {
  const container = $("#promptSuggestionsRow");
  container.empty();

  if (suggestions.length === 0) {
    container.html('<div class="col-12"><p class="text-muted text-center">No suggestions generated. Please try again.</p></div>');
    showModalStep(2);
    return;
  }

  // Container already has class="row", so append columns directly
  suggestions.forEach((suggestion, index) => {
    const suggestionHtml = `
      <div class="col-md-4 mb-3">
        <div class="card h-100" style="border: 1px solid #dee2e6;">
          <div class="card-body d-flex flex-column">
            <h6 class="card-title text-center mb-3" style="color: #007bff; font-weight: 600;">Option ${suggestion.number || (index + 1)}</h6>
            <p class="card-text flex-grow-1" style="color: #495057; line-height: 1.5; overflow-y: auto; max-height: 200px; font-size: 0.9rem; margin-bottom: 1rem;">${suggestion.text}</p>
            <div class="text-center mt-auto">
              <button type="button" class="nav-button-1a2b text-button-1a2b AI_create_and_edit-1a2b select-suggestion-btn" data-suggestion="${index}" style="width: auto; padding: 0.375vw 0.75vw;">
                Select This Prompt
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    container.append(suggestionHtml);
  });

  // Add click handlers for selection
  $('.select-suggestion-btn').click(function() {
    const suggestionIndex = $(this).data('suggestion');
    const selectedText = suggestions[suggestionIndex].text;

    // Insert the selected prompt into the main textarea
    $("textarea[name='fromSentence']").val(selectedText);

    // Clear any existing error messages and re-validate
    $("#error-message-container").hide();
    $("#submitButtonExplanation").hide();
    $("#generateBlockBtn").prop('disabled', false);
    validateScreen2();

    // Close the modal
    $("#promptImprovementModal").modal('hide');

    // Show success message
    show_client_side_message('New prompt selected successfully!', 'success');
  });

  // Move to modal step 2 to show results
  showModalStep(2);
}
function populateOptionsContainer(options, container, selectElement, searchInput) {
  container.empty();
  if (options.length === 0) {
    container.append('<div class="dropdown-item" style="padding: 8px 12px; cursor: default;">No matching options</div>');
    return;
  }
  options.forEach(option => {
    const optionElement = $('<div class="dropdown-item" style="padding: 8px 12px; cursor: pointer;"></div>')
      .text(option.text)
      .data('value', option.value);
    optionElement.hover(
      function() { $(this).css('background-color', '#f8f9fa'); },
      function() { $(this).css('background-color', ''); }
    );
    optionElement.click(function() {
      const selectedValue = $(this).data('value');
      const selectedText = $(this).text();
      selectElement.val(selectedValue).trigger('change');
      searchInput.val(selectedText);
      container.hide();
      $("#generateBlockBtn").prop("disabled", false);
      $("#additionalInfoText").hide();
      $("#submitButtonExplanation").hide();
      updateFormState();
    });
    container.append(optionElement);
  });
}

$("#capi_id_dropdown, #cb_id_dropdown").on("change", function(){
  var selectedVal = $(this).val();
  if(selectedVal && selectedVal !== ""){
    $("#generateBlockBtn").prop("disabled", false);
    $("#additionalInfoText").hide();
  }
});
function checkPreviewSelectAvailability() {
  var hasContent = $.trim($(".content-wrapper").html()) !== "";
  var $selectionRadio = $("input[name='content-source'][value='selection']");
  var $selectionLabel = $selectionRadio.closest('label');

  $selectionRadio.prop('disabled', !hasContent);

  if (!hasContent) {
    $selectionRadio.prop('checked', false);
    if ($("input[name='content-source']:checked").val() === 'selection') {
      $("input[name='content-source'][value='none']").prop('checked', true);
    }

    // Add disabled styling and tooltip
    $selectionLabel.addClass('content-preview-disabled')
                   .removeClass('ai-content-preview-active')
                   .attr('data-tooltip', 'Right-click on content in the Content Preview and select "Edit with AI" to enable this option');
  } else {
    // Remove disabled styling
    $selectionLabel.removeClass('content-preview-disabled')
                   .removeAttr('data-tooltip');
  }
}

function initializeContentDeselection() {
  $(document).on("click.aiDeselectCheck", function(e) {
    setTimeout(() => {
      if ($("input[name='content-source'][value='selection']").is(':checked')) {
        console.log("Correcting radio button state after deselection.");
        var $selectionRadio = $("input[name='content-source'][value='selection']");
        var $selectionLabel = $selectionRadio.closest('label');

        $selectionRadio.prop("checked", false);
        $selectionLabel.removeClass('ai-content-preview-active'); // Remove AI styling
        checkPreviewSelectAvailability();
        $("input[name='content-source'][value='none']").prop("checked", true).trigger('change');
      }
    }, 50);
  });
}

function setupContextMenuEditAction() {
  function updateAIEditUI() {
    // Check if settings reuse modal should be shown
    if (typeof window.SettingsReuseModalManager !== 'undefined') {
      const shouldShowModal = window.SettingsReuseModalManager.checkAndShowModalIfNeeded();
      if (shouldShowModal) {
        console.log("📋 Settings reuse modal shown - Edit with AI will proceed after user choice");
        return; // Exit here, modal will handle the rest
      }
    }

    // Original Edit with AI logic
    proceedWithEditWithAI();
  }

  function proceedWithEditWithAI() {
    var $generateTab = $("#about-tab");
    if ($generateTab.length) {
      console.log("Activating Generate tab");
      $generateTab.tab('show');
      $("#about").addClass('show active');
      $("#block-settings, #previous-saves, #clipboard, #content-library").removeClass('show active');
      $("#about-tab").addClass('active');
      $("#block-settings-tab, #previous-saves-tab, #clipboard-tab, #content-library-tab").removeClass('active');
    }
    setTimeout(function() {
      if (typeof moveToStep === 'function') {
        console.log("Moving to step 2");
        moveToStep(2);
        setTimeout(function() {
          var $radio = $("input[name='content-source'][value='selection']");
          if ($radio.length) {
            console.log("Selecting 'from editor' radio button");
            $radio.prop("disabled", false)
              .prop("checked", true)
              .trigger('change');
            $("#generateBlockBtn").prop('disabled', false);
            $("#submitButtonExplanation").hide();

            // Add AI styling to the Content Preview label
            var $label = $radio.closest('label');
            if ($label.length) {
              $label.addClass('ai-content-preview-active');
              console.log("Added AI styling to Content Preview label");
            }
          } else {
            console.warn("Selection radio button not found.");
          }
        }, 100);
      } else {
        console.warn("moveToStep function not found.");
      }
    }, 100);
  }

  // Export both functions globally
  window.updateAIEditUI = updateAIEditUI;
  window.proceedWithEditWithAI = proceedWithEditWithAI;
}

/**
 * Function to restore event handling for highlight wrapper after tab navigation
 */
function restoreHighlightEventHandling(highlightWrapper) {
  if (!highlightWrapper) return;

  console.log('Restoring event handling for highlight wrapper');

  // Ensure the wrapper maintains its visual properties
  if (!highlightWrapper.style.animation) {
    highlightWrapper.style.animation = 'aiGradientBorder 30s linear infinite';
  }

  // The click handling is managed by the global handlers, so no need to add specific handlers here
  // Just ensure the wrapper is properly marked
  highlightWrapper.setAttribute('data-ai-highlight', 'true');
}

/**
 * Function to monitor highlight status and show/hide Replace Selection button
 */
function monitorReplaceSelectionButtonVisibility() {
  let lastState = null; // Track last state to avoid unnecessary updates

  function updateReplaceSelectionButtonVisibility() {
    // Check for highlight wrapper in DOM as backup
    const highlightWrapperInDOM = document.querySelector('.content-wrapper .ai-highlight-wrapper');
    const hasActiveHighlight = (window.selectedForAIEditingElement && window.aiHighlightWrapper) || highlightWrapperInDOM;
    const contentSource = $("input[name='content-source']:checked").val();
    const isSelectionMode = (contentSource === 'selection');
    const shouldShowButton = hasActiveHighlight && isSelectionMode;

    // Create state signature to avoid unnecessary updates
    const currentState = `${!!hasActiveHighlight}-${isSelectionMode}-${contentSource}`;

    // Only proceed if state has changed
    if (lastState === currentState) {
      return;
    }

    lastState = currentState;

    // console.log('Replace Selection Button State Change:', {
    //   hasHighlight: !!hasActiveHighlight,
    //   isSelectionMode: isSelectionMode,
    //   contentSource: contentSource,
    //   shouldShow: shouldShowButton
    // });

    const replaceButton = $('#replace_selection_step3');
    if (replaceButton.length) {
      if (shouldShowButton) {
        replaceButton.show();

        // If we found highlight in DOM but references are missing, restore them
        if (highlightWrapperInDOM && (!window.selectedForAIEditingElement || !window.aiHighlightWrapper)) {
          console.log('Restoring highlight references from DOM');
          window.aiHighlightWrapper = highlightWrapperInDOM;
          window.selectedForAIEditingElement = highlightWrapperInDOM.firstElementChild;

          // Ensure the highlight wrapper has proper event handling
          restoreHighlightEventHandling(highlightWrapperInDOM);
        }
      } else {
        replaceButton.hide();

        // Reset radio button to "New" when button legitimately disappears
        if (!hasActiveHighlight && isSelectionMode) {
          console.log('Resetting content-source to "none" - no active highlight');
          $("input[name='content-source'][value='none']").prop('checked', true).trigger('change');
        }
      }
    }
  }

  // Efficient monitoring with debounced updates
  let updateTimeout;
  const debouncedUpdate = function() {
    clearTimeout(updateTimeout);
    updateTimeout = setTimeout(updateReplaceSelectionButtonVisibility, 50);
  };

  // Monitor changes to highlight status (more targeted)
  const observer = new MutationObserver(function(mutations) {
    let shouldUpdate = false;
    mutations.forEach(function(mutation) {
      // Only check for highlight wrapper changes
      if (mutation.type === 'childList') {
        const addedNodes = Array.from(mutation.addedNodes);
        const removedNodes = Array.from(mutation.removedNodes);

        if (addedNodes.some(node => node.classList && node.classList.contains('ai-highlight-wrapper')) ||
            removedNodes.some(node => node.classList && node.classList.contains('ai-highlight-wrapper'))) {
          shouldUpdate = true;
        }
      }
    });

    if (shouldUpdate) {
      debouncedUpdate();
    }
  });

  // Observe only the content wrapper for highlight changes
  const contentWrapper = document.querySelector('.content-wrapper');
  if (contentWrapper) {
    observer.observe(contentWrapper, {
      childList: true,
      subtree: true
    });
  }

  // Monitor radio button changes (essential)
  $(document).on('change', 'input[name="content-source"]', debouncedUpdate);

  // Monitor Generate tab activation (essential)
  $('#about-tab').on('shown.bs.tab', debouncedUpdate);

  // Initial check
  updateReplaceSelectionButtonVisibility();

  // Store the function globally for manual calls
  window.updateReplaceSelectionButtonVisibility = updateReplaceSelectionButtonVisibility;
}

setupContextMenuEditAction();
monitorReplaceSelectionButtonVisibility();

// Note: Highlight removal is handled by the main editor file's click handlers
// No additional global click handler needed here to avoid conflicts

updateFormState();
validateScreen1();
validateScreen2();
$(document).ready(function() {
  setupContextMenuEditAction();
  $("input[name='columns'], input[name='rows'], #stack_content_vertical, #extra_spacing_in_elements").on("input change", function() {
    updateFormState();
  });
  $(document).on("click", "#close-api-btn", function() {
    console.log("Close button clicked");
    $("#loadingModal-1a2b").data('allow-close', true).modal('hide');
    isCancelled = false;
  });
  updateFormState();
  function initializeSearchableDropdown(container) {
    const selectElement = container.find('select');
    const searchInput = container.find('.search-input');
    const optionsContainer = container.find('.dropdown-options-container');
    const options = [];
    selectElement.find('option').each(function() {
      if ($(this).val() !== '' && !$(this).prop('disabled')) {
        options.push({
          value: $(this).val(),
          text: $(this).text()
        });
      }
    });
    populateOptionsContainer(options, optionsContainer, selectElement, searchInput);
    searchInput.focus(function() {
      optionsContainer.show();
    });
    searchInput.on('input', function() {
      const searchTerm = $(this).val().toLowerCase();
      const filteredOptions = options.filter(option =>
        option.text.toLowerCase().includes(searchTerm) ||
        option.value.toLowerCase().includes(searchTerm)
      );
      populateOptionsContainer(filteredOptions, optionsContainer, selectElement, searchInput);
      optionsContainer.show();
    });
    $(document).click(function(e) {
      if (!container.is(e.target) && container.has(e.target).length === 0) {
        optionsContainer.hide();
      }
    });
  }
  initializeSearchableDropdown($("#capiSelection"));
  initializeSearchableDropdown($("#existingBlockSelection"));
  $("#capi_id_dropdown, #cb_id_dropdown").on("change", function(){
    updateFormState();
    validateScreen2();
  });
});

/* ==================== Event Handlers End ==================== */


/* ==================== AI Results Functions Start ==================== */

function reusePromptSettings(eventId) {
  console.log('Reusing prompt settings for event:', eventId);
  var selectedData = window.selectedAIResultData;
  if (!selectedData || !selectedData.form_json) {
    console.error('No form data available to reuse');
    show_client_side_message('No form data available to reuse from this AI generation.', 'warning');
    return;
  }
  try {
    var decodedFormJson;
    try {
      decodedFormJson = base64DecodeUTF8(selectedData.form_json);
      console.log('Successfully decoded form JSON with UTF-8 decoder');
    } catch (decodeError) {
      console.warn('UTF-8 decode failed, falling back to Unicode decoder:', decodeError);
      try {
        decodedFormJson = base64DecodeUnicode(selectedData.form_json);
        console.log('Successfully decoded form JSON with Unicode decoder');
      } catch (unicodeError) {
        console.error('Both decoders failed, falling back to basic atob:', unicodeError);
        decodedFormJson = atob(selectedData.form_json);
      }
    }
    var formData = JSON.parse(decodedFormJson);
    console.log('Parsed form data:', formData);
    $('.nav-tabs a[href="#about"]').tab('show');
    setTimeout(function() {
      populateFormFields(formData);
      moveToStep(2);
      show_client_side_message('Prompt settings have been applied successfully!', 'success');
    }, 100);
  } catch (error) {
    console.error('Error parsing form data:', error);
    show_client_side_message('Error loading prompt settings. Please try again.', 'danger');
  }
}

function populateFormFields(formData) {
  console.log('Populating form fields with:', formData);
  if (formData.fromSentence) {
    var sanitizedPrompt = sanitizeUTF8(formData.fromSentence);
    $("textarea[name='fromSentence']").val(sanitizedPrompt);
  }
  if (formData.columns) {
    $("input[name='columns']").val(formData.columns);
  }
  if (formData.rows) {
    $("input[name='rows']").val(formData.rows);
  }
  if (formData.brandCssStyle) {
    $("select[name='brandCssStyle']").val(formData.brandCssStyle);
    // Trigger the change event to handle brand consistency checkbox visibility
    // This ensures the checkbox is shown/hidden correctly when reusing prompt settings
    $("select[name='brandCssStyle']").trigger('change');
  }
  if (formData.creativityLevel) {
    $("input[name='creativityLevel']").val(formData.creativityLevel);
    $("#creativityValue").text(formData.creativityLevel + '%');
  }
  if (formData.aiModel) {
    $("select[name='aiModel']").val(formData.aiModel);
  }
  if (formData.contentSource) {
    $("input[name='content-source'][value='" + formData.contentSource + "']").prop('checked', true);
    $("input[name='content-source'][value='" + formData.contentSource + "']").trigger('change');
  }
  if (formData.capiId) {
    $("select[name='capi_id']").val(formData.capiId);
  }
  if (formData.cbId) {
    $("select[name='cb_id']").val(formData.cbId);
  }
  if (formData.goals && Array.isArray(formData.goals)) {
    $('.goal-checkbox').prop('checked', false);
    formData.goals.forEach(function(goal) {
      $('.goal-checkbox[value="' + goal + '"]').prop('checked', true);
    });
  }
  if (formData.otherGoalDescription) {
    var sanitizedDescription = sanitizeUTF8(formData.otherGoalDescription);
    $("textarea[name='otherGoalDescription']").val(sanitizedDescription);
  }
  if (formData.stackContentVertical !== undefined) {
    $("#stack_content_vertical").prop('checked', formData.stackContentVertical);
  }
  if (formData.extraSpacingInElements !== undefined) {
    $("#extra_spacing_in_elements").prop('checked', formData.extraSpacingInElements);
  }
  console.log('Form fields populated successfully');
}

/* ==================== AI Results Functions End ==================== */


/* ==================== BLock settings start ==================== */

document.querySelectorAll('.tab-link').forEach(function(tab) {
  tab.addEventListener('click', function() {
    document.querySelectorAll('.tab-link').forEach(function(item) {
      item.classList.remove('active');
    });
    document.querySelectorAll('.tab-content').forEach(function(content) {
      content.classList.remove('active');
    });
    this.classList.add('active');
    document.getElementById(this.getAttribute('data-tab')).classList.add('active');
  });
});
function show_or_hide_checkbox(checkboxId, fieldId) {
  var checkbox = document.getElementById(checkboxId);
  var field = document.getElementById(fieldId);
  if (checkbox.checked) {
    field.classList.add('show');
  } else {
    field.classList.remove('show');
  }
}

function initializeDateFieldVisibility() {
  var hideAfterCheckbox = document.getElementById('hide_after_date_checkbox');
  var hideAfterField = document.getElementById('hide_after_date_field');
  if (hideAfterCheckbox && hideAfterField) {
    if (hideAfterCheckbox.checked) {
      hideAfterField.classList.add('show');
    } else {
      hideAfterField.classList.remove('show');
    }
  }
  var hideBeforeCheckbox = document.getElementById('hide_before_date_checkbox');
  var hideBeforeField = document.getElementById('hide_before_date_field');
  if (hideBeforeCheckbox && hideBeforeField) {
    if (hideBeforeCheckbox.checked) {
      hideBeforeField.classList.add('show');
    } else {
      hideBeforeField.classList.remove('show');
    }
  }
}

$(document).ready(function() {
  initializeDateFieldVisibility();
});
document.querySelectorAll('.sync-field').forEach(function(input) {
  input.addEventListener('input', function() {
    var fieldName = this.getAttribute('name');
    var modalField = document.querySelector('#aiModal-1a2b .sync-field-popup[name="'+fieldName+'"]');
    if (modalField) {
      modalField.value = this.value;
    }
  });
});
document.querySelectorAll('.sync-field[type="checkbox"]').forEach(function(checkbox) {
  checkbox.addEventListener('change', function() {
    var fieldName = this.getAttribute('name');
    var modalCheckbox = document.querySelector('#aiModal-1a2b .sync-field-popup[name="'+fieldName+'"]');
    if (modalCheckbox) {
      modalCheckbox.checked = this.checked;
    }
  });
});
document.querySelectorAll('#aiModal-1a2b .sync-field-popup').forEach(function(input) {
  input.addEventListener('input', function() {
    var fieldName = this.getAttribute('name');
    var mainField = document.querySelector('#settings-container .sync-field[name="'+fieldName+'"]');
    if (mainField) {
      mainField.value = this.value;
    }
  });
});
document.querySelectorAll('#aiModal-1a2b .sync-field-popup[type="checkbox"]').forEach(function(checkbox) {
  checkbox.addEventListener('change', function() {
    var fieldName = this.getAttribute('name');
    var mainCheckbox = document.querySelector('#settings-container .sync-field[name="'+fieldName+'"]');
    if (mainCheckbox) {
      mainCheckbox.checked = this.checked;
    }
  });
});
document.addEventListener('DOMContentLoaded', () => {
  const modal = document.getElementById('aiModal-1a2b');
  if (modal) {
    modal.addEventListener('shown.bs.modal', () => {
      modal.style.paddingLeft = '0';
    });
  }
  // Removed problematic click listeners that were automatically changing radio buttons
});

/* ==================== Block Settings End ==================== */

</script>