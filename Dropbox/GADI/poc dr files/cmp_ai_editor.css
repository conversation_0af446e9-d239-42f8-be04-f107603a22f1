<style>

/* Mobile and Desktop View Styles */

.desktop-view {
  display: block !important;
  width: 100% !important;
  border: 1px solid rgb(222, 226, 230) !important;
  border-radius: var(--border-radius, 0.42vw) !important;
  margin-bottom: 1vw !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
  height: 18.75vw !important;
  position: relative !important;
  padding: 0px !important;
  margin-top: -19.25vw !important;
}

/* Save Preview Mobile and Desktop View Styles */
.save-preview-mobile-view {
  display: block !important;
  border: none !important;
  border-radius: var(--border-radius, 0.42vw) !important;
  margin-bottom: 1.1vw !important;
  position: absolute !important;
  top: 0 !important;
  left: 50% !important;
  height: 100% !important;
  min-height: 25vw !important;
  padding: 0px !important;
  width: 480px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  transform: translateX(-50%) !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
}

/* Specific class for clipboard preview in mobile view */
.clipboard-preview-mobile-view {
  display: block !important;
  border: 8px solid #333 !important;
  border-top-width: 20px !important;
  border-bottom-width: 20px !important;
  margin-bottom: 1.1vw !important;
  height: 18.85vw !important;
  position: relative !important;
  padding: 0px !important;
  margin-top: 0 !important;
  width: 480px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  left: 25% !important;
  transform: translateX(-50%) !important;
  overflow-x: hidden !important;
  background-color: #f8f9fa !important;
  max-width: 100% !important;
  z-index: 10 !important;
}

/* Container styles for mobile view */
.clipboard-container-mobile-view {
  background-color: #f8f9fa !important;
  overflow-x: hidden !important;
  text-align: center !important;
  position: relative !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

/* Container styles for desktop view */
.clipboard-container-desktop-view {
  background-color: #f8fafc !important;
}

/* Add notch to mobile view */
.clipboard-preview-mobile-view::before {
  content: "" !important;
  position: absolute !important;
  top: -12px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 60px !important;
  height: 4px !important;
  background-color: #222 !important;
  border-radius: 4px !important;
  z-index: 10 !important;
}

/* Mobile iframe content styles */
.mobile-iframe-content {
  max-width: 100% !important;
  overflow-x: hidden !important;
  margin: 0 auto !important;
}

/* Library Tab Styles */
#library-content {
  height: 100%;
  overflow-y: auto;
}

#library-content .library-search-bar {
  margin-bottom: 1rem;
  width: 100%;
}

#library-content .library-table {
  width: 100%;
  border-collapse: collapse;
}

#library-content .library-table th,
#library-content .library-table td {
  padding: 0.5rem;
  text-align: center;
  border: 1px solid #dee2e6;
}

#library-content .library-table th {
  background-color: #f8f9fa;
}

#library-content .library-preview {
  margin-top: 1rem;
  border: 1px solid #dee2e6;
  padding: 1rem;
  background-color: #f8f9fa;
}

#library-content .library-preview-content {
  max-height: 300px;
  overflow-y: auto;
  background-color: white;
  padding: 1rem;
  border: 1px solid #dee2e6;
}

/* Mobile view class for iframe */
.mobileViewClass {
  width: 450px !important;
  position: relative !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  overflow-x: hidden !important;
  margin: 0 auto !important;
  display: block !important;
}

/* Specific mobile view class for clipboard iframe */
.clipboard-mobile-iframe {
  width: 480px !important;
  position: relative !important;
  left: 30% !important;
  transform: translateX(-50%) !important;
  overflow-x: hidden !important;
  margin: 0 auto !important;
  display: block !important;
  border: none !important;
  max-width: 100% !important;
  z-index: 10 !important;
}

.mobileViewClass table {
  table-layout: fixed !important;
  width: 100% !important;
}

.save-preview-desktop-view {
  display: block !important;
  width: 100% !important;
  border: none !important;
  border-radius: var(--border-radius, 0.42vw) !important;
  margin-bottom: 1vw !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  height: 100% !important;
  padding: 0px !important;
  overflow-x: hidden !important;
  overflow-y: auto !important;
}

/* Specific class for clipboard preview in desktop view */
.clipboard-preview-desktop-view {
  display: block !important;
  width: 100% !important;
  border: 1px solid rgb(222, 226, 230) !important;
  border-radius: var(--border-radius, 0.42vw) !important;
  margin-bottom: 1vw !important;
  transform: none !important;
  height: 18.75vw !important;
  position: relative !important;
  padding: 0px !important;
  margin-top: 0 !important;
  overflow-x: hidden !important;
  top: 0 !important;
  left: 0 !important;
}

/* Shared content block preview in desktop view */
.shared-preview-desktop-view {
  display: block !important;
  width: 100% !important;
  border: 1px solid rgb(222, 226, 230) !important;
  border-radius: var(--border-radius, 0.42vw) !important;
  margin-bottom: 1vw !important;
  transform: none !important;
  height: 300px !important;
  position: relative !important;
  padding: 0px !important;
  margin-top: -300px !important;
  overflow-x: hidden !important;
  top: 0 !important;
  left: 0 !important;
}

/* Shared content block preview in mobile view */
.shared-preview-mobile-view {
  display: block !important;
  margin-bottom: 15px !important;
  height: 300px !important;
  position: relative !important;
  padding: 0px !important;
  margin-top: -300px !important;
  width: 480px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  left: 40% !important;
  transform: translateX(-50%) !important;
  overflow-x: hidden !important;
  background-color: #f8f9fa !important;
  max-width: 100% !important;
  z-index: 10 !important;
}

/* AI Results preview in desktop view */
.ai-results-preview-desktop-view {
  display: block !important;
  width: 100% !important;
  border: 1px solid rgb(222, 226, 230) !important;
  border-radius: var(--border-radius, 0.42vw) !important;
  margin-bottom: 1vw !important;
  transform: none !important;
  height: 100% !important;
  position: absolute !important;
  padding: 0px !important;
  margin-top: 0 !important;
  overflow: hidden !important;
  top: 0 !important;
  left: 0 !important;
}

/* AI Results preview in mobile view */
.ai-results-preview-mobile-view {
  display: block !important;
  border-top-width: 20px !important;
  border-bottom-width: 20px !important;
  margin-bottom: 1.1vw !important;
  height: 100% !important;
  position: absolute !important;
  padding: 0px !important;
  margin-top: 0 !important;
  width: 375px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  overflow: hidden !important;
  background-color: #f8f9fa !important;
  max-width: 100% !important;
  z-index: 10 !important;
  border-radius: 20px !important;
}

/* Add notch to AI Results mobile view */
.ai-results-preview-mobile-view::before {
  content: "" !important;
  position: absolute !important;
  top: -12px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 60px !important;
  height: 4px !important;
  background-color: #222 !important;
  border-radius: 4px !important;
  z-index: 10 !important;
}

.btn {
  display: inline-block;
  font-weight: 400;
  color: #212529;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: transparent;
  border: 0.07vw solid transparent;
  padding: 0.375vw 0.75vw;
  font-size: 1vw;
  line-height: 1.5;
  border-radius: 0.25vw;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

table {
  border-collapse: unset;
}

.btn {
  background-color: #0070d2;
  color: white;
  font-weight: normal;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  text-align: left;
  border-radius: 0.63vw;
}
.btn:hover {
  background-color: #005fb2;
}
.btn:active {
  background-color: #004c8d;
}
.btn:focus {
  outline: none;
}

.text-primary {
  color: #007bff !important;
}

.mce-item-table:not([border]),
.mce-item-table:not([border]) caption,
.mce-item-table:not([border]) td,
.mce-item-table:not([border]) th,
.mce-item-table[border="0"],
.mce-item-table[border="0"] caption,
.mce-item-table[border="0"] td,
.mce-item-table[border="0"] th,
table[style*="border-width: 0px"],
table[style*="border-width: 0px"] caption,
table[style*="border-width: 0px"] td,
table[style*="border-width: 0px"] th {
  border: none;
}

:root {
  --blue: #007bff;
  --indigo: #6610f2;
  --purple: #6f42c1;
  --pink: #e83e8c;
  --red: #dc3545;
  --orange: #fd7e14;
  --yellow: #ffc107;
  --green: #28a745;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #fff;
  --gray: #6c757d;
  --gray-dark: #343a40;
  --primary: #007bff;
  --secondary: #6c757d;
  --success: #28a745;
  --info: #17a2b8;
  --warning: #ffc107;
  --danger: #dc3545;
  --light: #f8f9fa;
  --dark: #343a40;
  --breakpoint-xs: 0;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;
}

*,
::after,
::before {
  box-sizing: border-box;
}

code,
kbd,
pre,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;
  font-size: 1em;
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  -ms-overflow-style: scrollbar;
}

code {
  font-size: 87.5%;
  color: #e83e8c;
  word-wrap: break-word;
}

pre {
  display: block;
  font-size: 87.5%;
  color: #212529;
}

.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container-sm {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container-md,
  .container-sm {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container-lg,
  .container-md,
  .container-sm {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl {
    max-width: 1140px;
  }
}

.row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.col {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 100%;
}

@media (min-width: 576px) {
  .col-sm {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }

  .row-cols-sm-1 > * {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .row-cols-sm-2 > * {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .row-cols-sm-3 > * {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .row-cols-sm-4 > * {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .row-cols-sm-5 > * {
    -ms-flex: 0 0 20%;
    flex: 0 0 20%;
    max-width: 20%;
  }

  .row-cols-sm-6 > * {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-sm-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }

  .col-sm-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }

  .col-sm-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }

  .col-sm-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }

  .col-sm-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }

  .col-sm-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }

  .col-sm-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-sm-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }

  .col-sm-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }

  .col-sm-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }

  .col-sm-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }

  .col-sm-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }

  .col-sm-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }

  .order-sm-first {
    -ms-flex-order: -1;
    order: -1;
  }

  .order-sm-last {
    -ms-flex-order: 13;
    order: 13;
  }

  .order-sm-0 {
    -ms-flex-order: 0;
    order: 0;
  }

  .order-sm-1 {
    -ms-flex-order: 1;
    order: 1;
  }

  .order-sm-2 {
    -ms-flex-order: 2;
    order: 2;
  }

  .order-sm-3 {
    -ms-flex-order: 3;
    order: 3;
  }

  .order-sm-4 {
    -ms-flex-order: 4;
    order: 4;
  }

  .order-sm-5 {
    -ms-flex-order: 5;
    order: 5;
  }

  .order-sm-6 {
    -ms-flex-order: 6;
    order: 6;
  }

  .order-sm-7 {
    -ms-flex-order: 7;
    order: 7;
  }

  .order-sm-8 {
    -ms-flex-order: 8;
    order: 8;
  }

  .order-sm-9 {
    -ms-flex-order: 9;
    order: 9;
  }

  .order-sm-10 {
    -ms-flex-order: 10;
    order: 10;
  }

  .order-sm-11 {
    -ms-flex-order: 11;
    order: 11;
  }

  .order-sm-12 {
    -ms-flex-order: 12;
    order: 12;
  }

  .offset-sm-0 {
    margin-left: 0;
  }

  .offset-sm-1 {
    margin-left: 8.333333%;
  }

  .offset-sm-2 {
    margin-left: 16.666667%;
  }

  .offset-sm-3 {
    margin-left: 25%;
  }

  .offset-sm-4 {
    margin-left: 33.333333%;
  }

  .offset-sm-5 {
    margin-left: 41.666667%;
  }

  .offset-sm-6 {
    margin-left: 50%;
  }

  .offset-sm-7 {
    margin-left: 58.333333%;
  }

  .offset-sm-8 {
    margin-left: 66.666667%;
  }

  .offset-sm-9 {
    margin-left: 75%;
  }

  .offset-sm-10 {
    margin-left: 83.333333%;
  }

  .offset-sm-11 {
    margin-left: 91.666667%;
  }
}

.btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  color: #fff;
  background-color: #0069d9;
  border-color: #0062cc;
}

.btn-primary.focus,
.btn-primary:focus {
  color: #fff;
  background-color: #0069d9;
  border-color: #0062cc;
  box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);
}

.btn-primary.disabled,
.btn-primary:disabled {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #0062cc;
  border-color: #005cbf;
}

.btn-primary:not(:disabled):not(.disabled).active:focus,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover {
  color: #fff;
  background-color: #5a6268;
  border-color: #545b62;
}

.btn-secondary.focus,
.btn-secondary:focus {
  color: #fff;
  background-color: #5a6268;
  border-color: #545b62;
  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
}

.btn-secondary.disabled,
.btn-secondary:disabled {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:not(:disabled):not(.disabled).active,
.btn-secondary:not(:disabled):not(.disabled):active,
.show > .btn-secondary.dropdown-toggle {
  color: #fff;
  background-color: #545b62;
  border-color: #4e555b;
}

.btn-secondary:not(:disabled):not(.disabled).active:focus,
.btn-secondary:not(:disabled):not(.disabled):active:focus,
.show > .btn-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
}

.btn-success {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.btn-success:hover {
  color: #fff;
  background-color: #218838;
  border-color: #1e7e34;
}

.btn-success.focus,
.btn-success:focus {
  color: #fff;
  background-color: #218838;
  border-color: #1e7e34;
  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);
}

.btn-success.disabled,
.btn-success:disabled {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.btn-success:not(:disabled):not(.disabled).active,
.btn-success:not(:disabled):not(.disabled):active,
.show > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #1e7e34;
  border-color: #1c7430;
}

.btn-success:not(:disabled):not(.disabled).active:focus,
.btn-success:not(:disabled):not(.disabled):active:focus,
.show > .btn-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);
}

.btn-info:hover {
  color: #fff;
  background-color: #138496;
  border-color: #117a8b;
}

.btn-info.focus,
.btn-info:focus {
  color: #fff;
  background-color: #138496;
  border-color: #117a8b;
  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
}

.btn-info.disabled,
.btn-info:disabled {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-info:not(:disabled):not(.disabled).active,
.btn-info:not(:disabled):not(.disabled):active,
.show > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #117a8b;
  border-color: #10707f;
}

.btn-info:not(:disabled):not(.disabled).active:focus,
.btn-info:not(:disabled):not(.disabled):active:focus,
.show > .btn-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
}

.btn-warning {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn-warning:hover {
  color: #212529;
  background-color: #e0a800;
  border-color: #d39e00;
}

.btn-warning.focus,
.btn-warning:focus {
  color: #212529;
  background-color: #e0a800;
  border-color: #d39e00;
  box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);
}

.btn-warning.disabled,
.btn-warning:disabled {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn-warning:not(:disabled):not(.disabled).active,
.btn-warning:not(:disabled):not(.disabled):active,
.show > .btn-warning.dropdown-toggle {
  color: #212529;
  background-color: #d39e00;
  border-color: #c69500;
}

.btn-warning:not(:disabled):not(.disabled).active:focus,
.btn-warning:not(:disabled):not(.disabled):active:focus,
.show > .btn-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);
}

.btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-danger:hover {
  color: #fff;
  background-color: #c82333;
  border-color: #bd2130;
}

.btn-danger.focus,
.btn-danger:focus {
  color: #fff;
  background-color: #c82333;
  border-color: #bd2130;
  box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);
}

.btn-danger.disabled,
.btn-danger:disabled {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-danger:not(:disabled):not(.disabled).active,
.btn-danger:not(:disabled):not(.disabled):active,
.show > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #bd2130;
  border-color: #b21f2d;
}

.btn-danger:not(:disabled):not(.disabled).active:focus,
.btn-danger:not(:disabled):not(.disabled):active:focus,
.show > .btn-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);
}

.btn-light {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-light:hover {
  color: #212529;
  background-color: #e2e6ea;
  border-color: #dae0e5;
}

.btn-light.focus,
.btn-light:focus {
  color: #212529;
  background-color: #e2e6ea;
  border-color: #dae0e5;
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}

.btn-light.disabled,
.btn-light:disabled {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-light:not(:disabled):not(.disabled).active,
.btn-light:not(:disabled):not(.disabled):active,
.show > .btn-light.dropdown-toggle {
  color: #212529;
  background-color: #dae0e5;
  border-color: #d3d9df;
}

.btn-light:not(:disabled):not(.disabled).active:focus,
.btn-light:not(:disabled):not(.disabled):active:focus,
.show > .btn-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}

.btn-dark {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-dark:hover {
  color: #fff;
  background-color: #23272b;
  border-color: #1d2124;
}

.btn-dark.focus,
.btn-dark:focus {
  color: #fff;
  background-color: #23272b;
  border-color: #1d2124;
  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}

.btn-dark.disabled,
.btn-dark:disabled {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-dark:not(:disabled):not(.disabled).active,
.btn-dark:not(:disabled):not(.disabled):active,
.show > .btn-dark.dropdown-toggle {
  color: #fff;
  background-color: #1d2124;
  border-color: #171a1d;
}

.btn-dark:not(:disabled):not(.disabled).active:focus,
.btn-dark:not(:disabled):not(.disabled):active:focus,
.show > .btn-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}

.btn-outline-primary {
  color: #007bff;
  border-color: #007bff;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-outline-primary.focus,
.btn-outline-primary:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.btn-outline-primary.disabled,
.btn-outline-primary:disabled {
  color: #007bff;
  background-color: transparent;
}

.btn-outline-primary:not(:disabled):not(.disabled).active,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.btn-outline-secondary {
  color: #ffffff;
  border-color: #6c757d;
}

.btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary.focus,
.btn-outline-secondary:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
  color: #6c757d;
  background-color: transparent;
}

.btn-outline-secondary:not(:disabled):not(.disabled).active,
.btn-outline-secondary:not(:disabled):not(.disabled):active,
.show > .btn-outline-secondary.dropdown-toggle {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-outline-secondary:not(:disabled):not(.disabled).active:focus,
.btn-outline-secondary:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-secondary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.btn-outline-success {
  color: #28a745;
  border-color: #28a745;
}

.btn-outline-success:hover {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.btn-outline-success.focus,
.btn-outline-success:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-outline-success.disabled,
.btn-outline-success:disabled {
  color: #28a745;
  background-color: transparent;
}

.btn-outline-success:not(:disabled):not(.disabled).active,
.btn-outline-success:not(:disabled):not(.disabled):active,
.show > .btn-outline-success.dropdown-toggle {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.btn-outline-success:not(:disabled):not(.disabled).active:focus,
.btn-outline-success:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-success.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.btn-outline-info {
  color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-info:hover {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-info.focus,
.btn-outline-info:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-outline-info.disabled,
.btn-outline-info:disabled {
  color: #17a2b8;
  background-color: transparent;
}

.btn-outline-info:not(:disabled):not(.disabled).active,
.btn-outline-info:not(:disabled):not(.disabled):active,
.show > .btn-outline-info.dropdown-toggle {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-outline-info:not(:disabled):not(.disabled).active:focus,
.btn-outline-info:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-info.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-outline-warning {
  color: #ffc107;
  border-color: #ffc107;
}

.btn-outline-warning:hover {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn-outline-warning.focus,
.btn-outline-warning:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.btn-outline-warning.disabled,
.btn-outline-warning:disabled {
  color: #ffc107;
  background-color: transparent;
}

.btn-outline-warning:not(:disabled):not(.disabled).active,
.btn-outline-warning:not(:disabled):not(.disabled):active,
.show > .btn-outline-warning.dropdown-toggle {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.btn-outline-warning:not(:disabled):not(.disabled).active:focus,
.btn-outline-warning:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-warning.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
}

.btn-outline-danger:hover {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-outline-danger.focus,
.btn-outline-danger:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.btn-outline-danger.disabled,
.btn-outline-danger:disabled {
  color: #dc3545;
  background-color: transparent;
}

.btn-outline-danger:not(:disabled):not(.disabled).active,
.btn-outline-danger:not(:disabled):not(.disabled):active,
.show > .btn-outline-danger.dropdown-toggle {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-outline-danger:not(:disabled):not(.disabled).active:focus,
.btn-outline-danger:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-danger.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.btn-outline-light {
  color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-outline-light:hover {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-outline-light.focus,
.btn-outline-light:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.btn-outline-light.disabled,
.btn-outline-light:disabled {
  color: #f8f9fa;
  background-color: transparent;
}

.btn-outline-light:not(:disabled):not(.disabled).active,
.btn-outline-light:not(:disabled):not(.disabled):active,
.show > .btn-outline-light.dropdown-toggle {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.btn-outline-light:not(:disabled):not(.disabled).active:focus,
.btn-outline-light:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.btn-outline-dark {
  color: #343a40;
  border-color: #343a40;
}

.btn-outline-dark:hover {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-outline-dark.focus,
.btn-outline-dark:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.btn-outline-dark.disabled,
.btn-outline-dark:disabled {
  color: #343a40;
  background-color: transparent;
}

.btn-outline-dark:not(:disabled):not(.disabled).active,
.btn-outline-dark:not(:disabled):not(.disabled):active,
.show > .btn-outline-dark.dropdown-toggle {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.btn-outline-dark:not(:disabled):not(.disabled).active:focus,
.btn-outline-dark:not(:disabled):not(.disabled):active:focus,
.show > .btn-outline-dark.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.btn-link {
  font-weight: 400;
  color: #007bff;
  text-decoration: none;
}

.btn-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

.btn-link.focus,
.btn-link:focus {
  text-decoration: underline;
}

.btn-link.disabled,
.btn-link:disabled {
  color: #6c757d;
  pointer-events: none;
}

.btn-group-lg > .btn,
.btn-lg {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.btn-group-sm > .btn,
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-block + .btn-block {
  margin-top: 0.5rem;
}

input[type="button"].btn-block,
input[type="reset"].btn-block,
input[type="submit"].btn-block {
  width: 100%;
}

.fade {
  transition: opacity 0.15s linear;
}

@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}

.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}

@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}

.dropdown,
.dropleft,
.dropright,
.dropup {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}

.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}

.nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link:focus,
.nav-link:hover {
  text-decoration: none;
}

.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
}

.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  color: #495057;
  background-color: #f7f7f7;
  border-color: #dee2e6 #dee2e6 #fff;
  border-top: 5px solid #fe0000;
  padding-top: 0.1vw !important;
}

.nav-tabs .nav-link.settings-active:not(.active) {
  color: #007c1c !important;
}

.nav-tabs .nav-link.settings-active.active {
  background-color: #e8f5e8ad !important;
  border-color: #d1ecf1 #d1ecf1 #fff !important;
  border-top: 5px solid #fe0000 !important;
}

/* Settings indicator dot with pulse animation */
.nav-tabs .nav-link.settings-active::after {
  content: "";
  position: relative;
  top: 0.37vw;
  width: 0.7vw;
  height: 0.7vw;
  background-color: #28a745;
  border-radius: 50%;
  border: 1px solid #fff;
  box-shadow: 0 0 0 1px #28a745;
  transform: translateY(-50%);
  animation: settings-pulse 2s ease-in-out infinite;
  display: inline-block;
  margin-left: 0.5vw;
}

/* Pulse animation keyframes */
@keyframes settings-pulse {
  0% {
    transform: translateY(-50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-50%) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translateY(-50%) scale(1);
    opacity: 0.8;
  }
}

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  border-radius: 0.25rem;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #007bff;
}

.nav-fill .nav-item,
.nav-fill > .nav-link {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified .nav-item,
.nav-justified > .nav-link {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center;
}

.tab-content > .tab-pane {
  display: none;
}

.tab-content > .active {
  display: block;
}

.modal-open {
  overflow: hidden;
}

.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
  /* Allow modal backdrop clicks */
  pointer-events: auto;
}

/* Ensure the modal content itself can receive clicks */
.modal-content {
  pointer-events: auto;
}

/* Make sure all interactive elements inside modals are clickable */
.modal-content button,
.modal-content a,
.modal-content input,
.modal-content select,
.modal-content textarea,
.modal-content .form-control,
.modal-content .btn,
.modal-content label {
  pointer-events: auto;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}

/* Add a rule to prevent Bootstrap's data-dismiss functionality */
[data-dismiss="modal"] {
  pointer-events: auto;
}

/* Only allow close buttons to be clickable */
.modal .close,
.modal .btn-close,
.modal .close-button,
.modal .modal-close-button,
.modal #cancelButton,
.modal #deleteModalClose,
.modal #duplicateModalClose,
.modal #pasteOptionsModalClose,
.modal #cancelDelete,
.modal #cancelSaveButton,
.modal #close-api-btn,
.modal #retryButton,
#promptModalClose,
#ContentLargeModalClose,
#imageUploadModalClose,
#settingsReuseModalClose,
.modal #cancelImageUpload,
.modal button[data-dismiss="modal"] {
  pointer-events: auto;
}

/* Image Upload Modal - Make backdrop non-clickable */
#imageUploadModal.modal {
  pointer-events: none;
}

#imageUploadModal .modal-dialog {
  pointer-events: auto;
}

#imageUploadModal .modal-backdrop {
  pointer-events: none;
}

/* Make entire page non-clickable when image upload modal is open */
body.modal-open-no-click {
  pointer-events: none;
}

body.modal-open-no-click #imageUploadModal {
  pointer-events: auto;
}

body.modal-open-no-click #imageUploadModal .modal-dialog {
  pointer-events: auto;
}

.modal.fade .modal-dialog {
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  -webkit-transform: translate(0, -65px);
  transform: translate(0, -65px);
}

@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}

.modal.show .modal-dialog {
  -webkit-transform: none;
  transform: none;
}

.modal.modal-static .modal-dialog {
  -webkit-transform: scale(1.02);
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  display: -ms-flexbox;
  display: flex;
  max-height: calc(100% - 1rem);
}

.modal-dialog-scrollable .modal-content {
  max-height: calc(100vh - 1rem);
  overflow: hidden;
}

.modal-dialog-scrollable .modal-footer,
.modal-dialog-scrollable .modal-header {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  min-height: calc(100% - 1rem);
}

.modal-dialog-centered::before {
  display: block;
  height: calc(100vh - 1rem);
  height: -webkit-min-content;
  height: -moz-min-content;
  height: min-content;
  content: "";
}

.modal-dialog-centered.modal-dialog-scrollable {
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100%;
}

.modal-dialog-centered.modal-dialog-scrollable .modal-content {
  max-height: none;
}

.modal-dialog-centered.modal-dialog-scrollable::before {
  content: none;
}

.modal-content {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0;
  outline: 0;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}

.modal-backdrop.fade {
  opacity: 0;
}

.modal-backdrop.show {
  opacity: 0.5;
  pointer-events: auto; /* Allow clicks on the backdrop to close modals */
}

.modal-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.modal-header .close {
  padding: 1rem 1rem;
  margin: -1rem -1rem -1rem auto;
}

.modal-body {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1rem;
}

.modal-footer {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: calc(0.3rem - 1px);
  border-bottom-left-radius: calc(0.3rem - 1px);
}

.modal-footer > * {
  margin: 0.25rem;
}

.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }

  .modal-dialog-scrollable {
    max-height: calc(100% - 3.5rem);
  }

  .modal-dialog-scrollable .modal-content {
    max-height: calc(100vh - 3.5rem);
  }

  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }

  .modal-dialog-centered::before {
    height: calc(100vh - 3.5rem);
    height: -webkit-min-content;
    height: -moz-min-content;
    height: min-content;
  }

  .modal-sm {
    max-width: 300px;
  }
}

@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    max-width: 800px;
  }
}

@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1140px;
  }
}

@-webkit-keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: spinner-border 0.75s linear infinite;
  animation: spinner-border 0.75s linear infinite;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

.text-center {
  text-align: center !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@-webkit-keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  50% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  50% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  -webkit-animation: spinner-grow 0.75s linear infinite;
  animation: spinner-grow 0.75s linear infinite;
}

.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}

.align-items-center {
  -ms-flex-align: center !important;
  align-items: center !important;
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}

/* Email Preview Loading Overlay */
#emailPreviewLoadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

#emailPreviewLoadingOverlay .spinner-border {
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
}

#emailPreviewLoadingText {
  font-size: 1.2rem;
  color: #333;
  font-weight: 500;
}

.form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75vw + 0.15vw);
  padding: 0.375vw 0.75vw;
  font-size: 0.9vw;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 0.07vw solid #ced4da;
  border-radius: 0.25vw;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.align-items-center {
  -ms-flex-align: center !important;
  align-items: center !important;
}
.d-flex {
  display: -ms-flexbox !important;
  display: flex !important;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
}

.nav-link:focus,
.nav-link:hover {
  text-decoration: none;
}

.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid #dee2e6;
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
}

.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.userTabButton {
  height: 42px;
  font-size: 14px !important;
  padding-left: 20px;
  border-radius: 0 !important;
}

.nav-pills .nav-link {
  border-radius: 0.25rem;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #007bff;
}

.assetRowContainer td {
  text-align: left !important;
  font-size: 12px;
  padding-left: 10px;
  width: auto;
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  word-wrap: break-word;
  white-space: nowrap;
  font-weight: 500;
  overflow: hidden;
}

.assetRowContainer > table {
  width: 100%;
  display: table;
  table-layout: fixed;
  border-collapse: collapse;
  color: #000;
  height: 100%;
}

.nav-fill .nav-item,
.nav-fill > .nav-link {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified .nav-item,
.nav-justified > .nav-link {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center;
}

.tab-content > .tab-pane {
  display: none;
}

.tab-content > .active {
  display: block;
}

.navbar {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}

.navbar .navbar .container-fluid,
.navbar .container-lg,
.navbar .container-md,
.navbar .container-sm,
.navbar .container-xl {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.navbar-brand {
  display: inline-block;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  margin-right: 1rem;
  font-size: 1.25rem;
  line-height: inherit;
  white-space: nowrap;
}

.navbar-brand:focus,
.navbar-brand:hover {
  text-decoration: none;
}

.navbar-nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
}

.navbar-nav .dropdown-menu {
  position: static;
  float: none;
}

.navbar-text {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.navbar-collapse {
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-align: center;
  align-items: center;
}

.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.25rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.navbar-toggler:focus,
.navbar-toggler:hover {
  text-decoration: none;
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: "";
  background: no-repeat center center;
  background-size: 100% 100%;
}

@media (max-width: 575.98px) {
  .navbar-expand-sm > .navbar-expand-sm > .container-fluid,
  .navbar-expand-sm > .container-lg,
  .navbar-expand-sm > .container-md,
  .navbar-expand-sm > .container-sm,
  .navbar-expand-sm > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 576px) {
  .navbar-expand-sm {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .navbar-expand-sm .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .navbar-expand-sm > .navbar-expand-sm > .container-fluid,
  .navbar-expand-sm > .container-lg,
  .navbar-expand-sm > .container-md,
  .navbar-expand-sm > .container-sm,
  .navbar-expand-sm > .container-xl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }

  .navbar-expand-sm .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }

  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
}

@media (max-width: 767.98px) {
  .navbar-expand-md > .navbar-expand-md > .container-fluid,
  .navbar-expand-md > .container-lg,
  .navbar-expand-md > .container-md,
  .navbar-expand-md > .container-sm,
  .navbar-expand-md > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 768px) {
  .navbar-expand-md {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .navbar-expand-md .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .navbar-expand-md > .navbar-expand-md > .container-fluid,
  .navbar-expand-md > .container-lg,
  .navbar-expand-md > .container-md,
  .navbar-expand-md > .container-sm,
  .navbar-expand-md > .container-xl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }

  .navbar-expand-md .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }

  .navbar-expand-md .navbar-toggler {
    display: none;
  }
}

@media (max-width: 991.98px) {
  .navbar-expand-lg > .navbar-expand-lg > .container-fluid,
  .navbar-expand-lg > .container-lg,
  .navbar-expand-lg > .container-md,
  .navbar-expand-lg > .container-sm,
  .navbar-expand-lg > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 992px) {
  .navbar-expand-lg {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .navbar-expand-lg .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .navbar-expand-lg > .navbar-expand-lg > .container-fluid,
  .navbar-expand-lg > .container-lg,
  .navbar-expand-lg > .container-md,
  .navbar-expand-lg > .container-sm,
  .navbar-expand-lg > .container-xl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }

  .navbar-expand-lg .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }

  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
}

@media (max-width: 1199.98px) {
  .navbar-expand-xl > .navbar-expand-xl > .container-fluid,
  .navbar-expand-xl > .container-lg,
  .navbar-expand-xl > .container-md,
  .navbar-expand-xl > .container-sm,
  .navbar-expand-xl > .container-xl {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 1200px) {
  .navbar-expand-xl {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }

  .navbar-expand-xl .navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }

  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }

  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }

  .navbar-expand-xl > .navbar-expand-xl > .container-fluid,
  .navbar-expand-xl > .container-lg,
  .navbar-expand-xl > .container-md,
  .navbar-expand-xl > .container-sm,
  .navbar-expand-xl > .container-xl {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }

  .navbar-expand-xl .navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }

  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
}

.navbar-expand {
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.navbar-expand > .navbar-expand > .container-fluid,
.navbar-expand > .container-lg,
.navbar-expand > .container-md,
.navbar-expand > .container-sm,
.navbar-expand > .container-xl {
  padding-right: 0;
  padding-left: 0;
}

.navbar-expand .navbar-nav {
  -ms-flex-direction: row;
  flex-direction: row;
}

.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}

.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

.navbar-expand > .navbar-expand > .container-fluid,
.navbar-expand > .container-lg,
.navbar-expand > .container-md,
.navbar-expand > .container-sm,
.navbar-expand > .container-xl {
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.navbar-expand .navbar-collapse {
  display: -ms-flexbox !important;
  display: flex !important;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
}

.navbar-expand .navbar-toggler {
  display: none;
}

.navbar-light .navbar-brand {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-brand:focus,
.navbar-light .navbar-brand:hover {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-nav .nav-link {
  color: rgba(0, 0, 0, 0.5);
}

.navbar-light .navbar-nav .nav-link:focus,
.navbar-light .navbar-nav .nav-link:hover {
  color: rgba(0, 0, 0, 0.7);
}

.navbar-light .navbar-nav .nav-link.disabled {
  color: rgba(0, 0, 0, 0.3);
}

.navbar-light .navbar-nav .active > .nav-link,
.navbar-light .navbar-nav .nav-link.active,
.navbar-light .navbar-nav .nav-link.show,
.navbar-light .navbar-nav .show > .nav-link {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-toggler {
  color: rgba(0, 0, 0, 0.5);
  border-color: rgba(0, 0, 0, 0.1);
}

.navbar-light .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.5%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-light .navbar-text {
  color: rgba(0, 0, 0, 0.5);
}

.navbar-light .navbar-text a {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-light .navbar-text a:focus,
.navbar-light .navbar-text a:hover {
  color: rgba(0, 0, 0, 0.9);
}

.navbar-dark .navbar-brand {
  color: #fff;
}

.navbar-dark .navbar-brand:focus,
.navbar-dark .navbar-brand:hover {
  color: #fff;
}

.navbar-dark .navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.5);
}

.navbar-dark .navbar-nav .nav-link:focus,
.navbar-dark .navbar-nav .nav-link:hover {
  color: rgba(255, 255, 255, 0.75);
}

.navbar-dark .navbar-nav .nav-link.disabled {
  color: rgba(255, 255, 255, 0.25);
}

.navbar-dark .navbar-nav .active > .nav-link,
.navbar-dark .navbar-nav .nav-link.active,
.navbar-dark .navbar-nav .nav-link.show,
.navbar-dark .navbar-nav .show > .nav-link {
  color: #fff;
}

.nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link {
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.nav-tabs .nav-link:focus,
.nav-tabs .nav-link:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
}

.nav-tabs .nav-link.disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}

.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  border-radius: 0.25rem;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #007bff;
}

.nav-fill .nav-item,
.nav-fill > .nav-link {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified .nav-item,
.nav-justified > .nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link:focus,
.nav-link:hover {
  text-decoration: none;
}

a.nav-link {
  color: #007bff;
  text-decoration: none;
  background-color: transparent;
}

.custom-control-label::before,
.custom-file-label,
.custom-select {
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out,
    box-shadow 0.15s ease-in-out;
}

.custom-select {
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  background: #fff
    url(
      data:image/svg + xml,
      %3csvgxmlns="http://www.w3.org/2000/svg"width="4"height="5"viewBox="0 0 4 5"%3e%3cpathfill="%23343a40"d="M2 0L0 2h4zm0 5L0 3h4z"/%3e%3c/svg%3e
    )
    no-repeat right 0.75rem center / 8px 10px;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

/* Removed duplicate .form-check-inline rule - see line 3247 for the main definition */

.form-check {
  position: relative;
  display: block;
  padding-left: 0;
}

html,
body {
  height: 100%;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  line-height: 1.5;
  box-sizing: border-box;
  background: white;
}
*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Table row background styles */
.table-row-active {
  background-color: #daedf9 !important;
}

.table-row-default {
  background-color: white !important;
}

/* For HOVER effect OR Context Menu Selection (e.g., Select Parent) */
.highlighted {
  outline: 2px solid #007bff !important; /* Simple blue outline */
  outline-offset: 1px !important;
  /* No box-shadow for simple highlight */
  box-shadow: none !important;
  cursor: pointer;
  position: relative; /* May be needed if z-index conflicts arise, otherwise potentially optional */
  z-index: 5;
}

.highlighted-from-editor {
  outline: 5px solid transparent !important;
  outline-offset: -5px;
  /* Background image will be set dynamically via JavaScript */
  position: relative;
  z-index: 5;
  box-shadow: 0 0 0 5px transparent;
}

/* Path hover highlight - modern orange outline with glow */
.path-hover-highlight {
  outline: 2px solid #ff6b35 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 12px 2px rgba(255, 107, 53, 0.3) !important;
  transition: all 0.3s ease !important;
}

/* Path element hover styling */
.path-element:hover {
  background-color: rgba(255, 107, 53, 0.1) !important;
  color: #ff6b35 !important;
  transition: all 0.3s ease !important;
}

/* HTML Syntax Validation Styles */
.html-editor-container {
  position: relative;
  display: flex;
  gap: 15px;
  width: 100%;
}

.html-editor-wrapper {
  flex: 1;
  position: relative;
}

.html-syntax-error {
  background-color: rgba(255, 0, 0, 0.2) !important;
  border-bottom: 2px wavy #ff0000 !important;
  position: relative;
}

.html-syntax-warning {
  background-color: rgba(255, 165, 0, 0.15) !important;
  border-bottom: 2px wavy #ffa500 !important;
  position: relative;
}

.html-error-tooltip {
  position: absolute;
  background: #ff4444;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  max-width: 300px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.html-error-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid transparent;
  border-top-color: #ff4444;
}

.html-warning-tooltip {
  position: absolute;
  background: #ff8800;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  max-width: 300px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.html-warning-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border: 5px solid transparent;
  border-top-color: #ff8800;
}

/* Live Preview Styles */
.live-preview-container {
  flex: 1;
  min-width: 300px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: #f9f9f9;
  overflow: hidden;
}

.live-preview-header {
  background: #f1f1f1;
  padding: 8px 12px;
  border-bottom: 1px solid #ddd;
  font-weight: 600;
  font-size: 14px;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.live-preview-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: normal;
}

.live-preview-status.valid {
  background: #d4edda;
  color: #155724;
}

.live-preview-status.invalid {
  background: #f8d7da;
  color: #721c24;
}

.live-preview-status.warning {
  background: #fff3cd;
  color: #856404;
}

.live-preview-content {
  padding: 12px;
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
  background: white;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;
}

.live-preview-content.valid {
  border-color: #28a745;
}

.live-preview-content.invalid {
  border-color: #dc3545;
  background: #fff5f5;
}

.live-preview-content.warning {
  border-color: #ffc107;
  background: #fffef5;
}

.error-panel {
  background: #f8f9fa;
  border-top: 1px solid #ddd;
  padding: 12px;
  max-height: 100%;
  overflow-y: auto;
}

.error-item {
  margin-bottom: 8px;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 13px;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.error-item.error {
  background: #f8d7da;
  border-left: 3px solid #dc3545;
}

.error-item.warning {
  background: #fff3cd;
  border-left: 3px solid #ffc107;
}

.error-icon {
  font-weight: bold;
  margin-top: 1px;
}

.error-icon.error {
  color: #dc3545;
}

.error-icon.warning {
  color: #ffc107;
}

.error-message {
  flex: 1;
}

.error-suggestion {
  font-family: monospace;
  background: rgba(0, 0, 0, 0.1);
  padding: 2px 4px;
  border-radius: 2px;
  margin-top: 4px;
  font-size: 12px;
}

.apply-suggestion-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  cursor: pointer;
  margin-top: 4px;
}

.apply-suggestion-btn:hover {
  background: #0056b3;
}

/* Validation Status Dropdown Styles */
.validation-status-container {
  position: relative;
  display: inline-block;
  margin-right: 10px;
}

/* Validation Toggle Styles */
.validation-toggle-container {
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 0.5vw;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-input:checked + .toggle-slider {
  background-color: #28a745;
}

.toggle-slider-circle {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-input:checked + .toggle-slider .toggle-slider-circle {
  transform: translateX(17px);
}

.validation-toggle-status {
  font-size: 11px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.validation-toggle-status.enabled {
  color: #28a745;
}

.validation-toggle-status.disabled {
  color: #dc3545;
}

.validation-status {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.validation-status:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.validation-status.valid {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.validation-status.invalid {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.validation-status.warning {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.validation-status.scanning {
  background: #e3f2fd;
  color: #1565c0;
  border: 1px solid #90caf9;
  animation: pulse 1.5s infinite;
}

.dropdown-arrow {
  font-size: 10px;
  opacity: 0.7;
  transition: transform 0.2s ease;
}

.validation-status:hover .dropdown-arrow {
  opacity: 1;
}

.validation-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 400px;
  max-height: calc(100vh - 200px);
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1050;
  overflow: hidden;
}

.validation-dropdown-header {
  background: #f8f9fa;
  padding: 12px 16px;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
  font-size: 14px;
  color: #495057;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.validation-issues-text {
  flex: 1;
}

/* Fix All Button Styles */
.fix-all-button {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: 6vw;
  padding: 0.3vw 0.7vw;
  font-size: 0.7vw;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-transform: uppercase;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.fix-all-button:hover {
  background: linear-gradient(135deg, #218838, #1ea080);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.fix-all-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.fix-all-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.fix-all-progress {
  font-weight: 700;
  color: #ffc107;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.validation-error-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
}

.no-errors-message {
  padding: 20px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

.validation-error-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 4px solid;
  background: #f8f9fa;
}

.validation-error-item.error {
  border-left-color: #dc3545;
  background: #fff5f5;
}

.validation-error-item.warning {
  border-left-color: #ffc107;
  background: #fffbf0;
}

.validation-error-icon {
  margin-right: 12px;
  margin-top: 2px;
  font-weight: bold;
  flex-shrink: 0;
}

.validation-error-icon.error {
  color: #dc3545;
}

.validation-error-icon.warning {
  color: #ffc107;
}

.validation-error-message {
  flex: 1;
  font-size: 13px;
  line-height: 1.4;
}

.validation-error-suggestion {
  margin-top: 8px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  color: #495057;
}

.validation-apply-suggestion-btn {
  margin-top: 8px;
  padding: 6px 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.validation-apply-suggestion-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.validation-apply-suggestion-btn:active {
  transform: translateY(0);
}
@keyframes aiGradientBorder {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.form-section-h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1872e2;
  background-color: #ffffff;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  padding-bottom: 10px;
}
.form-section {
  margin-bottom: 2vw;
  border-radius: 0.3vw;
  overflow: hidden;
  border: 0.1vw solid #f0f0f0;
}
.form-body {
  padding: 0.7vw 1vw;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0px;
  padding-top: 0.7vw;
}
.form-label {
  font-weight: 600;
  color: #2c3e50;
  text-align: right;
  align-self: center;
  display: block;
  font-size: 0.9vw;
}
.form-input,
.form-select {
  padding: 0.6vw;
  border: 1px solid #dde2e5;
  border-radius: 0.4vw;
  font-size: 0.9vw;
  width: 100%;
  box-sizing: border-box;
}
.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #0070d2;
  box-shadow: 0 0 5px rgba(0, 112, 210, 0.3);
}
.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.6vw;
  margin: 0.6vw;
}
.checkbox-label input {
  margin-top: 0.375vw;
  width: 1vw;
  height: 1vw;
}
.checkbox-description {
  font-size: 0.8vw;
  color: #7f8c8d;
  line-height: 1.4;
  margin: 0;
  padding: 0.3vw;
}
.slider-container {
  display: contents;
  grid-column: 1 / -1;
  margin-top: 1vw;
}

/* Block Settings Tab Styles */
#block-settings .card-body {
  padding: 1.2vw;
  border-radius: 0.42vw;
}

/* Heading removed */
#block-settings .p-3 {
  padding-top: 0.5vw !important;
}

#block-settings .form-group {
  margin-bottom: 1.5vw;
  position: relative;
}

#block-settings .form-check {
  margin-bottom: 0.8vw;
}

#block-settings .form-check-input {
  margin-top: 0;
  margin-right: 0.5vw;
}

#block-settings .form-check-label {
  font-size: 0.9vw;
  font-weight: 500;
}

#block-settings .subText2 {
  font-size: 0.8vw;
  color: #6c757d;
  margin-top: 0.3vw;
  margin-bottom: 1vw;
}

#block-settings label.d-block {
  font-size: 0.9vw;
  font-weight: 600;
  margin-top: 0.4vw;
  color: #343a40;
  margin-right: 0.5vw;
}

/* Date field styles */
.hide-date-field {
  margin-left: 1.8vw;
  margin-bottom: 1vw;
  margin-top: 0.5vw;
  width: 100%;
  max-width: 300px;
  display: none; /* Hidden by default, shown when checkbox is checked */
}

.date-input-width {
  width: 100%;
  max-width: 300px;
  height: 2.2vw;
  font-size: 0.9vw;
  padding: 0.3vw 0.5vw;
}

/* Day of week checkboxes - ensure horizontal alignment */
.form-check-inline {
  display: inline-flex !important;
  align-items: center;
  padding-left: 0;
  margin-right: 0.8vw;
  margin-bottom: 0.5vw;
  margin-left: 0.2vw;
  vertical-align: top;
}

.form-check-inline .form-check-input {
  margin-right: 0.3vw;
  margin-top: 0;
  position: relative;
  top: -0.05vw;
}

.form-check-inline .form-check-label {
  font-size: 0.9vw;
  font-weight: 500;
}

#block-settings select.question2 {
  width: 100%;
  max-width: 300px;
  font-size: 0.9vw;
  height: 2.2vw;
  padding: 0.3vw 0.5vw;
  border: 1px solid #dde2e5;
  border-radius: 0.3vw;
}

/* Make day checkboxes align horizontally */
#block-settings .form-group:nth-child(3) > div {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.2vw;
}

.show {
  display: block !important;
}

/* Ensure day checkbox containers display properly */
.form-group div:has(.day-checkbox) {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.2vw;
}

/* Specific targeting for the day checkboxes section */
#block-settings .form-group:last-child > div,
.form-group:has(.day-checkbox) > div {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 0.2vw;
}

/* Fix Position in email alignment */
#block-settings .form-group.row {
  display: block;
}

#block-settings .form-group.row label {
  display: block;
  font-size: 0.9vw;
  font-weight: 600;
  margin-bottom: 0.8vw;
  color: #343a40;
}

#block-settings .form-group.row .col-sm-8 {
  padding-left: 0;
  margin-left: 0;
}

#block-settings .form-group.row .subText2 {
  margin-left: 0;
  font-size: 0.8vw;
  color: #6c757d;
  margin-top: 0.3vw;
}
.slider {
  width: 100%;
  appearance: none;
  height: 0.5vw;
  border-radius: 0.3vw;
  background: #dde2e5;
  outline: none;
  transition: background-color 0.2s ease;
  margin-top: 0.5vw;
}
.slider::-webkit-slider-thumb {
  appearance: none;
  width: 1.25vw;
  height: 1.25vw;
  border-radius: 50%;
  background: #0070d2;
  cursor: pointer;
  transition: background-color 0.3s;
}
.slider::-webkit-slider-thumb:hover {
  background: #005fb2;
}
.slider::-moz-range-thumb {
  width: 1.25vw;
  height: 1.25vw;
  border-radius: 50%;
  background: #0070d2;
  cursor: pointer;
}
.slider-labels {
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-top: 0.5vw;
  font-size: 0.875vw;
  color: #7f8c8d;
  font-weight: 600;
}
.grid-dimensions {
  display: flex;
  gap: 10px;
  align-items: center;
  width: 100%;
  margin-bottom: 1rem;
}
.grid-inputs {
  display: flex;
  gap: 1rem;
  align-items: center;
}
.grid-inputs .form-input {
  width: 60px;
  text-align: center;
}
.editable-div {
  min-height: 100px;
  overflow: auto;
  border: 1px solid #dde2e5;
  padding: 8px;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
  background-color: #ffffff;
}
.editable-div:focus {
  outline: none;
  border-color: #0070d2;
  box-shadow: 0 0 5px rgba(0, 112, 210, 0.3);
}
.field-wrapper {
  margin-bottom: 15px;
}
.field-wrapper label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}
.field-wrapper input,
.field-wrapper select {
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
}
.field-wrapper input[type="color"] {
  padding: 0;
  border: none;
  background: none;
  width: 50px;
  height: 30px;
}
.color-container {
  display: flex;
  align-items: center;
  gap: 0.3vw;
  position: relative;
}
.color-input {
  width: 40px;
  height: 30px;
  border: none;
  padding: 0;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  margin-right: 10px;
}
.color-preview {
  width: 40px;
  height: 40px;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
}
/* Box model container for padding, margin, border width */
.box-model-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5vw;
  box-sizing: border-box;
  background-color: #f9f9f9;
  border-radius: 0.3vw;
  width: 100%;
}

/* Box model input styles */
.box-model-input {
  width: 3vw;
  text-align: center;
  border: 1px solid #dde2e5;
  margin: 0.2vw !important;
  border-radius: 0.3vw;
  flex: 1;
  min-width: 0;
  font-size: 0.8vw;
}

/* Box model input wrapper */
.box-model-input-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-basis: calc(20% - 0.4vw);
}

/* Box model labels */
.box-model-label {
  display: block;
  font-size: 0.8vw;
  text-align: center;
  color: #666;
}
.corner-model-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.7vw;
}
.corner-model-input {
  width: 4.5vw;
  text-align: center;
  border: 1px solid #dde2e5;
  padding: 0 !important;
  border-radius: 0.3vw;
}
/* Border radius container */
.border-radius-box-model-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5vw;
  box-sizing: border-box;
  background-color: #f9f9f9;
  border-radius: 0.3vw;
  width: 100%;
}

/* Corner input styles */
.corner-input {
  width: 3vw;
  text-align: center;
  border: 1px solid #dde2e5;
  margin: 0.2vw !important;
  border-radius: 0.3vw;
  flex: 1;
  min-width: 0;
  font-size: 0.8vw;
}

/* Corner input wrapper */
.corner-input-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-basis: calc(20% - 0.4vw);
}

/* Corner input labels */
.corner-input-label {
  display: block;
  font-size: 0.8vw;
  text-align: center;
  color: #666;
}

.preview-container .form-section {
  flex: 1;
  width: 50vw;
}
.preview-container #actionButtons {
  flex-shrink: 0;
}
.toggle-container {
  text-align: right;
  margin-bottom: 1vw;
  margin-top: -0px;
}
.view-toggle {
  background-color: #0070d2;
  color: white;
  padding: 0.6vw 1.2vw;
  font-weight: bold;
  border: none;
  border-radius: 0.4vw;
  cursor: pointer;
  margin: 0 0.4vw;
}
.view-toggle.active {
  background-color: #005fb2;
}
.view-toggle:focus {
  outline: none;
}

/* Edit/Preview Toggle Styles */
.edit-preview-toggle-container {
  display: flex;
  align-items: center;
  margin: 0 0.5vw;
}

.edit-preview-toggle {
  display: flex;
  overflow: hidden;
  height: 100%;
}

.edit-preview-toggle-btn {
  border: 1px solid #dee2e6;
  background-color: #fff;
  color: black;
  padding: 0.375vw 0.75vw;
  font-size: var(--font-size-base, 0.97vw);
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  height: 100%;
  display: flex;
  align-items: center;
  border-radius: 0;
}

/* Left button (Edit) */
.edit-preview-toggle-btn:first-child {
  border-top-left-radius: var(--border-radius, 0.42vw);
  border-bottom-left-radius: var(--border-radius, 0.42vw);
  border-right: none;
}

/* Right button (Saved preview) */
.edit-preview-toggle-btn:last-child {
  border-top-right-radius: var(--border-radius, 0.42vw);
  border-bottom-right-radius: var(--border-radius, 0.42vw);
}

.edit-preview-toggle-btn.active {
  background-color: #0d6efd;
  color: white;
}

/* Email Preview Container Styles */
#emailPreviewContainer {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

#emailPreviewIframe {
  width: 100%;
  height: 100%;
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius, 0.42vw);
  margin: 0 auto;
  position: absolute !important;
}

/* Disabled button styles */
.view-toggle-btn {
  border: 1px solid #dee2e6;
  background-color: #fff;
  color: black;
  padding: 0vw 0.75vw;
  cursor: pointer;
  transition: background-color 0.3s, color 0.3s;
  height: 100%;
  display: flex;
  align-items: center;
  border-radius: 0;
  font-size: 1vw;
}

/* Left button (Desktop) */
.view-toggle-btn:first-child {
  border-top-left-radius: var(--border-radius, 0.42vw);
  border-bottom-left-radius: var(--border-radius, 0.42vw);
  border-right: none;
}

/* Right button (Mobile) */
.view-toggle-btn:last-child {
  border-top-right-radius: var(--border-radius, 0.42vw);
  border-bottom-right-radius: var(--border-radius, 0.42vw);
}

.view-toggle-btn.active {
  background-color: #0d6efd;
  color: white;
}

/* Mobile view styles for content preview */
#contentPreview.mobile-view {
  width: 34vw;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid #ccc;
  overflow-x: hidden;
}

/* Ensure content is properly displayed in mobile view */
#contentPreview.mobile-view .content-wrapper {
  width: 100% !important;
  transform: scale(0.9);
  transform-origin: top center;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
  padding: 0.7vw;
}

/* Mobile content view styles for tables and other elements */
.mobile-content-view {
  /* General container styles for mobile view */
  width: 100% !important;
  max-width: 100% !important;
  overflow-x: hidden !important;
}

.mobile-content-view table,
#contentPreview.mobile-view table {
  width: 100% !important;
  max-width: 100% !important;
  table-layout: fixed !important;
  border-collapse: collapse !important;
}

.mobile-content-view img,
#contentPreview.mobile-view img {
  max-width: 100% !important;
  height: auto !important;
}

.mobile-content-view div,
#contentPreview.mobile-view div {
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* Ensure all content in mobile view is properly sized */
.mobile-content-view *,
#contentPreview.mobile-view * {
  box-sizing: border-box !important;
}

.preview-container .preview-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  position: relative;
}

/* Center the contentPreview in mobile view */
.preview-container .preview-content #contentPreview.mobile-view {
  margin-left: auto;
  margin-right: auto;
}

#contentPreview::-webkit-scrollbar {
  width: 0.6vw;
}
#contentPreview::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 0.3vw;
}
#contentPreview::-webkit-scrollbar-thumb {
  background: #0070d2;
  border-radius: 0.3vw;
}
#contentPreview::-webkit-scrollbar-thumb:hover {
  background: #005fb2;
}
#actionButtons {
  flex-shrink: 0;
  display: flex;
  gap: 0.7vw;
  justify-content: flex-end;
  margin-top: 1.4vw;
}
#top_buttons h3,
#top_buttons form,
#top_buttons .toggle-container {
  flex-shrink: 0;
  margin-bottom: 0;
}
#top_buttons .toggle-container {
  margin-left: auto;
}
#loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(244, 247, 250, 0.9);
  z-index: 13000;
  display: none;
  justify-content: center;
  align-items: center;
}
.loader-animation {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.7vw;
}
.loader-dot {
  width: 1vw;
  height: 1vw;
  background-color: #0070d2;
  border-radius: 50%;
  animation: loader-bounce 1.4s infinite;
}
.loader-dot:nth-child(2) {
  animation-delay: 0.2s;
}
.loader-dot:nth-child(3) {
  animation-delay: 0.4s;
}
.spinner-border {
  display: inline-block;
  width: 1.4vw;
  height: 1.4vw;
  vertical-align: text-bottom;
  border: 0.18vw solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: spinner-border 0.75s linear infinite;
  animation: spinner-border 0.75s linear infinite;
}
.spinner-border-sm {
  width: 0.7vw;
  height: 0.7vw;
  border-width: 0.14vw;
}
.spinner-grow {
  display: inline-block;
  width: 1.4vw;
  height: 1.4vw;
  vertical-align: text-bottom;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  -webkit-animation: spinner-grow 0.75s linear infinite;
  animation: spinner-grow 0.75s linear infinite;
}
.spinner-grow-sm {
  width: 0.7vw;
  height: 0.7vw;
}
.modal.fade {
  opacity: 0;
  transition: opacity 0.15s linear;
}
.modal.fade.show {
  opacity: 1;
}
.modal-backdrop.fade {
  opacity: 0;
  transition: opacity 0.15s linear;
}
.modal-backdrop.fade.show {
  opacity: 0.5;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -65px);
}
.modal.show .modal-dialog {
  transform: none;
}
.modal-open {
  overflow: hidden;
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}
.modal,
#loadingModal,
.modall {
  position: fixed;
  top: 0;
  left: 0;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
  background-color: rgba(0, 0, 0, 0.4);
}
.modal {
  z-index: 2000;
}
#loadingModal {
  z-index: 15050;
}
.modall {
  z-index: 2000;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: 0.5;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -65px);
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}
.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.35vw;
  pointer-events: none;
}
.modal-dialog-scrollable {
  display: flex;
  max-height: calc(100% - 0.7vw);
}
.modal-dialog-scrollable .modal-content {
  max-height: calc(100vh - 0.7vw);
  overflow: hidden;
}
.modal-dialog-scrollable .modal-header,
.modal-dialog-scrollable .modal-footer {
  flex-shrink: 0;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}
.modal-dialog-centered {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100% - 1vw);
}
.modal-dialog-centered::before {
  display: block;
  height: calc(100vh - 1vw);
  content: "";
}
.modal-dialog-centered.modal-dialog-scrollable {
  flex-direction: column;
  justify-content: center;
  height: 100%;
}
.modal-dialog-centered.modal-dialog-scrollable .modal-content {
  max-height: none;
}
.modal-dialog-centered.modal-dialog-scrollable::before {
  content: none;
}
.modal-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1vw 1vw;
  border-bottom: 0.07vw solid #dee2e6;
  border-top-left-radius: calc(0.3vw - 0.07vw);
  border-top-right-radius: calc(0.3vw - 0.07vw);
}
.modal-header .close {
  padding: 1vw 1vw;
  margin: -1vw -1vw -1vw auto;
}
.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
  font-size: 1.5vw;
  font-weight: normal;
  margin-left: .5vw;
}
.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1vw;
}
.modal-footer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75vw;
  border-top: 0.07vw solid #dee2e6;
  border-bottom-right-radius: calc(0.3vw - 0.07vw);
  border-bottom-left-radius: calc(0.3vw - 0.07vw);
}
.modal-footer > * {
  margin: 0.25vw;
}
.bd-example-modal-lg .modal-dialog {
  display: table;
  position: relative;
  margin: 0 auto;
  top: calc(50% - 24px);
}
.modall-dialog.modal-sm {
  max-width: 300px;
}
.modall-content {
  width: 48px;
}
.tox .tox-dialog {
  background-color: #fff;
  border-color: #eee;
  border-radius: 10px;
  border-style: solid;
  border-width: 0;
  box-shadow: 0 16px 16px -10px rgba(34, 47, 62, 0.15),
    0 0 40px 1px rgba(34, 47, 62, 0.15);
  display: flex;
  flex-direction: column;
  max-height: 100%;
  max-width: 480px;
  overflow: hidden;
  position: relative;
  width: 95vw;
  z-index: 200;
}
.tox-dialog__body-nav {
  display: none !important;
}
div[role="tab"][aria-selected="false"][class*="tox-dialog__body-nav-item"] {
  display: none !important;
}
.custom-context-menu {
  position: absolute;
  z-index: 13000;
  background-color: #fff;
  border: 0.07vw solid #ccc;
  box-shadow: 0.15vw 0.15vw 0.4vw rgba(0, 0, 0, 0.2);
  display: none;
  border-radius: 0.4vw;
  min-width: 12vw;
  max-width: 20vw;
  font-size: 0.9vw;
}
.custom-context-menu ul {
  list-style: none;
  margin: 0;
  padding: 0.2vw 0;
}
.custom-context-menu li {
  padding: 0.6vw 0.8vw;
  cursor: pointer;
  display: block;
  transition: background-color 0.2s ease;
  border-bottom: 0.05vw solid #f0f0f0;
}
.custom-context-menu li button {
  width: 100%;
  text-align: left;
  font-size: inherit;
  line-height: 1.4;
  white-space: nowrap;
}

.custom-context-menu li:hover {
  background-color: #f8f9fa;
}

.custom-context-menu li:last-child {
  border-bottom: none;
}
#aiAssistantModal .modal-content {
  height: auto;
  width: 50%;
}
#editModal .form-body {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: 100%;
  padding: 0.5vw;
}

.tox .tox-statusbar__right-container {
  display: none !important;
}

.tox-statusbar__resize-handle {
  display: none !important;
}

#editModal .form-body-tinymce {
  display: block;
  width: 100%;
  margin-top: -4vw;
}

#editModal .form-label {
  min-width: 8vw;
  margin-right: 0.5vw;
  text-align: left;
  align-self: center;
  font-weight: 600;
  font-size: 0.8vw;
}

#editModal .form-input,
#editModal .form-select {
  min-width: 3.75vw;
  padding: 0.45vw;
  border: 0.07vw solid #dde2e5;
  border-radius: 0.45vw;
  font-size: 0.8vw;
  flex: 1;
  max-width: 100%;
  box-sizing: border-box;
  font-family: system-ui;
}

/* Make text inputs in CSS tab 100% width */
#cssContent .form-input[type="text"],
#cssContent textarea {
  width: 100%;
  box-sizing: border-box;
}

#attributesFormFields {
  margin-bottom: 4vw;
  padding: 1vw;
}

/* Create a horizontal layout for CSS fields to save vertical space */
#cssFormFields .form-fields {
  display: flex;
  overflow-y: auto;
  width: 100%;
  padding-bottom: 3vw;
  padding-left: 0.5vw;
  padding-top: 0.5vw;
}

/* Ensure the CSS tab content has proper scrolling */
#cssContent {
  overflow-y: auto;
  height: 100%;
  width: 100%;
}

/* For text fields that need full width */
#cssFormFields .form-body.full-width {
  width: 100%;
  order: -1; /* Ensure text fields appear at the top */
}

/* Text content field should appear at the very top */
#cssFormFields .form-body.text-content-field {
  width: 100%;
  order: -2; /* Even higher priority than other full-width fields */
  margin-bottom: 0vw;
  display: block; /* Override grid display for text content field */
  padding-bottom: 0.1vw;
}

/* Text content field textarea styling */
.text-content-field textarea.form-input {
  width: 100%;
  min-height: 6vw;
  resize: vertical;
  margin-top: 0.5vw;
}

/* Side-by-side fields container */
#cssFormFields .side-by-side-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  gap: 0px;
  margin-bottom: 0.7vw;
}

/* Default for 2-column layout */
#cssFormFields .side-by-side-container .form-body {
  flex: 1;
  min-width: 45%;
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
}

/* For 3-column layout (color fields and style/align/height fields) */
#cssFormFields .side-by-side-container.three-column .form-body {
  flex: 1;
  min-width: 30%;
  max-width: 33%;
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
}

/* For 6-column layout (all font fields in one row) */
#cssFormFields .side-by-side-container.six-column .form-body {
  flex: 1;
  min-width: 15%;
  max-width: 16.66%;
  margin-bottom: 0;
  display: flex;
  flex-direction: column;
}

/* Center text for 6-column font field labels */
#cssFormFields .side-by-side-container.six-column .form-label {
  padding-left: 0.8vw;
  padding-bottom: 0.5vw;
  padding-top: 0.5vw;
}

/* Dynamic color fields container */
#cssFormFields .color-fields-container {
  display: ruby !important;
  width: 100%;
  gap: 0.5vw;
  margin-bottom: 0.7vw;
  flex-wrap: nowrap;
}

/* Color field items with dynamic width - override grid layout */
#cssFormFields .color-fields-container .color-field-item {
  flex: 1 !important;
  min-width: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  margin-bottom: 0 !important;
  padding: 0.5vw !important;
  grid-template-columns: none !important;
}

/* Ensure color field labels and containers scale properly */
#cssFormFields .color-field-item .form-label {
  font-size: 0.9vw !important;
  margin-bottom: 0.3vw !important;
  text-align: center !important;
  align-self: center !important;
}

#cssFormFields .color-field-item .color-container {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  width: 100% !important;
}

/* Additional fallback for any color fields that might not get the color-field-item class */
#cssFormFields .color-fields-container .form-body {
  flex: 1 !important;
  min-width: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  grid-template-columns: none !important;
  padding-top: 0 !important;
}

/* Ensure proper spacing and width for form fields */
#cssFormFields .form-body {
  width: auto; /* Display 3 fields per row */
  box-sizing: border-box;
  display: grid;
  flex-direction: row;
  align-items: center;
}

/* Text content field container */
.text-content-field {
  width: 100%;
  margin-bottom: 1vw;
}

/* Fix TinyMCE editor layout */
#editorContent .form-fields {
  padding: 1vw;
  width: 100%;
  display: block;
}

/* Ensure tab content has proper scrolling and fills available space */
.tab-content {
  overflow: hidden;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tab-pane {
  height: 100%;
  overflow: hidden;
  flex: 1;
  display: none;
  flex-direction: column;
}

.tab-pane.active {
  display: flex !important;
}

/* Improve layout for color inputs */
#editModal .color-container {
  display: inline-block;
  align-items: center;
  width: 100%;
  position: relative;
}

/* Color swatch styling */
.color-swatch {
  width: 1.5vw;
  height: 1.5vw;
  border: 1px solid #dde2e5;
  border-radius: 0.3vw;
  display: inline-block;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.color-swatch:hover {
  border-color: #0070d2;
  box-shadow: 0 2px 6px rgba(0, 112, 210, 0.3);
  transform: scale(1.05);
}

/* Hide color inputs but keep them functional */
.color-container input[type="color"] {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  width: 1px;
  height: 1px;
  border: none;
  background: transparent;
  z-index: 1;
}

/* Style for color text input field */
.color-container .color-text-input {
  border: 1px solid #dde2e5;
  border-radius: 0.3vw;
  font-family: monospace;
  text-align: center;
  transition: border-color 0.2s ease;
}

.color-container .color-text-input:focus {
  outline: none;
  border-color: #0070d2;
  box-shadow: 0 0 3px rgba(0, 112, 210, 0.3);
}

/* Border Color Box Model Styling */
.border-color-box-model-container {
  display: flex;
  flex-direction: column;
  gap: 0.5vw;
}

.border-color-box-model-container .box-model-input-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5vw;
}

.border-color-box-model-container .box-model-label {
  min-width: 60px;
  font-size: 0.8vw;
  font-weight: 500;
}

/* Background Gradient Styling */
.background-gradient-container {
  display: flex;
  flex-direction: column;
  gap: 0.5vw;
}

.background-type-wrapper,
.background-solid-wrapper,
.background-gradient-wrapper {
  display: flex;
}

.background-gradient-wrapper .gradient-input-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5vw;
}

.background-label,
.gradient-label {
  min-width: 80px;
  font-size: 0.8vw;
  font-weight: 500;
}

.background-type-select,
.gradient-direction-select {
  padding: 0.2vw 0.3vw;
  border: 1px solid #dde2e5;
  border-radius: 0.3vw;
  font-size: 0.7vw;
  background-color: white;
}

.background-type-select:focus,
.gradient-direction-select:focus {
  outline: none;
  border-color: #0070d2;
  box-shadow: 0 0 3px rgba(0, 112, 210, 0.3);
}

/* Adjust box model inputs layout */
#editModal .box-model-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5vw;
  background-color: #f9f9f9;
  border-radius: 0.3vw;
  width: 100%;
}
#editModal .form-label {
  font-weight: 600;
  color: #2c3e50;
}
.modal-buttons {
  padding: 1vw;
  border-top: 0.07vw solid #dee2e6;
  position: absolute;
  bottom: 0;
  background-color: #ffffff;
  justify-content: right;
  width: 100%;
}
.drop-placeholder {
  background: rgba(0, 0, 255, 0.2);
  border: 2px dashed #00f;
  box-sizing: border-box;
  pointer-events: none;
  margin: inherit;
  transform: translateZ(0);
}
.form-fields {
  flex: 1 1 auto;
  overflow-y: auto;
  padding-right: 0.75vw;
  margin-bottom: 1.5vw;
  display: flex;
  flex-direction: column;
}

.preview-container #actionButtons {
  flex-shrink: 0;
  margin-top: 0; /* Changed from -3vw to fix button clickability */
  display: flex;
  gap: 0.75vw;
  padding: 0.5vw;
  position: relative; /* Added to ensure proper stacking context */
  z-index: 2; /* Ensure buttons stay above content */
}
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0);
  padding: 0 !important;
}

/* Specific styles for delete and duplicate modals */
.custom-modal {
  width: 30vw;
  /* max-width: 30vw; */
  margin: 5vh auto;
  transform: none !important;
  height: auto;
  border-radius: 0.6vw;
  box-shadow: 0 0.35vw 1.1vw rgba(0, 0, 0, 0.2);
  border: none;
}

/* Ensure consistent modal sizing across all viewports */
.modal-dialog {
  max-width: 30vw;
  width: 30vw;
  margin: 10vh auto;
}

/* Modal button styling */
.modal-footer .btn,
.modal-header .btn,
.modal-body .btn {
  font-size: 0.9vw;
  padding: 0.375vw 0.75vw;
  border-radius: 0.25vw;
  border-width: 0.07vw;
}

/* Modal close button */
.modal-header .close {
  font-size: 1.5vw;
  line-height: 1;
  opacity: 0.5;
  transition: opacity 0.15s;
}

.modal-header .close:hover {
  opacity: 1;
}

#deleteModal .modal-dialog,
#pasteOptionsModal .modal-dialog,
#duplicateModal .modal-dialog {
  margin: 10vh auto;
  max-width: 30vw;
  width: 30vw;
}

#deleteModal .modal-header,
#pasteOptionsModal .modal-header,
#duplicateModal .modal-header {
  border-bottom: 0.07vw solid #dee2e6;
  padding: 1.1vw 1.5vw;
  position: relative;
}

#deleteModal .modal-title,
#pasteOptionsModal .modal-title,
#duplicateModal .modal-title {
  font-size: 1.3vw;
  font-weight: 600;
  margin: 0;
  line-height: 1.5;
}

#deleteModal .btn-close,
#pasteOptionsModal .btn-close,
#duplicateModal .btn-clos,
#NewPromptModalClose,
#promptModalClose,
#ContentLargeModalClose,
#imageUploadModalClose,
#settingsReuseModalClose {
  font-size: 2.4vw;
  font-weight: 700;
  line-height: 1;
  color: #000;
  background-color: transparent;
  border: 0;
  cursor: pointer;
  position: absolute;
  right: 1.1vw;
  top: 0.9vw;
  padding: 0;
  margin: 0;
  opacity: 0.5;
}

#promptSuggestionsRow {
  height: 20vw;
}

#deleteModal .btn-close:hover,
#duplicateModal .btn-close:hover,
#pasteOptionsModal .btn-close:hover {
  opacity: 1;
}

#deleteModal .modal-body,
#duplicateModal .modal-body,
#pasteOptionsModal .modal-body {
  padding: 1.5vw;
}

#deleteModal .modal-footer,
#duplicateModal .modal-footer,
#pasteOptionsModal .modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 1.1vw 1.5vw;
  border-top: 0.07vw solid #dee2e6;
  gap: 0.75vw;
}

#deleteModal .btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
  padding: 0.6vw 1.2vw;
  font-size: 0.9vw;
  border-radius: 0.3vw;
}

#deleteModal .btn-danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

#deleteModal .btn-secondary,
#duplicateModal .btn-secondary,
#pasteOptionsModal .btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
  padding: 0.6vw 1.2vw;
  font-size: 0.9vw;
  border-radius: 0.3vw;
}

#deleteModal .btn-secondary:hover,
#duplicateModal .btn-secondary:hover,
#pasteOptionsModal .btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

#duplicateModal .btn-primary,
#pasteOptionsModal .btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
  padding: 0.6vw 1.2vw;
  font-size: 0.9vw;
  border-radius: 0.3vw;
  margin-bottom: 0.75vw;
}

#duplicateModal .btn-primary:hover,
#pasteOptionsModal .btn-primary:hover {
  background-color: #0069d9;
  border-color: #0062cc;
}

#duplicateModal .d-grid,
#pasteOptionsModal .d-grid {
  display: grid;
  gap: 0.7vw;
}
#editModal .modal-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-height: 100vh;
}
#modalFormFields {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 5vw;
}

.modal-buttons {
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5vw;
}

.element-path-container {
  display: flex;
  align-items: center;
  gap: 0.5vw;
}

.path-label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9vw;
}

/* Text wrapping properties for HTML tab textarea - moved to proper selector below */

.element-path {
  display: flex;
  align-items: center;
  gap: 0.3vw;
  font-size: 0.9vw;
  overflow-x: scroll;
  max-width: 36vw;
  padding: 0.2vw 0;
  scrollbar-width: thin;
  scrollbar-color: #dee2e6 transparent;
  direction: rtl;
  margin-bottom: -0.5vw;
}

.element-path > * {
  direction: ltr;
}

.element-path::-webkit-scrollbar {
  height: 0.3vw;
}

.element-path::-webkit-scrollbar-track {
  background: transparent;
}

.element-path::-webkit-scrollbar-thumb {
  background-color: #dee2e6;
  border-radius: 0.15vw;
}

.element-path::-webkit-scrollbar-thumb:hover {
  background-color: #adb5bd;
}

.path-element {
  padding: 0vw 0.5vw;
  background-color: #f8f9fa;
  border: 0.07vw solid #dee2e6;
  border-radius: 0.25vw;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #007bff;
  text-decoration: none;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: fit-content;
}

.path-element:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
  color: #212529;
}

.path-element.current {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  cursor: default;
}

.path-element.current:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.path-separator {
  color: #6c757d;
  user-select: none;
}

.button-group {
  display: flex;
}
.close-button {
  color: #aaa;
  float: right;
  font-size: 2vw;
  font-weight: bold;
  cursor: pointer;
  position: absolute;
  right: 1vw;
  top: 1vw;
}
.close-button:hover,
.close-button:focus {
  color: black;
  text-decoration: none;
}
.form-body {
  padding: 0.7vw 0.7vw;
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 0px;
  padding-top: 0 !important;
}
.form-label {
  font-weight: 600;
  color: #2c3e50;
  text-align: right;
  align-self: center;
}
.checkbox-group {
  grid-column: 1 / -1;
}
.form-input {
  width: 7vw;
}

/* Ensure inputs in side-by-side containers have proper width */
.side-by-side-container .form-input {
  width: 100%;
  min-width: 6vw;
}

/* Adjust input width for font family field */
#cssFormFields .form-body.full-width .form-input {
  width: 100%;
}

/* Ensure color inputs have proper size */
.side-by-side-container .color-input {
  width: 3vw !important;
  min-width: 3vw !important;
}

/* Ensure color container is properly sized */
.side-by-side-container .color-container {
  display: flex;
  align-items: center;
  width: 100%;
}
.slider-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  grid-column: 1 / -1;
  margin-top: 1vw;
  display: contents;
}
.toggle-container {
  text-align: right;
  margin-bottom: 1vw;
}
.content-wrapper {
  transform: scale(0.9);
  transform-origin: top;
  margin-top: 0.7vw;
  justify-content: center;
  display: flex;
  height: -webkit-fill-available;
}
/* Mobile content view styles for content wrapper */
.mobile-content-view.content-wrapper,
.content-wrapper.mobile-content-view {
  width: 100% !important;
  max-width: 100% !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.mce-content-body {
  margin-bottom: 15vw !important;
}

/* Dynamic Field Management - Non-clickable spans */
.content-wrapper .mceNonEditable {
  pointer-events: none !important;
  cursor: not-allowed !important;
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  background: rgba(0, 124, 255, 0.08) !important;
  border-radius: 3px !important;
  padding: 1px 3px !important;
  display: inline-block !important;
}

/* Mobile content view styles for tables and other elements */
.mobile-content-view .content-wrapper table,
.content-wrapper.mobile-content-view table {
  width: 100% !important;
  border-collapse: collapse !important;
  table-layout: fixed !important;
}

.mobile-content-view .content-wrapper img,
.content-wrapper.mobile-content-view img {
  max-width: 100% !important;
  height: auto !important;
}

.mobile-content-view .content-wrapper div,
.content-wrapper.mobile-content-view div {
  max-width: 100% !important;
  overflow-x: hidden !important;
}
.modal-dialog-scrollable .modal-footer,
.modal-dialog-scrollable .modal-header {
  flex-shrink: 0;
}
.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  border: 0.07vw solid rgba(175, 175, 175, 0.1);
  border-radius: 0.3vw;
  outline: 0;
  height: 100%;
  padding: 0;
}
.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner-border 0.75s linear infinite;
}
.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  animation: spinner-grow 0.75s linear infinite;
}
.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}
.extra_border {
  border: 8px solid #6ad29c;
  outline: 0rem;
}
.properties-info {
  color: #007bff;
}
#propertiesPanel {
  position: absolute;
  width: 27vw;
  max-height: 39vw;
  overflow-y: auto;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid #ddd;
  border-radius: 0.3vw;
  padding: 0.7vw;
  padding-bottom: 0.14vw;
  display: none;
  box-shadow: 0px 0px 0.9vw 0.9vw rgba(0, 0, 0, 0.1);
  z-index: 12001;
  pointer-events: none;
  font-size: 0.85vw;
  transform: scale(0.9);
}
.property-item {
  margin-bottom: 0.6vw;
}
.property-name {
  font-weight: bold;
  display: inline-block;
  width: 8.5vw;
}
.property-value {
  display: inline-block;
  word-break: break-all;
}
.tox-tinymce-aux {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  z-index: 13000 !important;
}
.tox-tinymce {
  height: auto !important;
  min-height: 42vw;
  flex: 1;
  margin-top: -1vw;
  border-radius: 0 !important;
}
.custom-context-menu li {
  padding: 0.7vw;
  cursor: pointer;
}
.corner-model-input {
  width: 100%;
}
.corner-model-input {
  width: auto;
  padding: 0.6vw;
  box-sizing: border-box;
}
.corner-model-container .corner-model-input {
  width: 4.5vw;
  text-align: center;
}
.bottom-left-input,
.bottom-right-input,
.top-left-input,
.top-right-input {
  position: relative;
  width: 3vw;
  text-align: center;
  border: 1px solid #dde2e5;
  padding: 0 !important;
  border-radius: 0.3vw;
}
.modall {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2000;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}
#loadingModal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 15050;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}
.style-settings {
  display: flex;
  flex-direction: column;
  gap: 0.7vw;
}
.form-fields .tox-tinymce {
  flex: 1;
  min-height: 42vw;
  height: auto !important;
  width: 100% !important;
  margin-left: 0 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Fix TinyMCE scrolling: toolbar fixed, content scrollable */
.tox-tinymce .tox-toolbar-overlord {
  flex-shrink: 0 !important;
}

.tox-tinymce .tox-sidebar-wrap {
  flex: 1 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  min-height: 0 !important;
}

.tox-sidebar-wrap {
  margin-bottom: 7vw;
}

/* Ensure TinyMCE editor has proper spacing */
#editorContent .form-fields {
  padding: 1vw;
  display: block !important;
  width: 100% !important;
}
.main-container {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 0px;
  height: 100%;
  overflow-x: hidden;
  padding-top: 0.5vw;
}
.settings-container {
  flex: 1;
  max-width: 49%;
  background: white;
  padding-left: 0.5vw;
  padding-right: 0.5vw;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.settings-container form {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.form-fields {
  padding-top: 1vw;
  flex: 1;
  overflow-y: auto;
  padding-right: 0.7vw;
}
.preview-container {
  flex: 1;
  max-width: 50%;
  background: white;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.preview-container .preview-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  margin-bottom: 0; /* Changed from 20px to fix button clickability */
  position: relative; /* Added to ensure proper stacking context */
  z-index: 1; /* Ensure content stays below buttons */
}
#result {
  font-size: 0.85vw;
  color: grey;
  margin-top: -1.2vw;
  margin-bottom: -2vw;
  width: 50%;
}
#top_buttons {
  display: flex;
  margin-top: -1vw;
  padding-top: 1vw;
  padding-right: 0.5vw;
  padding-bottom: 0.5vw;
}
#contentPreview {
  flex: 1;
  overflow-y: auto;
  border: 0.5px solid #e9e9e9;
  background-color: #f4f7fa;
  scrollbar-width: thin;
  scrollbar-color: #0070d2 #f1f1f1;
  height: 100%;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Override transform for mobile view to prevent positioning issues */
#contentPreview.mobile-view {
  position: relative;
  top: 0 !important;
  left: 0 !important;
  transform: none !important;
  margin-top: 1.4vw !important;
}
.w-100 {
  padding: 0.35vw;
}
.btn-outline-danger {
  background-color: #ff0505;
  color: white;
}
.custom-context-menu ul {
  display: flex;
  flex-direction: column;
  list-style: none;
  margin: 0;
  padding: 0;
}
.custom-context-menu li {
  margin: 0;
  padding: 0;
}
.custom-context-menu button {
  background: none;
  border: none;
  color: #0070d2;
  text-align: left;
  width: 100%;
  padding: 0.4vw 0.7vw;
  cursor: pointer;
}
.custom-context-menu button:hover {
  background-color: #f0f0f0;
}
.deleteTrigger button {
  color: #ff0505;
}
.modal-content.glassy {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(0.7vw);
  -webkit-backdrop-filter: blur(0.7vw);
  box-shadow: 0 0.6vw 2.3vw rgba(31, 38, 135, 0.37);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 0.7vw;
  padding: 1.4vw;
}
.AI_create_and_edit,
button[data-mce-name="aiassistant"] {
  position: relative;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(0.7vw);
  -webkit-backdrop-filter: blur(0.7vw);
  z-index: 1;
  overflow: hidden;
  color: white !important;
  text-shadow: 0 0 0.7vw black;
}
.AI_create_and_edit::before,
button[data-mce-name="aiassistant"]::before {
  content: "";
  position: absolute;
  top: -0.3vw;
  left: -0.3vw;
  right: -0.3vw;
  bottom: -0.3vw;
  z-index: -1;
  border-radius: 0px;
  background: linear-gradient(
    90deg,
    #ff0000,
    #ff6700,
    #ff9200,
    #ffdc00,
    #dcff00,
    #bdff00,
    #30ff00,
    #00ffcf,
    #00ebff,
    #008fff,
    #0044ff,
    #8f00ff,
    #c400ff,
    #ff00a9,
    #ff0000
  );
  background-size: 400% 400%;
  animation: aiGradientBorder 25s linear infinite;
}
.AI_create_and_edit::after,
button[data-mce-name="aiassistant"]::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0px;
  z-index: 2;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
  box-shadow: inset 0 0 0.85vw 0.4vw rgba(255, 255, 255, 0.8);
}
.AI_create_and_edit:hover::after,
button[data-mce-name="aiassistant"]:hover::after {
  opacity: 1;
}
@media (max-height: 600px) {
  #actionButtons {
    gap: 0.35vw;
  }
  .toggle-container {
    margin-left: 0;
    margin-top: -0.14vw;
  }
}
@keyframes loader-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
@-webkit-keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}
@keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
@media (min-width: 576px) {
  .modal-dialog {
    max-width: 35vw;
    margin: 1.25vw auto;
  }
  .modal-dialog-scrollable {
    max-height: calc(100% - 2.5vw);
  }
  .modal-dialog-scrollable .modal-content {
    max-height: calc(100vh - 2.5vw);
  }
  .modal-dialog-centered {
    min-height: calc(100% - 2.5vw);
  }
  .modal-dialog-centered::before {
    height: calc(100vh - 2.5vw);
  }
  .modal-sm {
    max-width: 21vw;
  }
}
@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    max-width: 56vw;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    max-width: 80vw;
  }
}
@media (min-width: 576px) {
  .modal-dialog {
    max-width: 35vw;
    margin: 1.25vw auto;
  }
  .modal-dialog-scrollable {
    max-height: calc(100% - 2.5vw);
  }
  .modal-dialog-scrollable .modal-content {
    max-height: calc(100vh - 2.5vw);
  }
  .modal-dialog-centered {
    min-height: calc(100% - 2.5vw);
  }
  .modal-dialog-centered::before {
    height: calc(100vh - 2.5vw);
  }
  .modal-sm {
    max-width: 21vw;
  }
}
@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    max-width: 60vw;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    max-width: 85vw;
  }
}
@keyframes aiGradientBorder {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 400% 50%;
  }
}

.layer_3_z_index {
  z-index: 3000;
}

/* pop-up css start */

.tox-edit-area__iframe {
  height: calc(100vh - 10vw) !important;
  position: relative !important;
  padding-bottom: 4vw !important;
}

/* Base Reset */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
html {
  height: 100%;
}

/* Common Variables */
:root {
  --primary-color: #0066ff;
  --secondary-color: #64748b;
  --text-color: #1a2b3c;
  --border-color: #dde2e5;
  --bg-light: #f8fafc;
  --bg-dark: #e2e8f0;
  --success-color: #10b981;
  --font-size-base: 0.97vw;
  --font-size-large: 1.245vw;
  --font-size-heading: 1.5vw;
  --spacing-sm: 0.5vw;
  --spacing-md: 1vw;
  --spacing-lg: 1.5vw;
  --border-radius: 0.42vw;
  --transition-std: 0.3s ease;
}

/* Page Layout */
.page-body-1a2b {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  color: var(--text-color);
  background: linear-gradient(135deg, var(--bg-light), var(--bg-dark));
  width: 100%;
  overflow-x: hidden;
}

.wizard-container-1a2b {
  max-width: 97.75vw;
  position: relative;
  width: 100%;
  background: #ffffff;
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  overflow: hidden;
}

/* Typography */
.heading-2-1a2b {
  margin: 1.2vw 0 0.3vw 0;
  font-weight: 500;
  color: var(--text-color);
  font-size: 1.2vw;
}

.paragraph-1a2b {
  margin-bottom: var(--spacing-lg);
  line-height: 1.8;
  font-size: 1vw;
}

.text-button-1a2b {
  font-size: var(--font-size-large);
}

/* Lists */
.list-1a2b {
  margin-bottom: var(--spacing-lg);
  padding-left: 1.6vw;
}

.list-item-1a2b {
  margin-bottom: 0.63vw;
  line-height: 1.4;
  color: var(--text-color);
  font-size: 1vw;
}

/* Form Elements */
.form-label-1a2b {
  display: inline-block;
  font-size: 0.9vw;
  font-weight: 500;
  margin-bottom: 0;
}

.form-control-1a2b {
  padding: 0.56vw;
  border: 0.07vw solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  width: 100%;
  margin-bottom: 0.8vw;
  transition: border-color var(--transition-std),
    box-shadow var(--transition-std);
  background: #fff;
}

.form-control,
.form-control-1a2b,
.dropdown-item {
  font-size: 0.9vw;
}

.form-input-1a2b,
.form-select-1a2b {
  padding: 0.56vw;
  border: 0.07vw solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  width: 100%;
  margin-bottom: 0.8vw;
  transition: border-color var(--transition-std),
    box-shadow var(--transition-std);
  background: #fafafa;
  line-height: 1.4;
  padding-top: 0.4vw;
  padding-bottom: 0.4vw;
  height: 2.5vw !important;
}

.form-input-1a2b:focus,
.form-select-1a2b:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0.35vw rgba(0, 112, 210, 0.3);
}

.radio-input-1a2b {
  margin-right: 0.465vw;
  font-size: var(--font-size-base);
}

/* Buttons */
.nav-button-1a2b {
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-weight: normal;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  min-width: 7.815vw;
  padding: 0.575vw 0.75vw;
  font-size: 1vw;
}

.nav-button-1a2b:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-button-1a2b:hover:not(:disabled) {
  transform: translateY(-0.3vw);
  box-shadow: 0 0.3vw 0.6vw rgba(0, 0, 0, 0.1);
}

.next-button-1a2b {
  background: var(--primary-color);
  color: #fff;
}

.back-button-1a2b {
  background: #f1f5f9;
  color: var(--secondary-color);
}

/* Progress Indicators */
.progress-bar-1a2b {
  display: flex;
  justify-content: space-between;
  position: relative;
  padding: 0 3.12vw;
  width: 100%;
  margin-top: 0.8vw;
}

.progress-bar-1a2b::before {
  content: "";
  position: absolute;
  top: 1.17vw;
  left: 3.12vw;
  right: 3.12vw;
  height: 0.156vw;
  background: var(--bg-dark);
  border-radius: 0.078vw;
  z-index: 1;
}

.step-indicator-1a2b {
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
  position: relative;
  cursor: pointer;
}

.step-bubble-1a2b {
  width: 2vw;
  height: 2vw;
  border-radius: 50%;
  background: #fff;
  border: 0.156vw solid var(--bg-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5vw;
  font-weight: 600;
  margin-bottom: 0vw;
  transition: all var(--transition-std);
}

.step-label-1a2b {
  font-size: var(--font-size-heading);
  color: var(--secondary-color);
  text-align: center;
  transition: all var(--transition-std);
}

.step-indicator-1a2b.active .step-bubble-1a2b {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: #fff;
  box-shadow: 0 0.3vw 0.6vw rgba(0, 0, 0, 0.1);
}

.step-indicator-1a2b.active .step-label-1a2b {
  color: var(--primary-color);
  font-weight: 500;
}

.step-indicator-1a2b.completed .step-bubble-1a2b {
  background: var(--success-color);
  border-color: var(--success-color);
  color: #fff;
}

/* Step Panels */
.steps-content-1a2b {
  position: relative;
  width: 100%;
}

.step-panel-1a2b {
  display: none;
  width: 100%;
  background: #fff;
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  padding-right: 0;
  padding-top: 0;
  padding-left: 0;
  overflow-y: auto;
  margin-bottom: 4vw;
}

.step-panel-1a2b.active {
  display: block;
}

/* Modal Step Panels */
.modal-step-panel {
  display: none;
  width: 100%;
  background: #fff;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  margin-bottom: var(--spacing-md);
  padding-left: 0;
  overflow-y: auto;
  margin-bottom: 3vw;
}

tbody {
  display: table-row-group;
  vertical-align: middle;
  unicode-bidi: isolate;
  border-color: inherit;
}
table {
  border-spacing: 0;
}

table {
  border-collapse: separate;
  text-indent: initial;
  line-height: normal;
  font-weight: normal;
  font-size: medium;
  font-style: normal;
  color: -internal-quirk-inherit;
  text-align: start;
  border-spacing: 2px;
  white-space: normal;
  font-variant: normal;
}

.card-title.text-center.mb-3 {
  width: 4vw;
  margin-top: 1.2vw !important;
}

.modal-step-panel.active {
  display: block;
}

/* Navigation */
.navigation-buttons-1a2b {
  display: flex;
  justify-content: left;
  gap: 0.945vw;
  margin-top: 0 !important;
  bottom: 1vw;
  position: absolute;
}

/* Range Slider */
.slider-1a2b {
  width: 100%;
  appearance: none;
  height: 0.56vw;
  border-radius: 0.35vw;
  background: var(--border-color);
  outline: none;
  transition: background-color 0.2s ease;
  margin-top: 0.56vw;
}

.slider-1a2b::-webkit-slider-thumb {
  appearance: none;
  width: 1.5vw;
  height: 1.5vw;
  border-radius: 50%;
  background: #0070d2;
  cursor: pointer;
  transition: background-color var(--transition-std);
}

.slider-1a2b::-webkit-slider-thumb:hover {
  background: #005fb2;
}

.slider-1a2b::-moz-range-thumb {
  width: 1.5vw;
  height: 1.5vw;
  border-radius: 50%;
  background: #0070d2;
  cursor: pointer;
}

/* Code Containers */
.code-container-1a2b {
  margin-top: var(--spacing-md);
  padding: var(--spacing-sm);
  border: 0.07vw solid #ced4da;
  border-radius: 0.25vw;
  font-family: monospace;
  font-size: 0.9vw;
  white-space: pre-line;
  word-wrap: break-word;
  width: 100%;
}

.json-view-1a2b {
  background: #f1f3f5;
}

.prompt-view-1a2b {
  background: #e9ecef;
}

/* AI Button Styling */
.AI_create_and_edit-1a2b,
button[data-mce-name="aiassistant"] {
  position: relative;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(0.5vw);
  -webkit-backdrop-filter: blur(0.5vw);
  z-index: 1;
  overflow: hidden;
  color: white !important;
  border-radius: 0.63vw;
  padding: 0.545vw 1.275vw;
  text-shadow: 0 0 10px black !important;
  font-weight: normal;
  font-size: 1vw;
}

.AI_create_and_edit-1a2b::before,
button[data-mce-name="aiassistant"]::before {
  content: "";
  position: absolute;
  top: -0.8vw;
  left: -0.8vw;
  right: -0.8vw;
  bottom: -0.8vw;
  z-index: -1;
  border-radius: 0.63vw;
  background: linear-gradient(
    90deg,
    #ff0000,
    #ff6700,
    #ff9200,
    #ffdc00,
    #dcff00,
    #bdff00,
    #30ff00,
    #00ffcf,
    #00ebff,
    #008fff,
    #0044ff,
    #8f00ff,
    #c400ff,
    #ff00a9,
    #ff0000
  );
  background-size: 400% 400%;
  animation: aiGradientBorder 25s linear infinite;
}

.AI_create_and_edit-1a2b::after,
button[data-mce-name="aiassistant"]::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 0.63vw;
  z-index: 2;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.4s ease-in-out;
  box-shadow: inset 0 0 0.6vw 0.3vw rgba(255, 255, 255, 0.8);
}

.AI_create_and_edit-1a2b:hover::after,
button[data-mce-name="aiassistant"]:hover::after {
  opacity: 1;
}

@keyframes aiGradientBorder {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 400% 50%;
  }
}

/* AI-activated Content Preview radio button styling */
.form-label-1a2b.ai-content-preview-active {
  position: relative;
  color: #155724 !important;
  font-weight: 600 !important;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.4vw 0.8vw;
  border-radius: 0.42vw;
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.3s ease;
}

.form-label-1a2b.ai-content-preview-active::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  z-index: -1;
  border-radius: 0.42vw;
  background: linear-gradient(
    90deg,
    #ff0000,
    #ff6700,
    #ff9200,
    #ffdc00,
    #dcff00,
    #bdff00,
    #30ff00,
    #00ffcf,
    #00ebff,
    #008fff,
    #0044ff,
    #8f00ff,
    #c400ff,
    #ff00a9,
    #ff0000
  );
  background-size: 400% 400%;
  animation: aiGradientBorder 25s linear infinite;
}

.form-label-1a2b.ai-content-preview-active::after {
  content: "";
  position: absolute;
  right: -1vw;
  width: 1.2vw;
  height: 1.2vw;
  background: linear-gradient(
    90deg,
    #ff0000,
    #ff6700,
    #ff9200,
    #ffdc00,
    #dcff00,
    #bdff00,
    #30ff00,
    #00ffcf,
    #00ebff,
    #008fff,
    #0044ff,
    #8f00ff,
    #c400ff,
    #ff00a9,
    #ff0000
  );
  background-size: 400% 400%;
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 0 0px 2px rgb(191 191 191 / 80%);
  animation: aiGradientBorder 25s linear infinite,
    ai-content-preview-pulse 2s ease-in-out infinite;
  z-index: 2;
}

/* Pulse animation for the AI indicator dot */
@keyframes ai-content-preview-pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.3);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

/* Disabled state styling with tooltip */
.form-label-1a2b.content-preview-disabled {
  position: relative;
  opacity: 0.6;
  cursor: help;
}

.form-label-1a2b.content-preview-disabled::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.5vw 0.8vw;
  border-radius: 0.3vw;
  font-size: 0.75vw;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
  margin-bottom: 0.3vw;
}

.form-label-1a2b.content-preview-disabled::after {
  content: "";
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 0.3vw solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
  z-index: 1000;
}

.form-label-1a2b.content-preview-disabled:hover::before,
.form-label-1a2b.content-preview-disabled:hover::after {
  opacity: 1;
}

/* Responsive Adjustments */
@media (max-width: 60vw) {
  .wizard-container-1a2b {
    padding: 1.56vw;
  }

  .progress-bar-1a2b {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.56vw;
    padding-left: 1.56vw;
  }

  .progress-bar-1a2b::before {
    width: 0.156vw;
    height: 100%;
    left: 1.245vw;
    top: 0;
  }

  .step-indicator-1a2b {
    flex-direction: row;
    gap: 0.945vw;
  }

  .step-bubble-1a2b {
    margin-bottom: 0;
  }

  .navigation-buttons-1a2b {
    flex-direction: column-reverse;
  }

  .nav-button-1a2b {
    width: 100%;
  }

  .step-panel-1a2b {
    padding: 1.245vw;
  }
}

/* Bootstrap Overrides */
.modal-dialog {
  max-width: 100%;
  margin: 0vw;
}

.modal-header {
  padding: 0.5vw;
}

.card-body {
  flex: 1 1 auto;
  min-height: 0.07vw;
  padding: var(--spacing-md);
  padding-top: 0;
  padding-bottom: 0;
}

/* Form Layout Helpers */

.goal-inline label {
  font-size: 0.95vw;
}

#otherGoalContainer textarea {
  height: 3vw;
}

/* Form style adjustments */
.form-select-1a2b,
.custom-select {
  height: auto;
  padding-top: 0.4vw;
  padding-bottom: 0.4vw;
  line-height: 1.4;
}

select.form-select-1a2b option,
select.custom-select option {
  padding: 0.3vw;
  line-height: 1.4;
}

.d-flex .form-select-1a2b,
.d-flex .custom-select {
  max-height: none;
  overflow: visible;
}

.form-group {
  margin-bottom: 0;
}
#json-code-view,
#prompt-request {
  margin-top: 1vw;
  padding: 0.5vw;
  border: 0.07vw solid #ced4da;
  border-radius: 0.25vw;
  font-family: monospace;
  font-size: 0.9vw;
  white-space: pre-line;
  word-wrap: break-word;
  width: 100%;
}
#json-code-view {
  background: #f1f3f5;
}
.modal-body {
  overflow: auto;
}
/* View toggle button styles */
.view-toggle-buttons {
  display: flex;
  overflow: hidden;
  margin-bottom: 1vw;
}

.edit-preview-toggle.view-toggle-buttons {
  margin-bottom: 0;
}

/* Content preview container styles for different views */
#content-preview-container.desktop-view {
  width: 100%;
  max-width: 100%;
  transition: width 0.3s ease;
}

#content-preview-container.mobile-view {
  width: 30vw;
  max-width: 100%;
  margin: 0 auto;
  border: 0.07vw solid #ddd;
  border-radius: 0.6vw;
  transition: width 0.3s ease;
  box-shadow: 0 0.15vw 0.6vw rgba(0, 0, 0, 0.1);
}

/* Button styles - matches the existing UI style */
.view-toggle-btn {
  border: 1px solid #dee2e6;
  background-color: #fff;
  color: black;
}

.view-toggle-btn.active {
  background-color: #0d6efd;
  color: white;
  border-color: #0d6efd;
}

/* Icons using emojis for simplicity */
.icon-desktop::before {
  content: "💻";
  margin-right: 0.25vw;
}

.icon-mobile::before {
  content: "📱";
  margin-right: 0.25vw;
}
#modal-error-message {
  word-break: break-word;
  max-height: 12.5vw;
  overflow-y: auto;
  margin: 0.6vw 0;
}

/* Action buttons container */
.action-buttons-container {
  display: flex;
  justify-content: flex-end;
  gap: 0.5vw;
  margin-top: 1vw;
  padding: 0.5vw;
  border-top: 0.07vw solid #dee2e6;
  position: sticky;
  bottom: 0;
  background-color: #fff;
  z-index: 100;
}
.step-panel-1a2b[data-step="3"] {
  display: none;
}
.step-panel-1a2b[data-step="3"].active {
  display: block;
}
/* AI Modal Styles */
#loadingModal-1a2b .spinner-border {
  width: 3vw;
  height: 3vw;
}

/* AI Modal Body */
.ai-modal-body {
  padding: 2vw !important;
  min-height: 25vw;
}

/* AI Progress Circle */
.ai-progress-container {
  display: flex;
  justify-content: center;
  margin-bottom: 2vw;
}

.ai-progress-circle {
  width: 6.8vw;
  height: 6.8vw;
  border-radius: 50%;
  background: conic-gradient(#007bff 0deg, #e9ecef 0deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: background 0.3s ease;
}

.ai-progress-circle::before {
  content: "";
  position: absolute;
  width: 5.1vw;
  height: 5.1vw;
  border-radius: 50%;
  background: white;
}

.ai-progress-text {
  position: relative;
  z-index: 1;
  font-size: 1.2vw;
  font-weight: 600;
  color: #007bff;
}

/* AI Modal Heading */
.ai-modal-heading {
  font-size: 1.5vw;
  font-weight: 600;
  color: #333;
  margin-bottom: 1.5vw;
}

/* AI Model List Container */
.ai-model-list-container {
  max-height: 9.5vw;
  overflow-y: auto;
  overflow-x: hidden;
  margin-bottom: 1.5vw;
  position: relative;
  border-radius: 0.5vw;
  padding: 0.2vw;
}

.ai-model-list-container::after {
  content: '';
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1.5vw;
  background: linear-gradient(to bottom, rgba(248,249,250,0) 0%, rgba(248,249,250,1) 100%);
  pointer-events: none;
  z-index: 2;
  margin-top: -1.5vw;
}

.ai-model-list {
  display: flex;
  flex-direction: column;
  gap: 0.5vw;
  transition: all 0.3s ease;
  padding-bottom: 2vw;
}

/* AI Model Entry */
.ai-model-entry {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.8vw 1.5vw;
  border-radius: 0.5vw;
  background: #f8f9fa;
  border: none;
  transition: all 0.3s ease;
  font-size: 0.9vw;
  color: #495057;
  margin: 1vw 1vw 0 1vw;
}

.ai-model-entry.hidden {
  opacity: 0;
  transform: translateY(2vw);
}

/* Removed AI Current Goal Display - now integrated into status message */

/* AI Model Status Indicator */
.ai-model-status {
  margin-left: 0.8vw;
  width: 1vw;
  height: 1vw;
  border-radius: 50%;
  display: inline-block;
}

.ai-model-status.processing {
  background: #2196f3;
  animation: ai-pulse 1.5s ease-in-out infinite;
}

.ai-model-status.success {
  background: #4caf50;
}

.ai-model-status.error {
  background: #f44336;
}

/* AI Pulse Animation */
@keyframes ai-pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.2);
  }
}

/* AI Status Message */
.ai-status-message {
  font-size: 1vw;
  color: #666;
  margin-bottom: 2vw;
  min-height: 1.5vw;
}

/* AI Modal Actions */
.ai-modal-actions {
  margin-top: 1.5vw;
}

.ai-cancel-btn {
  padding: 0.6vw 1.5vw;
  font-size: 0.9vw;
  border-radius: 0.4vw;
  border-width: 0.1vw;
}

/* AI Error Styles */
.ai-error-icon {
  margin-bottom: 1.5vw;
}

.ai-error-icon i {
  font-size: 3vw;
  color: #dc3545;
}

.ai-error-heading {
  font-size: 1.3vw;
  color: #dc3545;
  margin-bottom: 1vw;
}

.ai-error-message {
  font-size: 0.9vw;
  color: #dc3545;
  margin-bottom: 2vw;
  word-break: break-word;
  max-height: 12vw;
  overflow-y: auto;
}

.ai-error-actions {
  display: flex;
  justify-content: center;
  gap: 1vw;
}

.ai-retry-btn,
.ai-close-btn {
  padding: 0.6vw 1.5vw;
  font-size: 0.9vw;
  border-radius: 0.4vw;
  border-width: 0.1vw;
}

/* Modern Modal Enhancements */
.modal-dialog-custom {
  max-width: 52vw !important;
  width: 52vw !important;
}

/* Smooth transitions for all modal elements */
.ai-modal-body * {
  transition: all 0.3s ease;
}

/* Enhanced progress circle with gradient and glow */
.ai-progress-circle {
  box-shadow: 0 0.2vw 1vw rgba(0, 123, 255, 0.3);
  animation: ai-progress-glow 2s ease-in-out infinite alternate;
}

@keyframes ai-progress-glow {
  0% {
    box-shadow: 0 0.2vw 1vw rgba(0, 123, 255, 0.3);
  }
  100% {
    box-shadow: 0 0.3vw 1.5vw rgba(0, 123, 255, 0.5);
  }
}

/* Enhanced model entry animations */
.ai-model-entry {
  transform: translateY(0);
  opacity: 1;
  animation: ai-slide-in 0.3s ease-out;
}

@keyframes ai-slide-in {
  from {
    transform: translateY(2vw);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.ai-model-entry.processing {
  animation: ai-processing-pulse 1.5s ease-in-out infinite;
}

@keyframes ai-processing-pulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0.1vw 0.5vw rgba(33, 150, 243, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0.2vw 1vw rgba(33, 150, 243, 0.5);
  }
}

.ai-model-entry.success {
  animation: ai-success-bounce 0.5s ease-out;
}

@keyframes ai-success-bounce {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.ai-model-entry.error {
  animation: ai-error-shake 0.5s ease-out;
}

@keyframes ai-error-shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-0.2vw);
  }
  75% {
    transform: translateX(0.2vw);
  }
}

/* Enhanced button hover effects */
.ai-cancel-btn:hover {
  transform: translateY(-0.1vw);
  box-shadow: 0 0.3vw 0.8vw rgba(108, 117, 125, 0.3);
}

.ai-retry-btn:hover {
  transform: translateY(-0.1vw);
  box-shadow: 0 0.3vw 0.8vw rgba(0, 123, 255, 0.3);
}

.ai-close-btn:hover {
  transform: translateY(-0.1vw);
  box-shadow: 0 0.3vw 0.8vw rgba(108, 117, 125, 0.3);
}

/* Enhanced modal content */
.custom-modal {
  border-radius: 1vw;
  box-shadow: 0 1vw 3vw rgba(0, 0, 0, 0.2);
  border: none;
  overflow: hidden;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 75vw) {
  .modal-dialog-custom {
    max-width: 90vw !important;
    width: 90vw !important;
  }

  .ai-progress-circle {
    width: 12vw;
    height: 12vw;
  }

  .ai-progress-circle::before {
    width: 9vw;
    height: 9vw;
  }

  .ai-progress-text {
    font-size: 1.8vw;
  }
}

/* Modal overflow handling */
.modal-open .modal {
  overflow-y: hidden;
}

/* Modal overflow auto */
.modal-open .modal {
  overflow-y: hidden;
}

/* And update the modal-body styling */
.modal-body {
  overflow: auto;
  max-height: 94vh; /* Limit height relative to viewport */
  padding-bottom: 20px;
}

/* Output container styling */
.output-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1vw;
  width: 100%;
}

#json-code-view,
#prompt-request {
  flex: 1 1 100%;
  min-width: 45%;
  max-height: 12.5vw;
  overflow-y: auto;
  padding: 0.6vw;
  border: 0.07vw solid #ced4da;
  border-radius: 0.25vw;
  background-color: #f8f9fa;
  font-family: monospace;
  font-size: 0.9vw;
  white-space: pre-wrap;
  word-break: break-word;
}

@media (min-width: 48vw) {
  #json-code-view,
  #prompt-request {
    flex: 1 1 45%;
  }
}

pre {
  text-wrap: auto;
  font-size: 0.9vw;
}

/* Modal open state */
.modal-open {
  overflow: auto !important;
  padding-right: 0 !important;
}

.modal-backdrop.show {
  opacity: 0.5;
}

.modal.fade.show {
  display: block;
}

/* For ensuring the button stays hidden */
#get_started_ai_cb_button.d-none {
  display: none !important;
}
.p-3 {
  padding: 1.5vw !important;
}

/* Make tabs container always full width */
.tabs-container {
  width: 100% !important;
}

/* Tab navigation should always be full width */
#outputTabs {
  width: 100% !important;
}

/* Results tabs font size */
#outputTabs .nav-link {
  font-size: 0.9vw;
  padding: 0.5vw;
}

/* Make the tab navigation wrapper full width */
.tab-navigation-wrapper {
  width: 100%;
}

/* Desktop view */
#content-preview-container.desktop-view {
  width: 100%;
  max-width: none;
  margin: 0 auto;
}

/* Mobile view */
#content-preview-container.mobile-view {
  width: 30vw;
  max-width: 100%;
  margin: 0 auto;
  border: 0.07vw solid #ccc;
  border-radius: 1.25vw;
  padding: 0.6vw;
  overflow-x: hidden;
}

.mobile-view_scaled_7 {
  width: 17.2vw;
  max-width: 100%;
  margin: 0 auto;
  border: 0.07vw solid #ccc;
  border-radius: 1.25vw;
  padding: 0.6vw;
  overflow-x: hidden;
}

.form-control {
  background-color: #fafafa;
}
.modal-header .close {
  padding: 1vw;
}

@media (min-width: 48vw) {
  .col-md-6 {
    max-width: 100%;
    padding-left: 0;
    padding-right: 0;
    margin-top: -0.5vw;
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
}

.card {
  border: none;
  /* border-bottom: 0.07vw solid rgba(0, 0, 0, .125); */
}

.mb-3,
.my-3 {
  margin-bottom: 0vw !important;
  margin: 0.5vw;
  margin-top: 0.5vw !important;
}

.disabled-2b {
  opacity: 0.5;
  cursor: not-allowed;
}

.row {
  margin-left: 0;
  margin-right: 0;
}

input[type="radio"] {
  vertical-align: middle;
  width: 1vw;
  height: 1vw;
}

.tab-navigation-wrapper {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: white;
  width: 100%;
  padding: 0.6vw 0;
  border-bottom: 0.07vw solid #dee2e6;
}

.nav-link {
  display: block;
  padding: 0.5vw 0.7vw;
  font-size: 1.1vw;
}

/* Main container styles */
.main-container {
  display: flex;
  width: 100%;
}

/* Settings container */
.settings-container {
  width: 50%;
  margin-top: 0;
  margin-top: 0vw !important;
}

/* Client side message */
.client-side-message {
  padding-left: 15%;
  padding-right: 15%;
  width: 100%;
  z-index: 60000;
  position: fixed;
  display: none;
}

/* About section styles */
.about-section {
  overflow: auto;
  /* Height will be set dynamically by JavaScript */
  margin-bottom: -1vw;
  margin-top: 1vw;
  border: 0.1vw solid #e6e3e3;
  border-radius: 0.25vw;
}

/* Navigation buttons explanation */
.nav-button-explanation {
  font-size: 0.9vw;
  color: #a00;
  margin-left: 1vw;
  margin-top: 1vw;
}

/* Card body styles */
.card-body-no-padding {
  padding-left: 0;
  padding-right: 0;
}

.mb1vw {
  margin-bottom: 1vw;
}

.card-body-left-padding-only {
  padding-left: 0;
}

.card-body-right-padding-only {
  padding-right: 0;
}

.card-body-no-margin {
  margin-bottom: 0.5vw;
  padding-right: 0;
}

/* Form input styles */
.form-textarea {
  min-height: 4vw;
  width: 100%;
  padding: 1vw;
  border: 0.07vw solid #ced4da;
  border-radius: 0.56vw;
  background-color: #f7f7f7;
  height: 11vw !important;
  font-size: 0.9vw;
  font-family: system-ui;
}

/* Goal settings container */
.goal-settings-container {
  overflow: auto;
  height: 8.5vw;
  border-bottom: 0.06vw solid #e1dede;
}

/* Other goal container */
.other-goal-container {
  display: none;
  margin-top: 0.5vw;
}

/* Navigation buttons container */
.navigation-buttons-mt {
  padding-top: 1vw !important;
  width: 48%;
  background: white;
  border-top: 1px solid #e5e5e5;
}

/* Content preview container */
.content-preview-container {
  min-height: 18.75vw;
  background-color: #fff;
  overflow: hidden;
  border-radius: 0 !important;
  transform: scale(0.7);
  position: relative;
  transform-origin: top;
  height: 100%;
}

/* Preview card body */
.preview-card-body {
  overflow: auto;
  /* Height will be set dynamically by JavaScript */
  padding-left: 0;
  margin-top: 0.5vw;
}

/* Error message container */
.error-message-container {
  display: none;
  color: #dc3545;
  margin-top: 0.625vw;
}

/* Show date field when checkbox is checked - works with main .hide-date-field rule at line 3221 */
.hide-date-field.show {
  display: block;
}

/* Date input width */
.date-input-width {
  width: 40%;
}

/* Event select */
.event-select {
  width: auto;
}

/* Preview container */
.friendly-name-input {
  width: 43.5%;
  margin-bottom: 0;
}

/* Only show red box shadow when the input is empty */
.friendly-name-input:placeholder-shown {
  box-shadow: 0 0 0.5vw 0.25vw rgba(255, 0, 0, 0.29);
}

/* Modal dialog styles */
.modal-dialog-left {
  margin: 0;
  max-width: 50vw;
  width: 50vw;
  position: absolute;
  left: 0;
  top: 0;
  height: 100vh;
  display: flex;
}

.modal-content-full-height {
  height: 100vh;
  border-radius: 0;
  border-right: 0.07vw solid #dee2e6;
  background-color: #ffffff;
  box-shadow: 0 0 1vw rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
}

.modal-close-button {
  top: -0.5vw;
  font-size: 2.4vw;
}

.modal-title-styled {
  margin-top: 0;
  padding: 0.5vw 1vw 0.5vw 1vw;
  margin-bottom: 0;
  font-size: 1.5vw;
  display: inline-block;
  margin-right: 2vw; /* Added margin to create space between title and tabs */
}

/* Modal header with tabs */
.modal-header-with-tabs {
  border-bottom: 0.07vw solid #dee2e6;
  padding-bottom: 0;
  margin: 0.5vw;
}

/* Modal header content - flex container for title and tabs */
.modal-header-content {
  display: flex;
  justify-content: flex-start; /* Changed from space-between to flex-start to align tabs to the left */
  align-items: center;
  padding-right: 1vw;
  border-bottom: 0.07vw solid #dee2e6;
}

/* Edit modal tabs */
.edit-modal-tabs {
  border-bottom: none;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start; /* This will align the tabs to the left within their container */
  margin-left: 0; /* Ensure no left margin pushes tabs away from the left */
}

.edit-modal-tabs .nav-item {
  margin-bottom: -0.07vw;
}

.edit-modal-tabs .nav-link {
  border: 0.07vw solid transparent;
  border-top-left-radius: 0.25vw;
  border-top-right-radius: 0.25vw;
  padding: 0.5vw 1vw;
  font-size: 1vw;
  color: #007bff;
  cursor: pointer;
}

.html-editor-textarea {
  width: 100%;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 0.8vw;
  line-height: 1.5;
  /* FIXED: Enable text wrapping for better code readability */
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  /* FIXED: Proper scrolling behavior */
  overflow-x: auto;
  overflow-y: auto;
  border: none;
  outline: none;
  resize: none;
  padding: 1vw;
  background-color: #f8f9fa;
  color: #333;
  tab-size: 2;
  caret-color: rgb(51, 51, 51);
  z-index: 2;
  position: relative;
  /* FIXED: Ensure proper scrollbar behavior */
  scrollbar-width: thin;
  scrollbar-color: #0070d2 #f1f1f1;
}

#htmlFormFields {
  margin-bottom: 4vw;
  padding: 0.5vw;
  padding-top: 0;
  margin-top: 0.5vw;
}

.tox-statusbar {
  display: none !important;
}

code[class*="language-"],
pre[class*="language-"] {
  color: #000;
  background: 0 0;
  text-shadow: 0 1px #fff;
  font-family: Consolas, Monaco, "Andale Mono", "Ubuntu Mono", monospace;
  font-size: 0.6vw !important;
  text-align: left;
  white-space: break-spaces !important;
  word-spacing: normal;
  word-break: normal;
  word-wrap: normal;
  line-height: 1.5;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  hyphens: none;
}

.edit-modal-tabs .nav-link:hover {
  border-color: #e9ecef #e9ecef #dee2e6;
}

.edit-modal-tabs .nav-link.active {
  color: #495057;
  background-color: #f7f7f7;
  border-color: #dee2e6 #dee2e6 #fff;
  border-top: 0.4vw solid #fe0000;
}

td {
  word-wrap: break-word;
  -ms-word-break: break-all;
  word-break: break-all;
  word-break: break-word;
}

.mce-content-body {
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-wrap: break-word;
  -ms-word-break: break-all;
  word-break: break-all;
  word-break: break-word;
}

.modal-form-fields-container {
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1 1 auto;
  min-height: 0;
  height: 100%;
}

/* Fix dual scrollbar issue: disable outer scrolling for Editor tab only */
#editorContent .modal-form-fields-container {
  overflow: hidden !important;
}

.modal-button-margin {
  margin-left: 0.5vw;
}

/* Modal dialog centered */
.modal-dialog-custom {
  margin: 10vh auto;
  max-width: 30vw;
  width: 30vw;
}

/* Error details */
.error-details {
  display: none;
}

.error-detail-content {
  max-height: 12.5vw;
  overflow-y: auto;
}

/* Retry button */
.retry-button {
  display: none;
}

/* Loading message */
.loading-message-heading {
  padding: 1vw;
  font-size: 1.5vw;
}

.loading-message-text {
  margin-top: 0;
  margin-bottom: 1.7vw;
}

/* Error state */
.error-state {
  display: none;
}

/* Context menu items */
.context-menu-item-top {
  padding-top: 0.35vw;
}

.context-menu-item-bottom {
  padding-bottom: 0.35vw;
}

/* AI create and edit button */
.ai-create-edit-button {
  text-shadow: 0 0 0.625vw black;
}

/* Modall content width */
.modall-content-width {
  width: 3.33vw;
}

/* Spinner dimensions */
.spinner-dimensions {
  width: 3.33vw;
  height: 3.33vw;
}

/* Transparent modal content */
.modal-content-transparent {
  background: transparent;
  border: none;
  box-shadow: none;
}

/* Heading font size */
.heading-font-size {
  font-size: 1.1vw;
}

/* Form label margin */
.form-label-margin {
  margin-right: 0.5vw;
}

/* Radio input margin */
.radio-input-margin {
  margin-right: 0.2vw;
}

/* Text align center */
.text-center-align {
  text-align: center;
}

/* Goal settings heading margin */
.goal-heading-margin {
  margin-bottom: 1.3vw;
  padding-left: 1vw;
}

/* Flex column styles */
.flex-column-30 {
  flex: 0 0 30%;
}

.flex-column-45 {
  flex: 0 0 45%;
}

/* Form display contents */
.form-display-contents {
  display: contents;
}

/* Card margin top */
.card-margin-top-0 {
  margin-top: 0 !important;
}

/* Full width input */
.full-width-input {
  width: 55%;
}

/* Label bottom margin */
.label-bottom-margin {
  margin-bottom: 0.5vw;
}

/* Textarea bottom margin */
.textarea-bottom-margin {
  width: 100%;
  margin-bottom: 0.4vw;
}

/* email css */

wbr {
  display: none;
}
body {
  margin: 0;
  padding: 0;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}
a,
div,
span,
td {
  -webkit-text-size-adjust: none;
}
table {
  border-spacing: 0;
}
table td {
  border-collapse: collapse;
}
.ExternalClass {
  width: 100%;
}
.ExternalClass,
.ExternalClass div,
.ExternalClass font,
.ExternalClass p,
.ExternalClass span,
.ExternalClass td {
  line-height: 100%;
}
.ReadMsgBody {
  width: 100%;
  background-color: #ebebeb;
}
table {
  mso-table-lspace: 0pt;
  mso-table-rspace: 0pt;
  border: none;
}
img {
  -ms-interpolation-mode: bicubic;
}
.yshortcuts a {
  border-bottom: none !important;
}
a[href^="x-apple-data-detectors:"],
a[x-apple-data-detectors] {
  color: inherit !important;
  text-decoration: none !important;
  font-size: inherit !important;
  font-family: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}
.open-paragrah {
  font-size: 16px !important;
  line-height: 25px;
  font-family: arial !important;
  font-size: 16px !important;
}
.add_vertical_divider_in_article {
  width: 0.6vw !important;
}
p {
  margin-top: 1vw;
  margin-bottom: 1vw;
}
.desktop_view {
  display: block;
}
.mobile_view {
  display: none;
}
.column {
  padding: 10px;
}
@media screen and (max-width: 599px) {
  .desktop_view {
    display: none;
  }
  .mobile_view {
    display: block;
  }
  .add_padding_bottom {
    padding-bottom: 0.7vw !important;
  }
  .add_vertical_divider_in_article {
    width: 0 !important;
  }
  .container,
  .force-row {
    width: 100% !important;
    max-width: 100% !important;
  }
  .section-title {
    font-size: 1.3vw !important;
  }
  .max-width {
    width: 100% !important;
    max-width: 100% !important;
  }
  .max-width-header-logo {
    width: 70%;
  }
  .max-width-header-logo-ta {
    width: 90%;
  }
  .text-center {
    width: 70% !important;
    text-align: center !important;
  }
  .ctpd {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .column {
    display: block !important;
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 0;
    padding-right: 0;
  }
  .display_block {
    display: block !important;
    width: 100% !important;
    max-width: 100% !important;
  }
}
@media screen and (max-width: 415px) {
  .container-padding {
    padding-left: 0.85vw !important;
    padding-right: 0.85vw !important;
  }
}
.ql-align-center {
  text-align: center;
}
.ql-align-left {
  text-align: left;
}
.ql-align-right {
  text-align: right;
}
.ql-align-justify {
  text-align: justify;
}

/* appc css */

.modall {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 5050;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
  /* Prevent modall from closing when clicking outside */
  pointer-events: none;
}

/* Ensure the modall content itself can receive clicks */
.modall-content {
  pointer-events: auto;
}

/* Make sure all interactive elements inside modalls are clickable */
.modall-content button,
.modall-content a,
.modall-content input,
.modall-content select,
.modall-content textarea,
.modall-content .form-control,
.modall-content .btn,
.modall-content label {
  pointer-events: auto;
}

.bd-example-modal-lg .modall-dialog {
  display: table;
  position: relative;
  margin: 0 auto;
  top: calc(50% - 24px);
}
.bd-example-modal-lg .modall-dialog .modall-content {
  background-color: transparent;
  border: none;
}

/* Only allow close buttons to be clickable in modall */
.modall .close,
.modall .btn-close,
.modall .close-button,
.modall .modall-close-button,
.modall button[data-dismiss="modall"] {
  pointer-events: auto;
}

.subText {
  font-size: 0.85vw;
  padding-top: 0.5vw;
  color: #5f5e5e;
  font-family: system-ui;
  margin-bottom: -0.3vw;
  font-weight: normal;
  line-height: 2;
}
.subText2 {
  font-size: 0.85vw;
  padding-bottom: 0.35vw !important;
  color: #5f5e5e;
  padding-left: 0.14vw;
  font-family: system-ui;
  font-weight: normal;
  line-height: 2;
}

.question2 {
  min-height: 2vw;
  font-size: 0.85vw !important;
  border-color: #e0e0e0 !important;
  background-color: #f5f5f5;
  border: 1px grey;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.3vw;
}

.alert {
  position: sticky;
  border-radius: 0 !important;
  border: 1px solid transparent;
  text-align: left;
  font-size: 0.9vw;
  padding: 0.7vw 2.1vw 0.7vw 1vw;
  margin-bottom: 0 !important;
  width: 100%;
  z-index: 12000;
}
.alert-dismissible .close {
  position: absolute;
  right: 0.6vw;
  color: inherit;
  padding: 0 !important;
  top: 0.14vw;
  font-size: 2.1vw !important;
  width: auto;
}
.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #819c7b !important;
}
.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #b47178;
}
.alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #d9bd65;
}

/* iframe css */

iframe {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  -ms-interpolation-mode: nearest-neighbor;
}

iframe {
  transform: translateZ(0);
  will-change: transform;
}

/* Mobile view class for iframe - matches the implementation in appC_css.css */
.mobileViewClass {
  width: 32vw;
  left: 50%;
  transform: translate(-50%, 0);
}

.mobileViewClass table {
  table-layout: fixed;
  width: 100%;
}

/* Mobile styles that will be applied via JavaScript */
.mobile-content-view .container,
.mobile-content-view .force-row {
  width: 100% !important;
  max-width: 100% !important;
}

/* Content Saves List Styles */

/* Dynamic preview container for Recent saves tab */
.dynamic-preview-container {
  transition: height 0.3s ease;
}

.dynamic-preview-container .save-preview-iframe-container {
  position: relative;
  width: 100%;
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius, 0.42vw);
  background-color: #f8fafc;
  margin-top: 0.5vw;
  overflow: hidden;
  margin-bottom: 5vw;
}

/* Classes needed for the content saves list component */
.explainerfourAndHalf {
  font-size: 0.9vw !important;
  line-height: 2.1vw;
  padding: 0.14vw;
  font-family: system-ui;
  color: #000c48;
  font-weight: 500;
}

.related_fields {
  border: 0.5px solid rgb(219, 219, 219);
  border-radius: 0.3vw;
  padding: 0.56vw;
  margin-bottom: 1.4vw;
}

.assetCard {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border-left: none;
  border-top: none;
  border-bottom: none;
  border-right: none;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.assetRowContainer {
  padding: 1vw;
  width: 100%;
  background: white;
  font-size: 0.7vw !important;
  color: #525252 !important;
  text-decoration: none !important;
  text-align: left !important;
  transition: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  outline: none;
  height: 2.3vw;
}

.list_view_table_row_container {
  max-height: 14vw;
  overflow-y: auto;
  margin-bottom: 1.4vw;
}

.table_tr_row {
  cursor: pointer;
}

.spanRowData {
  font-size: 0.75vw !important;
  color: #525252;
}

.toggleSortByFields {
  color: #428bca !important;
  font-size: 0.75vw !important;
  text-align: left;
  background-color: transparent !important;
  outline: 0 !important;
  border-radius: 0 !important;
  padding-left: 1.4vw;
}

.subText2 {
  font-size: 0.85vw;
  padding-bottom: 0.35vw !important;
  color: #5f5e5e;
  padding-left: 0.14vw;
  font-family: system-ui;
  font-weight: normal;
  line-height: 2;
}

.question2 {
  height: 2vw;
  background: 0 0;
  outline: 0;
  border: solid 1px #bebebe;
  font-size: 0.9vw !important;
  padding-left: 0.7vw;
  padding-right: 0.7vw;
  border-radius: 0.3vw;
  width: 100%;
}

.rightPreviewIframe {
  width: 100%;
  border: none;
  height: 400px;
}

.other_block_preview {
  padding-top: 50px;
  padding-bottom: 80px;
}

.btn-info {
  color: #fff;
  background-color: #17a2b8 !important;
  border-color: #00869b !important;
  border-radius: 0.5vw;
}

.width-auto {
  width: auto !important;
}

.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: spinner-border 0.75s linear infinite;
  animation: spinner-border 0.75s linear infinite;
}

/* Prompt Improvement Modal Styles */
#promptNewModal .form-group {
  margin-bottom: 1rem;
}

#promptNewModal .form-group label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
  font-size: 0.9vw;
}

.prompt-improvement-field {
  border: 1px solid #ced4da;
  border-radius: 0.25vw;
  padding: 0.375vw 0.75vw;
  font-size: 0.9vw;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.prompt-improvement-field:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.prompt-suggestion-card {
  border: 1px solid #dee2e6;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.prompt-suggestion-card:hover {
  border-color: #007bff;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transform: translateY(-1px);
}

.prompt-suggestion-card .card-title {
  color: #007bff;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.prompt-suggestion-card .card-text {
  color: #495057;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.select-suggestion-btn {
  transition: all 0.15s ease-in-out;
  margin: 1vw;
}

.card-body.d-flex.flex-column {
  padding: 0;
}

.select-suggestion-btn:hover {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

#promptValidationMessage {
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

#contextCharCount {
  font-weight: 600;
}

/* Improve Prompt Button Styling */
#improvePromptBtn {
  border: 1px solid #007bff;
  color: #007bff;
  background-color: transparent;
  transition: all 0.15s ease-in-out;
  position: relative;
  overflow: hidden;
}

#improvePromptBtn:hover {
  background-color: #007bff;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 0.125rem 0.25rem rgba(0, 123, 255, 0.25);
}

#improvePromptBtn:active {
  transform: translateY(0);
}

#generatePromptSuggestions.AI_create_and_edit-1a2b:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #6c757d;
}

#generatePromptSuggestions.AI_create_and_edit-1a2b:disabled::before,
#generatePromptSuggestions.AI_create_and_edit-1a2b:disabled::after {
  display: none;
}

/* Loading state styling */
#promptGenerationLoading {
  padding: 2rem 0;
}

#promptGenerationLoading .spinner-border {
  width: 2rem;
  height: 2rem;
}

/* Results section styling */
#promptSuggestionsResults {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #dee2e6;
}

#promptSuggestionsResults h6 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 1rem;
}

/* Modal consistency with existing patterns */
#promptNewModal .modal-title {
  font-size: 1.1vw;
  font-weight: 600;
}

#promptNewModal .modal-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1vw 1.5vw;
}

#promptNewModal .modal-body {
  padding: 1.5vw;
}

#promptNewModal .modal-footer {
  border-top: 1px solid #dee2e6;
  padding: 1vw 1.5vw;
}

#promptNewModal .modal-footer .btn {
  font-size: 0.9vw;
  padding: 0.375vw 0.75vw;
  border-radius: 0.25vw;
  border-width: 0.07vw;
}

/* Preview Mode Hover Indicator */
.preview-mode-hover-indicator {
  position: absolute;
  background-color: rgba(13, 110, 253, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  z-index: 1000;
  pointer-events: none;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.preview-mode-hover-indicator.show {
  opacity: 1;
}

/* Simple cursor change for preview mode elements */
.form-section.preview-mode *:hover {
  cursor: pointer;
}

/* Temporary highlight for iframe click-to-edit (extends existing highlighted class) */
.temporary-iframe-highlight.highlighted {
  outline: 3px solid #007bff !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 12px 2px rgba(0, 123, 255, 0.4) !important;
  transition: all 0.3s ease !important;
}

/* Undo/Redo Button Arrow Animations */
@keyframes pulse-left-arrow {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.7;
  }
}

@keyframes pulse-right-arrow {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.7;
  }
}

/* Undo buttons with left arrow */
#undoLastSaveButton::before,
#undoAllButton::before {
  content: "◀";
  display: inline-block;
  margin-right: 0.4em;
  animation: pulse-left-arrow 2.5s ease-in-out infinite;
  font-weight: 900;
  font-size: 0.9em;
  letter-spacing: -0.1em;
}

/* Redo button with right arrow */
#redoAllButton::before {
  content: "▶";
  display: inline-block;
  margin-right: 0.4em;
  animation: pulse-right-arrow 2.5s ease-in-out infinite;
  font-weight: 900;
  font-size: 0.9em;
  letter-spacing: -0.1em;
}

/* Columns and Rows field visibility control for content-source selection */
.columns-rows-fields {
  display: block;
}

.columns-rows-fields.hidden-for-selection {
  display: none !important;
}

</style>
