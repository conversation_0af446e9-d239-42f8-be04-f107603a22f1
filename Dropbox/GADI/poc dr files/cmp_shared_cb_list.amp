%%[
    SET @action_area = 'add'
    SET @de_appC_cc_Name = 'copy_or_duplicate_from_other_email'
    SET @OppositEnv = IIF(@AppStateEnvironment == 'UAT','PROD','UAT')
    SET @sourceTable = IIF(@AppStateEnvironment == 'UAT','ent.EmailObject_UAT','ent.EmailObject')
    SET @sourceTable_reverse = IIF(@AppStateEnvironment == 'UAT','ent.EmailObject','ent.EmailObject_UAT')
    SET @de_abi = IIF(@AppStateEnvironment == 'UAT','ent.All_Brand_Info_UAT','ent.All_Brand_Info')
    SET @de_abi_reverse = IIF(@AppStateEnvironment == 'UAT','ent.All_Brand_Info','ent.All_Brand_Info_UAT')
    SET @de_eca = IIF(@AppStateEnvironment == 'UAT','ent.email_contentBlock_areas_UAT','ent.email_contentBlock_areas')
    SET @de_emailObject = IIF(@AppStateEnvironment == 'UAT','ent.EmailObject_UAT','ent.EmailObject')
    SET @de_emailObject_reverse = IIF(@AppStateEnvironment == 'UAT','ent.EmailObject','ent.EmailObject_UAT')
    SET @de_eca_reverse = IIF(@AppStateEnvironment == 'UAT','ent.email_contentBlock_areas','ent.email_contentBlock_areas_UAT')
    SET @de_ani = IIF(@AppStateEnvironment == 'UAT','ent.All_Newsletter_Info_UAT','ent.All_Newsletter_Info')
    SET @de_ani_reverse = IIF(@AppStateEnvironment == 'UAT','ent.All_Newsletter_Info','ent.All_Newsletter_Info_UAT')
    SET @de_appC_cc = IIF(@environmentAppCentre == 'UAT','ent.AppC_components_code',IIF(@environmentAppCentre == 'SIT','AppC_components_SIT_code','ent.AppC_components_code'))
    SET @appC_brand_access_de = IIF(@AppStateEnvironment == 'UAT','ent.UAT_appC_brand_access','ent.appC_brand_access')
    SET @appC_brand_access_de_reverse = IIF(@OppositEnv == 'UAT','ent.UAT_appC_brand_access','ent.appC_brand_access')
    SET @sourceIdField = 'EmailName'
    SET @src_Secret = Lookup(@sourceTable,'Secret',@sourceIdField,@sourceIdValue)
    SET @CVBRowCount = RowCount(LookupRows(@de_eca,'EmailName',@sourceIdValue))
    SET @de_appC_cc_EventID = 'copy_or_duplicate_block'
    SET @de_eca_EventID = @de_appC_cc_EventID
    SET @friendly_id = Replace(@de_eca_EventID,'-','_')
    SET @emailAddress = Lookup('ent.Users','EmailAddress','Username',@username)
]%%
<div class="card-body" style="padding: .5rem 1.2rem 1rem 1.2rem;">
  <div class="accordion" id="accordion_group%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%">
    <div class="explainerfourAndHalf">Select a shared content block from the list</div>
    <input type="text" placeholder="Use this searchbar to filter through the list.." id="CBInputSearch" onkeyup="filterFunctionCBs()" class="userTabButton" autocomplete="off" style="background-color: #f5f5f5;text-align: left;padding-left: 20px;height:34px;margin-bottom:10px!important;border-radius: 4px!important;border: 1px solid rgba(0, 0, 0, .125);width: 100%;">
    <div class="related_fields" style="padding:0!important;margin-bottom:0;border: 1px solid #c4c9f3;border-radius: 4px;overflow: hidden;border: 1px solid #c8c6c6;">
      <div class="assetCard">
        <div class="card-header">
          <h5 class="mb-0">
            <div class="assetRowContainer" style="padding:0px;BACKGROUND-COLOR: #eeeeee;">
              <table>
                <tbody>
                  <tr>
                    <td style="width:130px!important;font-size:13px;font-weight:550;">Email name & description</td>
                    <td style="width:200px!important;font-size:13px;font-weight:550;">Select a block</td>
                    <td style="width:45px!important;font-size:13px;font-weight:550;">Owner</td>
                    <td style="width:5px!important;padding-left:0;"></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </h5>
        </div>
      </div>
      <div class="list_view_table_row_container" style="max-height:200px!important;margin-bottom:0!important;" id="FilterCB_dropdown_menu">
          %%[
              for @cb = 1 to RowCount(LookupOrderedRows(@de_eca,0,'EmailName ASC','shared_with_all','1')) do
                SET @cb_rows = LookupOrderedRows(@de_eca,0,'EmailName ASC','shared_with_all','1')
                SET @current_cb_id = Field(Row(@cb_rows,@cb),'EventID')
                SET @current_email = Field(Row(@cb_rows,@cb),'EmailName')
                SET @current_desc = Lookup(@de_emailObject,'Description','EmailName',@current_email)
                IF NOT EMPTY(@current_desc) THEN SET @current_desc = Concat(' (',@current_desc,')') ENDIF
                SET @current_secret = Lookup(@de_emailObject,'Secret','EmailName',@current_email)
                SET @current_e_Dynamic = Lookup(@de_emailObject,'Dynamic','EmailName',@current_email)
                SET @current_cb_pos = Field(Row(@cb_rows,@cb),'PositionInEmail')
                SET @current_cb_name = Field(Row(@cb_rows,@cb),'ContentBlock')
                SET @current_cb_visible = Field(Row(@cb_rows,@cb),'Visible')
                SET @current_cb_UpdatedBy = Field(Row(@cb_rows,@cb),'UpdatedBy')
                SET @current_cb_ref_cb_id = Field(Row(@cb_rows,@cb),'ref_cb_id')
                SET @current_cb_fname = Field(Row(@cb_rows,@cb),'friendly_name')
                SET @de_eca_ref_cb_EmailAddress = Lookup('ent.Users','EmailAddress','Username',@current_cb_UpdatedBy)
                SET @firstName_ref_cb_owner = Lookup('ent.Users','FirstName','UserName',@current_cb_UpdatedBy)
                SET @emailAddress_ref_cb_owner = Lookup('ent.Users','EmailAddress','UserName',@current_cb_UpdatedBy)
                SET @de_eca_ref_cb_EmailName = Lookup(@de_eca,'EmailName','EventID',@current_cb_id)
                SET @de_eca_ref_cb_ContentBlock = Lookup(@de_eca,'ContentBlock','EventID',@current_cb_id)
                SET @de_eca_ref_cb_friendly_name = Lookup(@de_eca,'friendly_name','EventID',@current_cb_id)
                SET @de_appC_cc_FriendlyName = Lookup(@de_appC_cc,'friendlyName','Name',@current_cb_name)
                SET @current_cb_fname = Field(Row(@cb_rows,@cb),'friendly_name')
                IF EMPTY(@de_appC_cc_FriendlyName) THEN SET @de_appC_cc_FriendlyName = @current_cb_name ENDIF
                IF NOT EMPTY(@de_eca_ref_cb_friendly_name) THEN SET @de_eca_ref_cb_friendly_name = Concat(' | ',@de_eca_ref_cb_friendly_name) ENDIF
                SET @ref_cb_Brand = Lookup(@de_emailObject,'Brand','EmailName',@de_eca_ref_cb_EmailName)
                SET @ref_cb_email_desc = Lookup(@de_emailObject,'Description','EmailName',@de_eca_ref_cb_EmailName)
                IF NOT EMPTY(@ref_cb_email_desc) THEN SET @ref_cb_email_desc = Concat(' - (',@ref_cb_email_desc,')') ENDIF
                IF EMPTY(@de_appC_cc_FriendlyName) THEN SET @de_appC_cc_FriendlyName = @current_cb_name ENDIF
                IF @current_e_Dynamic == true AND EMPTY(@current_cb_ref_cb_id) THEN ]%%
                <div class="retrieve">
                  <div class="assetCard">
                    <h5 class="mb-0">
                      <div class="assetRowContainer" style="padding:0px;">
                        <table>
                          <tbody>
                            <tr id="table_tr_row1%%=v(@de_eca_EventID)=%%%%=v(@current_cb_id)=%%" style="cursor:pointer;" onclick="
                              // Reset all row backgrounds
                              $('tr').css('background-color', '');
                              // Set this row's background directly
                              $(this).css('background-color', '#daedf9');
                              // Trigger click on the span
                              $(this).find('span.toggleSortByFields').click();">
                                <td style="width:130px!important;overflow:auto;font-size:11px!important;border-right: 1px solid #dfdfdf;cursor:pointer;" class="spanRowData" onclick="
                                  // Prevent event from bubbling up to the row
                                  event.stopPropagation();
                                  // Reset all row backgrounds
                                  $('tr').css('background-color', '');
                                  // Set this row's background directly
                                  $(this).closest('tr').css('background-color', '#daedf9');
                                  // Trigger click on the span in the next cell
                                  $(this).closest('tr').find('span.toggleSortByFields').click();">%%=v(@current_email)=%% - %%=v(@current_desc)=%%</td>
                                <td style="width:200px!important;overflow:auto;font-size:11px!important;border-right: 1px solid #dfdfdf;" class="spanRowData">
                                  <span style="padding-left:0px;height:0;font-size:11px!important;border-right: 1px solid #dfdfdf;" type="button" class="toggleSortByFields" id='{"brand":"%%=v(@e_brand)=%%","email":"%%=v(@current_email)=%%","position":"%%=v(@current_cb_pos)=%%","last_updated_by":"%%=v(@current_cb_UpdatedBy)=%%","secret":"%%=v(@current_secret)=%%","cb_id":"%%=v(@current_cb_id)=%%"}'  onclick="
                                    // Prevent event from bubbling up to the row
                                    event.stopPropagation();
                                    $('#cbPreviewIframe').attr('src','');
                                    enable_button('save%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%');
                                    $('#loading_cb_preview_left_view').show();
                                    // Reset all row backgrounds
                                    $('tr').css('background-color', '');
                                    // Set this row's background directly
                                    $(this).closest('tr').css('background-color', '#daedf9');
                                    // Call the extract function
                                    extract_val_from_select_option_id(event);">
                                    %%=v(@current_cb_pos)=%%. %%=v(@de_appC_cc_FriendlyName)=%%%%=IIF(NOT EMPTY(@current_cb_fname),Concat(' | ',@current_cb_fname),'')=%%
                                  </span>
                                </td>
                                <td style="width:45px!important;overflow:auto;font-size:11px!important;" class="spanRowData">
                                  <a target="_blank" href="mailto:%%=v(@emailAddress_ref_cb_owner)=%%?subject=Duplicating/referencing the block '%%=v(@de_eca_ref_cb_ContentBlock)=%%%%=v(@de_eca_ref_cb_friendly_name)=%%' at position %%=Lookup(@de_eca,'PositionInEmail','EventID',@current_cb_ref_cb_id)=%% in email %%=v(@de_eca_ref_cb_EmailName)=%%&cc=<EMAIL>&bcc=<EMAIL>&body=Hi %%=v(@firstName_ref_cb_owner)=%%,%0D%0DI am planning to duplicate/reference the '%%=v(@de_eca_ref_cb_ContentBlock)=%%%%=v(@de_eca_ref_cb_friendly_name)=%%' block at position %%=Lookup(@de_eca,'PositionInEmail','EventID',@current_cb_ref_cb_id)=%% in my email (%%=v(@current_email)=%%%%=v(@current_desc)=%%).%0D%0DPlease feel free to reach out if you would like to collaborate on updating its content.%0D%0DKind regards,%0D%%=v(@FirstName)=%% %%=v(@LastName)=%%%0D%%=v(@UserName)=%% ">%%=v(@de_eca_ref_cb_EmailAddress)=%%</a>
                                </td>
                              </tr>
                          </tbody>
                        </table>
                      </div>
                    </h5>
                  </div>
                </div>
                %%[ ENDIF
              next @cb ]%%
          %%[ for @bmail = 1 to RowCount(LookupRows(@appC_brand_access_de,'EmailAddress',@emailAddress,'Access',1)) do
            SET @e_brand = Field(Row(LookupOrderedRows(@appC_brand_access_de,0,'Brand ASC','EmailAddress',@emailAddress,'Access',1),@bmail),'Brand')
            for @i = 1 to RowCount(LookupORderedRows(@sourceTable,0,'EmailName ASC','Brand',@e_brand)) do
              SET @e_rows = LookupORderedRows(@sourceTable,0,'EmailName ASC','Brand',@e_brand)
              SET @current_email = Field(Row(@e_rows,@i),@sourceIdField)
              SET @current_desc = Field(Row(@e_rows,@i),'Description')
              SET @current_secret = Field(Row(@e_rows,@i),'Secret')
              IF NOT EMPTY(@current_desc) THEN SET @current_desc = Concat(' (',@current_desc,'..)') ENDIF
              for @cb = 1 to RowCount(LookupOrderedRows(@de_eca,0,'PositionInEmail ASC','EmailName',@current_email,'shared','1')) do
                SET @cb_rows = LookupOrderedRows(@de_eca,0,'PositionInEmail ASC','EmailName',@current_email,'shared','1')
                SET @current_cb_id = Field(Row(@cb_rows,@cb),'EventID')
                SET @current_cb_pos = Field(Row(@cb_rows,@cb),'PositionInEmail')
                SET @current_cb_name = Field(Row(@cb_rows,@cb),'ContentBlock')
                SET @current_cb_visible = Field(Row(@cb_rows,@cb),'Visible')
                SET @current_cb_UpdatedBy = Field(Row(@cb_rows,@cb),'UpdatedBy')
                SET @current_cb_ref_cb_id = Field(Row(@cb_rows,@cb),'ref_cb_id')
                SET @de_eca_ref_cb_EmailAddress = Lookup('ent.Users','EmailAddress','Username',@current_cb_UpdatedBy)
                SET @firstName_ref_cb_owner = Lookup('ent.Users','FirstName','UserName',@current_cb_UpdatedBy)
                SET @emailAddress_ref_cb_owner = Lookup('ent.Users','EmailAddress','UserName',@current_cb_UpdatedBy)
                SET @de_eca_ref_cb_EmailName = Lookup(@de_eca,'EmailName','EventID',@current_cb_id)
                SET @de_eca_ref_cb_ContentBlock = Lookup(@de_eca,'ContentBlock','EventID',@current_cb_id)
                SET @de_eca_ref_cb_friendly_name = Lookup(@de_eca,'friendly_name','EventID',@current_cb_id)
                SET @de_appC_cc_FriendlyName = Lookup(@de_appC_cc,'friendlyName','Name',@current_cb_name)
                SET @current_cb_fname = Field(Row(@cb_rows,@cb),'friendly_name')
                IF EMPTY(@de_appC_cc_FriendlyName) THEN SET @de_appC_cc_FriendlyName = @current_cb_name ENDIF
                IF NOT EMPTY(@de_eca_ref_cb_friendly_name) THEN SET @de_eca_ref_cb_friendly_name = Concat(' | ',@de_eca_ref_cb_friendly_name) ENDIF
                SET @ref_cb_Brand = Lookup(@de_emailObject,'Brand','EmailName',@de_eca_ref_cb_EmailName)
                SET @ref_cb_email_desc = Lookup(@de_emailObject,'Description','EmailName',@de_eca_ref_cb_EmailName)
                IF NOT EMPTY(@ref_cb_email_desc) THEN SET @ref_cb_email_desc = Concat(' - (',@ref_cb_email_desc,')') ENDIF
                IF EMPTY(@de_appC_cc_FriendlyName) THEN SET @de_appC_cc_FriendlyName = @current_cb_name ENDIF
                IF Field(Row(@e_rows,@i),'dynamic') == true AND EMPTY(@current_cb_ref_cb_id) THEN ]%%
                  <div class="retrieve">
                    <div class="assetCard">
                      <h5 class="mb-0">
                        <div class="assetRowContainer" style="padding:0px;">
                          <table>
                            <tbody>
                              <tr id="table_tr_row2%%=v(@de_eca_EventID)=%%%%=v(@current_cb_id)=%%" style="cursor:pointer;" onclick="
                                // Reset all row backgrounds
                                $('tr').css('background-color', '');
                                // Set this row's background directly
                                $(this).css('background-color', '#daedf9');
                                // Trigger click on the span
                                $(this).find('span.toggleSortByFields').click();">
                                <td style="width:130px!important;overflow:auto;font-size:11px!important;border-right: 1px solid #dfdfdf;cursor:pointer;" class="spanRowData" onclick="
                                  // Prevent event from bubbling up to the row
                                  event.stopPropagation();
                                  // Reset all row backgrounds
                                  $('tr').css('background-color', '');
                                  // Set this row's background directly
                                  $(this).closest('tr').css('background-color', '#daedf9');
                                  // Trigger click on the span in the next cell
                                  $(this).closest('tr').find('span.toggleSortByFields').click();">%%=v(@current_email)=%% - %%=v(@current_desc)=%%</td>
                                <td style="width:200px!important;overflow:auto;font-size:11px!important;border-right: 1px solid #dfdfdf;" class="spanRowData">
                                  <span style="padding-left:0px;height:0;font-size:11px!important;border-right: 1px solid #dfdfdf;" type="button" class="toggleSortByFields" id='{"brand":"%%=v(@e_brand)=%%","email":"%%=v(@current_email)=%%","position":"%%=v(@current_cb_pos)=%%","last_updated_by":"%%=v(@current_cb_UpdatedBy)=%%","secret":"%%=v(@current_secret)=%%","cb_id":"%%=v(@current_cb_id)=%%"}' onclick="
                                    // Prevent event from bubbling up to the row
                                    event.stopPropagation();
                                    $('#cbPreviewIframe').attr('src','');
                                    enable_button('save%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%');
                                    $('#loading_cb_preview_left_view').show();
                                    // Reset all row backgrounds
                                    $('tr').css('background-color', '');
                                    // Set this row's background directly
                                    $(this).closest('tr').css('background-color', '#daedf9');
                                    // Call the extract function
                                    extract_val_from_select_option_id(event);">
                                    %%=v(@current_cb_pos)=%%. %%=v(@de_appC_cc_FriendlyName)=%%%%=IIF(NOT EMPTY(@current_cb_fname),Concat(' | ',@current_cb_fname),'')=%%
                                  </span>
                                </td>
                                <td style="width:45px!important;overflow:auto;font-size:11px!important;" class="spanRowData">
                                  <a target="_blank" href="mailto:%%=v(@emailAddress_ref_cb_owner)=%%?subject=Duplicating/referencing the block '%%=v(@de_eca_ref_cb_ContentBlock)=%%%%=v(@de_eca_ref_cb_friendly_name)=%%' at position %%=Lookup(@de_eca,'PositionInEmail','EventID',@current_cb_ref_cb_id)=%% in email %%=v(@de_eca_ref_cb_EmailName)=%%&cc=<EMAIL>&bcc=<EMAIL>&body=Hi %%=v(@firstName_ref_cb_owner)=%%,%0D%0DI am planning to duplicate/reference the '%%=v(@de_eca_ref_cb_ContentBlock)=%%%%=v(@de_eca_ref_cb_friendly_name)=%%' block at position %%=Lookup(@de_eca,'PositionInEmail','EventID',@current_cb_ref_cb_id)=%% in my email (%%=v(@current_email)=%%%%=v(@current_desc)=%%).%0D%0DPlease feel free to reach out if you would like to collaborate on updating its content.%0D%0DKind regards,%0D%%=v(@FirstName)=%% %%=v(@LastName)=%%%0D%%=v(@UserName)=%% ">%%=v(@de_eca_ref_cb_EmailAddress)=%%</a>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </h5>
                    </div>
                  </div>
                %%[ ENDIF
              next @cb
            next @i
          next @bmail ]%%
        </div>
    </div>
    <div class="subText2">
      Select a block to preview. To one-click-email the owner, click on the <strong>Owner</strong> column link.
    </div>
    <div class="view-toggle-buttons" style="display: flex; gap: 0.5vw; margin-bottom: 0.8vw; margin-top: 0.8vw;">
      <button type="button" id="desktopView%%=v(@de_eca_EventID)=%%" class="view-toggle-btn active" style="border: 1px solid #dee2e6; background-color: #0d6efd; color: white; border-color: #0d6efd; border-radius: var(--border-radius, 0.42vw); padding: 0.375vw 0.75vw; font-size: var(--font-size-base, 0.97vw);" onclick="$('#ToggleBackUpEmailView%%=v(@de_eca_EventID)=%%').val('Desktop'); toggleSharedCBViewMode('Desktop', '%%=v(@de_eca_EventID)=%%');">💻 Desktop</button>
      <button type="button" id="mobileView%%=v(@de_eca_EventID)=%%" class="view-toggle-btn" style="border: 1px solid #dee2e6; background-color: #fff; color: black; border-radius: var(--border-radius, 0.42vw); padding: 0.375vw 0.75vw; font-size: var(--font-size-base, 0.97vw);" onclick="$('#ToggleBackUpEmailView%%=v(@de_eca_EventID)=%%').val('Mobile'); toggleSharedCBViewMode('Mobile', '%%=v(@de_eca_EventID)=%%');">📱 Mobile</button>
    </div>
    <select class="question2" id="ToggleBackUpEmailView%%=v(@de_eca_EventID)=%%" style="display:none;">
      <option value="Desktop" selected>Desktop view</option>
      <option value="Mobile">Mobile view</option>
    </select>
    <div style="position:relative!important;width:100%;border: 1px solid #c6c6c6;border-radius: 5px;background-color:#f7f7f7;margin-top:1vw;height: 300px;">
      <br>
      <div id="preview_text_placeholder%%=v(@de_eca_EventID)=%%" style="padding-left:15px;font-size:14px;margin-top:-5px;text-align:center;">Select a content block from the list to generate a preview.</div>
      <br>
      <br>
      <br>
      <div id="loading_cb_preview_left_view" style="display:none;position:absolute!important;width:100%;">
        <div style="left:50%;transform: translate(-50%, 0px);position:absolute;"> Loading preview...</div>
        <div style="left:50%;transform: translate(-50%, 0px);position:absolute;">
          <br><br>
          <div class="modall-dialog modal-sm">
            <div class="modall-content">
              <span class="fa fa-circle-o-notch fa-spin fa-fw fa-2x" aria-hidden="true"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <iframe id="cbPreviewIframe" class="rightPreviewIframe other_block_preview shared-preview-desktop-view" style="display:none;height:300px!important;position:relative!important;padding:0!important;margin-top:-300px!important;border: 1px solid #c6c6c6;border-radius: 5px;margin-bottom:15px;"></iframe>
</div>