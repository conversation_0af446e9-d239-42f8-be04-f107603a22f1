<script runat="server">
// Set content type header to ensure proper UTF-8 handling
Platform.Response.SetResponseHeader("Content-Type", "application/json;charset=UTF-8");
Platform.Variable.SetValue("@postData",postData);
</script>
%%[
SET @cookie = QueryParameter('c')
SET @environmentAppCentre = QueryParameter('sys_env')
SET @app_LastLoginDate = Lookup('ent.Users','LastLoginDate','SessionID',@cookie)
SET @app_LastLoginDate = IIF(EMPTY(@app_LastLoginDate),'1900-01-01',@app_LastLoginDate)
SET @appEventID = GUID()
SET @localTime = SystemdateToLocalDate(NOW())
SET @stateDE = 'ent.AppC_Notifications'
SET @formRowCount = RowCount(BuildRowSetFromString(@formName,'//'))
IF @formRowCount > 0 AND NOT EMPTY(@formName) THEN
  SET @fn_rows = BuildRowSetFromString(@formName,'//')
  for @f_pairs = 1 to @formRowCount do
    SET @fn_row = Field(Row(@fn_rows,@f_pairs),1)
    IF RowCount(BuildRowSetFromString(@fn_row,':')) > 1 THEN
      SET @f_field = Field(Row(BuildRowSetFromString(@fn_row,':'),1),1)
      SET @f_value =  Field(Row(BuildRowSetFromString(@fn_row,':'),2),1)
      TreatAsContent(CONCAT('%','%[ SET @fn_',@f_field,' = "',@f_value,'" ]%','%'))
    ENDIF
  next @f_pairs
ENDIF
SET @sourceIdValue = @fn_Value
IF RowCount(LookupRows('ent.Users','SessionID',@cookie)) > 0 AND FormatDate(@app_LastLoginDate,"DD-MM-YYYY") == FormatDate(@localTime, "DD-MM-YYYY") THEN
  SET @app = 'Email Editor'
  SET @ParentApp = Lookup('ent.appC_parent_apps','Name','Id',Lookup('ent.appC_applications','parent_Id','Name',@app))
  SET @AppObject = Lookup('ent.appC_applications','Name','Name',@app)
  SET @username = Lookup('ent.Users','Username','SessionID',@cookie)
  SET @emailAddress = Lookup('ent.Users','EmailAddress','Username',@username)
  SET @secret = Lookup('ent.Users','Secret','Username',@username)
  SET @role = Lookup('ent.Users','UserRole','SessionID',@cookie)
  SET @userBrand = Lookup('ent.Users','Brand','SessionID',@cookie)
  SET @userCurrentBrand = IIF(EMPTY(Lookup('ent.Users','CurrentBrand','SessionID',@cookie)),@userBrand,Lookup('ent.Users','CurrentBrand','SessionID',@cookie))
  SET @preview_mode = Lookup('ent.Users','preview_mode','SessionID',@cookie)
  SET @preview_section = Lookup('ent.Users','preview_section','SessionID',@cookie)
  SET @sourceIdField = Lookup('ent.appC_applications','sourceIdField','Name',@app)
  SET @AppStateEnvironment = @biz_env
  SET @sourceTable = Lookup('ent.appC_applications',IIF(@AppStateEnvironment == 'UAT','sourceTable_UAT','sourceTable'),'Name',@app)
  SET @AppCPermissionTable = IIF(@AppStateEnvironment == 'UAT','ent.AppC_AppPermissions_UAT','ent.AppC_AppPermissions')
  SET @de_abi = IIF(@AppStateEnvironment == 'UAT','ent.All_Brand_Info_UAT','ent.All_Brand_Info')
  SET @de_eca = IIF(@AppStateEnvironment == 'UAT','ent.email_contentBlock_areas_UAT','ent.email_contentBlock_areas')
  SET @de_emailObject = IIF(@AppStateEnvironment == 'UAT','ent.EmailObject_UAT','ent.EmailObject')
  SET @de_ani = IIF(@AppStateEnvironment == 'UAT','ent.All_Newsletter_Info_UAT','ent.All_Newsletter_Info')
  SET @sysFieldsDe = IIF(@AppStateEnvironment == 'UAT','ent.system_field__obj_UAT','ent.system_field__obj')
  SET @customDropdown_obj = IIF(@AppStateEnvironment == 'UAT','ent.customDropdown__obj_UAT','ent.customDropdown__obj')
  SET @de_ecc = IIF(@AppStateEnvironment == 'UAT','ent.email_content_cache_UAT','ent.email_content_cache')
  SET @de_ecc_saves = IIF(@AppStateEnvironment == 'UAT','ent.email_content_cache_saves_UAT','ent.email_content_cache_saves')
  SET @de_cb_types_list = IIF(@AppStateEnvironment == 'UAT','ent.cb_types_list_uat','ent.cb_types_list_prod')
  SET @de_eca_backup = Concat(@de_eca,'_backup')
  SET @de_ecc_backup = Concat(@de_ecc,'_backup')
  SET @de_cec_backup = Concat(@de_cec,'_backup')
  SET @make_backup = RequestParameter('make_backup')
  SET @create_or_update_backup = RequestParameter('create_or_update_backup')
  SET @backup_id = RequestParameter('backup_id')
  SET @de_cec_res = IIF(@make_backup == 1,@de_cec_backup,@de_cec)
  SET @new_backup_id = GUID()
  IF NOT EMPTY(Lookup(@StateDE,'EventID','Username',@Username,'SessionID',@cookie,'Object',@AppObject)) THEN
    SET @AppStateID = Field(Row(LookupOrderedRows(@StateDE,0,'DateAdded DESC','Username',@Username,'SessionID',@cookie,'Object',@AppObject),1),'EventID')
    SET @ShowInactive = Lookup(@StateDE,'ShowInactive','EventID',@AppStateID)
    SET @CurrentAppSection = IIF(EMPTY(Lookup(@StateDE,'Section','EventID',@AppStateID)),1,Lookup(@StateDE,'Section','EventID',@AppStateID))
    SET @CurrentAppSectionName = Lookup(@StateDE,'SectionName','EventID',@AppStateID)
    SET @sourceSubObjTableValue = Lookup(@StateDE,'SubObject','EventID',@AppStateID)
    SET @sourceObject = Lookup(@StateDE,'Object','EventID',@AppStateID)
    SET @sourceIdSubjValue = Lookup(@StateDE,'SubObjValue','EventID',@AppStateID)
    SET @fn_Value = Lookup(@StateDE,'Value','EventID',@AppStateID)
    SET @CurrentFields_state = Lookup(@StateDE,'fields_state','EventID',@AppStateID)
  ELSE
    SET @AppStateID = TreatAsContent(GUID())
    SET @ShowInactive = 0
  ENDIF
  SET @AppCPermissionTable = IIF(@AppStateEnvironment == 'UAT','ent.AppC_AppPermissions_UAT','ent.AppC_AppPermissions')
  IF Lookup(@appCPermissionTable,'Access','EmailAddress',@emailAddress,'ParentApp',@parentApp,'Type',@fn_Action) != true THEN
    SET @formMessage = Concat("You don't have permission to ",LowerCase(@fn_Action)," in this app or environment. To get your permissions updated, please raise a <a href='https://sofi.newscorp.com/esc?id=sc_cat_item&table=sc_cat_item&sys_id=ae90bf888724a510d82afcc6cebb35d9' target='_blank'>SOFI access request ticket</a>. |@postData:  ",@postData)
    SET @do_not_action = 1
    SET @formStatus = 'warning'
  ENDIF
  IF @do_not_action != 1 THEN
    SET @action_area = RequestParameter('action_area')
    SET @cb_EventID = GUID()
    SET @cb_pos = RequestParameter('cbPos')
    IF EMPTY(@cb_pos) THEN
        RaiseError('missing cbPos value')
    ENDIF
    SET @src_secret = RequestParameter('secret')
    IF @action_area == 'reference_from_other_email' THEN
      SET @de_appC_cc_Name = Lookup(@de_eca,'ContentBlock','EventID',@to_use_postData_cb_id)
      SET @de_eca_friendly_name = Lookup(@de_eca,'friendly_name','EventID',@to_use_postData_cb_id)
      SET @de_appC_cc_FriendlyName = Lookup(@de_appC_cc,'friendlyName','Name',@de_appC_cc_Name)
      SET @edit_eca_PositionInEmail = @cb_pos
      SET @new_eca_EventID = GUID()
      SET @fn_Object = 'Email Editor'
      SET @fn_SubObject = 'Content block settings'
      SET @fn_Action = 'Edit'
      SET @fn_Displayed = 0
      SET @fn_SubObjValue = @new_eca_EventID
      for @var = 1 to RowCount(LookupRows(@sysFieldsDe,'Template','none','component','email_content_areas','Active','1','default_group','0')) do
        SET @sys_eca_rows = LookupRows(@sysFieldsDe,'Template','none','component','email_content_areas','Active','1','default_group','0')
        SET @deField = Field(Row(@sys_eca_rows,@var),'field_name')
        SET @deData_type = Field(Row(@sys_eca_rows,@var),'data_type')
        IF @deField != 'EventID' AND @deField != 'PositionInEmail' THEN
          SET @deValue = Lookup(@de_eca,@deField,'EventID',@to_use_postData_cb_id)
          TreatAsContent(CONCAT('%','%[ SET @edit_eca_',@deField,' = "',@deValue,'" ]%','%'))
        ENDIF
      next @var
      SET @edit_eca_use_ref_cb_hide = RequestParameter('use_ref_cb_hide')
      SET @f_name = Concat(@de_appC_cc_FriendlyName,IIF(NOT EMPTY(@de_eca_friendly_name),Concat(' | ',@de_eca_friendly_name),''))
      SET @FormMessage = Concat('Successfully <strong>referenced</strong> content block <strong>',@f_name,'</strong> from the email <strong>',Lookup(@de_eca,'EmailName','EventID',@to_use_postData_cb_id),'</strong> into the email <strong>',@sourceIdValue,'</strong>.')
      SET @CVBRowCount = RowCount(LookupRows(@de_eca,'EmailName',@sourceIdValue))
      IF @edit_eca_PositionInEmail < Add(@CVBRowCount,1) THEN
        SET @posCounter = Add(@edit_eca_PositionInEmail,1)
        for @posChange = @edit_eca_PositionInEmail to @CVBRowCount do
          SET @eventID_posChange = Field(Row(LookupORderedRows(@de_eca,0,'PositionInEmail ASC','EmailName',@sourceIdValue),@posChange),'EventID')
          IF @posChange <= Add(@CVBRowCount,1) THEN
            UpdateData(@de_eca,2,'EventID',@eventID_posChange,'EmailName',@sourceIdValue,'PositionInEmail',@posCounter,'UpdatedDate',@LocalTime,'UpdatedBy',@Username)
          ENDIF
          SET @posCounter = Add(@posCounter,1)
        next @posChange
      ENDIF
      InsertData(@de_eca,'PositionInEmail',@edit_eca_PositionInEmail,'ContentBlock',@edit_eca_ContentBlock,'EmailName',@sourceIdValue,'EventID',@new_eca_EventID,'default_template_settings',@edit_eca_default_template_settings,'UpdatedDate',@LocalTime,'UpdatedBy',@Username,'CreatedBy',@userName,'CreatedDate',@localTime,'friendly_name',@edit_eca_friendly_name,'marketing',@edit_eca_marketing,'hide_from_prem_subs',@edit_eca_hide_from_prem_subs,'hide_before_date',@edit_eca_hide_before_date,'hide_after_date',@edit_eca_hide_after_date,'hide_set_of_days',@edit_eca_hide_set_of_days,'ref_cb_id',@to_use_postData_cb_id,'use_ref_cb_hide',@edit_eca_use_ref_cb_hide,'use_hide_after_date',@edit_eca_use_hide_after_date,'use_hide_before_date',@edit_eca_use_hide_before_date)
      UpdateData(@sourceTable,1,'EmailName',@sourceIdValue,'UpdatedDate',@LocalTime,'UpdatedBy',@Username,'disable_utm_appending',@edit_eca_disable_utm_appending)
      IF @preview_mode == 'focused' THEN
        UpdateData('ent.Users',1,'Username',@userName,'preview_section',@new_eca_EventID)
      ENDIF
    ELSEIF @action_area != 'reference_from_other_email' THEN
      ]%%%%{={{ }}=}%%{{.datasource data type=variable source=@postData }}{{.data}}{ "target" : "@postData" }{{/data}}%%[
      IF @action_area != 'duplicate' AND @action_area != 'duplicate_from_other_email' AND @action_area != 'decouple' AND @action_area != 'backup' THEN
        SET @action_area = TreatAscontent('%%{={{ }}=}%%{{data.action_area}}')
        SET @de_eca_PositionInEmail = TreatAscontent('%%{={{ }}=}%%{{data.de_eca_PositionInEmail}}')
        SET @edit_eca_EventID = TreatAscontent('%%{={{ }}=}%%{{data.de_eca_EventID}}')
        SET @edit_eca_friendly_name = TreatAscontent('%%{={{ }}=}%%{{data.friendly_name}}')
      ENDIF
      SET @edit_eca_ContentBlock = TreatAscontent('%%{={{ }}=}%%{{data.edit_eca_ContentBlock}}')
      SET @edit_eca_disable_utm_appending = TreatAscontent('%%{={{ }}=}%%{{data.disable_utm_appending}}')
      SET @de_appC_cc_Name = TreatAscontent(Concat('%%{={{ }}=}%%','{{data.de_appC_cc_Name}}'))
      SET @html_content = TreatAscontent(Concat('%%{={{ }}=}%%','{{data.freeText_content}}'))
      IF @action_area == 'duplicate' OR @action_area == 'duplicate_from_other_email' THEN
        SET @edit_eca_EventID = @to_use_postData_cb_id
        SET @fn_Object = 'Email Editor'
        SET @fn_SubObject = 'Content block settings'
        SET @fn_Action = 'Edit'
        SET @fn_Displayed = 0
        SET @fn_SubObjValue = @cb_EventID
        SET @formPost_data = Replace(@formPost_data,@edit_eca_EventID,@cb_EventID)
        IF @action_area != 'duplicate_from_other_email' THEN
          SET @edit_eca_PositionInEmail = Lookup(@de_eca,'PositionInEmail','EventID',@edit_eca_EventID)
          SET @fn_Value = Lookup(@de_eca,'EmailName','EventID',@edit_eca_EventID)
          SET @sourceIdValue = @fn_Value
        ELSE
          SET @fn_Value = @sourceIdValue
          SET @edit_eca_PositionInEmail = @cb_pos
        ENDIF
      ELSEIF @action_area == 'decouple' OR @action_area == 'backup' THEN
        SET @edit_eca_EventID = @to_use_postData_cb_id
        SET @fn_Object = 'Email Editor'
        SET @fn_SubObject = 'Content block settings'
        SET @fn_Action = 'Edit'
        IF @action_area != 'backup' THEN
          SET @fn_Displayed = 1
          SET @fn_SubObjValue = @form_cb_id
          SET @formPost_data = Replace(@formPost_data,@edit_eca_EventID,@form_cb_id)
          SET @edit_eca_PositionInEmail = Lookup(@de_eca,'PositionInEmail','EventID',@form_cb_id)
        ELSE
          SET @fn_Displayed = 0
          SET @fn_SubObjValue = @to_use_postData_cb_id
          SET @edit_eca_PositionInEmail = Lookup(@de_eca,'PositionInEmail','EventID',@to_use_postData_cb_id)
        ENDIF
      ENDIF
      IF @action_area == 'add' THEN
        SET @de_appC_cc_EventID = Lookup(@de_cb_types_list,'id','Name',@de_appC_cc_Name)
        SET @formPost_data = Replace(@formPost_data,@de_appC_cc_EventID,@cb_EventID)
      ENDIF
      SET @CVBRowCount = RowCount(LookupRows(@de_eca,'EmailName',@fn_Value))
      SET @de_e_Brand = Lookup(@sourceTable,'Brand',@sourceIdField,@fn_Value)
      SET @de_e_default_template_settings = Lookup(@sourceTable,'default_template_settings','EmailName',@fn_Value)
      SET @de_e_email_template = Lookup(@sourceTable,'email_template','EmailName',@fn_Value)
      SET @de_abi_email_template = Lookup(@de_abi,'email_template','Brand',@de_e_Brand)
      SET @de_e_email_template = IIF(EMPTY(@de_e_email_template),@de_abi_email_template,@de_e_email_template)
      IF @action_area != 'edit' AND @action_area != 'decouple' AND @action_area != 'backup' THEN
        SET @new_eca_EventID = @cb_EventID SET @fn_SubObjValue = @cb_EventID
      ELSEIF @action_area == 'edit' OR @action_area == 'backup' THEN
        SET @fn_SubObjValue = @edit_eca_EventID
      ENDIF
      IF (@action_area == 'edit' OR @action_area == 'decouple' OR @action_area == 'backup') THEN
        SET @de_appC_cc_Name = @edit_eca_ContentBlock
      ELSEIF @action_area != 'edit' AND @action_area != 'decouple' AND @action_area != 'backup' THEN
        SET @de_appC_cc_Name = TreatAscontent('%%{={{ }}=}%%{{data.de_appC_cc_Name}}')
      ENDIF
      SET @ref_cb = IIF(EMPTY(Lookup(@de_eca,'ref_cb_id','EventID',@edit_eca_EventID)),0,1)

      SET @result_CB_ID = IIF(@action_area != 'edit' AND @action_area != 'decouple' AND @action_area != 'backup',@new_eca_EventID,IIF(@action_area == 'decouple',@form_cb_id,@edit_eca_EventID))

      SET @publish_changes = TreatAscontent('%%{={{ }}=}%%{{data.published}}')
      IF @publish_changes == '1' THEN
        SET @published_json_data = @formPost_data
      ELSE
        SET @published_json_data = Lookup(@de_eca,'json_data','EventID',@result_CB_ID)
      ENDIF

      IF @formStatus != 0 THEN
        IF @role != 'Editor' AND @role != 'editorPlus' AND @action_area != 'backup' THEN
          IF  @action_area != 'duplicate' AND @action_area != 'duplicate_from_other_email' THEN
            SET @edit_eca_PositionInEmail = @cb_pos
          ENDIF
        ELSE
          IF @action_area == 'backup' THEN
            SET @edit_eca_PositionInEmail = Lookup(@de_eca_backup,'PositionInEmail','EventID',@edit_eca_EventID,'backup_id',@backup_id)
          ELSE
            IF @action_area != 'duplicate' AND @action_area != 'duplicate_from_other_email' THEN
              SET @edit_eca_PositionInEmail = Lookup(@de_eca,'PositionInEmail','EventID',@edit_eca_EventID)
            ENDIF
          ENDIF
        ENDIF

        IF EMPTY(@edit_eca_PositionInEmail) THEN
          RaiseError('missing position in email value')
        ELSEIF EMPTY(@CVBRowCount) THEN
          RaiseError('missing CBVROWCOUNT VALUE')
        ENDIF

        IF @formStatus != 0 THEN
          IF @Role != 'Editor' THEN
            IF @role != 'Editor' AND @role != 'editorPlus' AND @formStatus != 0 AND ((@action_area == 'edit') OR (@action_area != 'edit' AND @action_area != 'decouple' AND @action_area != 'backup')) THEN
              IF @action_area == 'add' OR @action_area == 'duplicate_from_other_email' THEN
                IF @edit_eca_PositionInEmail < Add(@CVBRowCount,1) THEN
                  SET @posCounter = Add(@edit_eca_PositionInEmail,1)
                  for @posChange = @edit_eca_PositionInEmail to @CVBRowCount do
                    SET @eventID_posChange = Field(Row(LookupORderedRows(@de_eca,0,'PositionInEmail ASC','EmailName',@sourceIdValue),@posChange),'EventID')
                    IF @posChange <= Add(@CVBRowCount,1) THEN
                      UpdateData(@de_eca,2,'EventID',@eventID_posChange,'EmailName',@sourceIdValue,'PositionInEmail',@posCounter,'UpdatedDate',@LocalTime,'UpdatedBy',@Username)
                    ENDIF
                    SET @posCounter = Add(@posCounter,1)
                  next @posChange
                ENDIF
              ELSEIF @action_area == 'duplicate' THEN
                SET @posCounter = Add(@edit_eca_PositionInEmail,2)
                for @posChange = Add(@edit_eca_PositionInEmail,1) to RowCount(LookupORderedRows(@de_eca,0,'PositionInEmail ASC','EmailName',@sourceIdValue)) do
                  SET @eventID_posChange = Field(Row(LookupORderedRows(@de_eca,0,'PositionInEmail ASC','EmailName',@sourceIdValue),@posChange),'EventID')
                  IF @posChange <= Add(@CVBRowCount,1) AND @posChange >= Add(@edit_eca_PositionInEmail,1) THEN
                    UpdateData(@de_eca,2,'EventID',@eventID_posChange,'EmailName',@sourceIdValue,'PositionInEmail',@posCounter,'UpdatedDate',@LocalTime,'UpdatedBy',@Username)
                  ENDIF
                  SET @posCounter = Add(@posCounter,1)
                next @posChange
              ELSE
                IF @edit_eca_PositionInEmail < @de_eca_PositionInEmail AND NOT EMPTY(@de_eca_PositionInEmail) THEN
                  SET @posCounter = Add(@edit_eca_PositionInEmail,1)
                  for @posChange = @edit_eca_PositionInEmail to @CVBRowCount do
                    SET @eventID_posChange = Field(Row(LookupORderedRows(@de_eca,0,'PositionInEmail ASC','EmailName',@sourceIdValue),@posChange),'EventID')
                    IF @edit_eca_EventID == @eventID_posChange THEN
                      UpdateData(@de_eca,2,'EventID',@eventID_posChange,'EmailName',@sourceIdValue,'PositionInEmail',@edit_eca_PositionInEmail,'UpdatedDate',@LocalTime,'UpdatedBy',@Username)
                    ELSEIF @posChange <= @de_eca_PositionInEmail THEN
                      UpdateData(@de_eca,2,'EventID',@eventID_posChange,'EmailName',@sourceIdValue,'PositionInEmail',@posCounter,'UpdatedDate',@LocalTime,'UpdatedBy',@Username)
                    ENDIF
                    SET @posCounter = Add(@posCounter,1)
                  next @posChange
                ELSEIF @edit_eca_PositionInEmail > @de_eca_PositionInEmail AND NOT EMPTY(@de_eca_PositionInEmail) THEN
                  SET @posCounter = @de_eca_PositionInEmail
                  for @posChange = @de_eca_PositionInEmail to @CVBRowCount do
                    SET @eventID_posChange = Field(Row(LookupORderedRows(@de_eca,0,'PositionInEmail ASC','EmailName',@sourceIdValue),@posChange),'EventID')
                    IF @edit_eca_EventID == @eventID_posChange THEN
                      UpdateData(@de_eca,2,'EventID',@eventID_posChange,'EmailName',@sourceIdValue,'PositionInEmail',@edit_eca_PositionInEmail,'UpdatedDate',@LocalTime,'UpdatedBy',@Username)
                    ELSEIF @posChange <= @edit_eca_PositionInEmail THEN
                      UpdateData(@de_eca,2,'EventID',@eventID_posChange,'EmailName',@sourceIdValue,'PositionInEmail',Subtract(@posCounter,1),'UpdatedDate',@LocalTime,'UpdatedBy',@Username)
                    ENDIF
                    SET @posCounter = Add(@posCounter,1)
                  next @posChange
                ELSEIF EMPTY(@de_eca_PositionInEmail) THEN
                  for @posChange = 1 to @CVBRowCount do
                    SET @eventID_posChange = Field(Row(LookupORderedRows(@de_eca,0,'PositionInEmail ASC','EmailName',@sourceIdValue),@posChange),'EventID')
                    IF EMPTY(Field(Row(LookupORderedRows(@de_eca,0,'PositionInEmail ASC','EmailName',@sourceIdValue),@posChange),'PositionInEmail')) THEN
                      SET @edit_eca_PositionInEmail = @posChange
                    ENDIF
                    UpdateData(@de_eca,2,'EventID',@eventID_posChange,'EmailName',@sourceIdValue,'PositionInEmail',@posChange,'UpdatedDate',@LocalTime,'UpdatedBy',@Username)
                  next @posChange
                ENDIF
              ENDIF
            ENDIF
            IF @role != 'editorPlus' AND (@make_backup == 1 OR @action_area == 'add' OR @action_area == 'duplicate' OR @action_area == 'decouple' OR @action_area == 'duplicate_from_other_email') THEN
              SET @edit_eca_field_set = ''
              for @var = 1 to RowCount(LookupRows(@sysFieldsDe,'Template','none','component','email_content_areas','Active','1','default_group','0')) do
                SET @sys_eca_rows = LookupRows(@sysFieldsDe,'Template','none','component','email_content_areas','Active','1','default_group','0')
                SET @deField = Field(Row(@sys_eca_rows,@var),'field_name')
                SET @deData_type = Field(Row(@sys_eca_rows,@var),'data_type')
                IF @action_area == 'backup' THEN
                  SET @deValue = Lookup(@de_eca_backup,@deField,'EventID',@edit_eca_EventID,'backup_id',@backup_id)
                ELSE
                  SET @deValue = Lookup(@de_eca,@deField,'EventID',@edit_eca_EventID)
                ENDIF
                TreatAsContent(CONCAT('%','%[ SET @de_eca_',@deField,' = "',@deValue,'" ]%','%'))
                SET @deValue = ''
                IF @role != 'Editor' THEN SET @deValue = TreatAscontent(Concat('%%{={{ }}=}%%','{{data.',@deField,'}}')) ENDIF
                IF EMPTY(@deValue) AND @deData_type == 'boolean' THEN SET @deValue = 0 ENDIF
                IF @deField != 'EventID' AND @deField != 'ContentBlock' AND @deField != 'PositionInEmail' AND @deField != 'Visible' AND @deField != 'shared' AND @deField != 'shared_with_all' THEN
                  TreatAsContent(CONCAT('%','%[ SET @edit_eca_',@deField,' = "',@deValue,'" ]%','%'))
                  SET @edit_eca_field_set = Concat(@edit_eca_field_set,',"',@deField,'","',@deValue,'"')
                ENDIF
                IF @var == RowCount(@sys_eca_rows) THEN
                  IF (@action_area == 'edit' OR @action_area == 'decouple' OR @action_area == 'backup') THEN
                    TreatAsContent(CONCAT('%','%[ UpdateData(@de_eca,1,"EventID",@result_CB_ID,"ContentBlock",@edit_eca_ContentBlock,"UpdatedBy",@userName,"UpdatedDate",@localTime,"json_data",@formPost_data,"published_json_data",@published_json_data',@edit_eca_field_set,') ]%','%'))
                    IF @make_backup == 1 THEN
                      IF @create_or_update_backup == 'Update' THEN
                        TreatAsContent(CONCAT('%','%[ UpsertData(@de_eca_backup,3,"EventID",@result_CB_ID,"EmailName",@sourceIdValue,"backup_id",@backup_id,"ContentBlock",@edit_eca_ContentBlock,"UpdatedBy",@userName,"UpdatedDate",@localTime,"json_data",@formPost_data,"published_json_data",@published_json_data',@edit_eca_field_set,') ]%','%'))
                      ELSEIF @create_or_update_backup == 'Create' THEN
                        TreatAsContent(CONCAT('%','%[ InsertData(@de_eca_backup,"EventID",@result_CB_ID,"EmailName",@sourceIdValue,"backup_id",@new_backup_id,"ContentBlock",@edit_eca_ContentBlock,"UpdatedBy",@userName,"UpdatedDate",@localTime,"json_data",@formPost_data,"published_json_data",@published_json_data',@edit_eca_field_set,') ]%','%'))
                      ENDIF
                    ENDIF
                  ELSE
                    IF @action_area == 'duplicate' THEN SET @edit_eca_PositionInEmail = Add(@edit_eca_PositionInEmail,1) ENDIF
                    InsertData(@de_eca,'PositionInEmail',@edit_eca_PositionInEmail,'ContentBlock',@edit_eca_ContentBlock,'EmailName',@sourceIdValue,'EventID',@new_eca_EventID,'default_template_settings',@edit_eca_default_template_settings,'UpdatedDate',@LocalTime,'UpdatedBy',@Username,'CreatedBy',@userName,'CreatedDate',@localTime,'friendly_name',@edit_eca_friendly_name,'marketing',@edit_eca_marketing,'hide_from_prem_subs',@edit_eca_hide_from_prem_subs,'hide_before_date',@edit_eca_hide_before_date,'hide_after_date',@edit_eca_hide_after_date,'hide_set_of_days',@edit_eca_hide_set_of_days,"json_data",@formPost_data,'use_ref_cb_hide',@edit_eca_use_ref_cb_hide,'use_hide_after_date',@edit_eca_use_hide_after_date,'use_hide_before_date',@edit_eca_use_hide_before_date,"published_json_data",@formPost_data,'disable_utm_appending',@edit_eca_disable_utm_appending)
                  ENDIF
                ENDIF
              next @var
            ELSEIF @action_area == 'edit' THEN
              UpsertData(@de_eca,2,"EventID",@result_CB_ID,"EmailName",@sourceIdValue,"ContentBlock",@edit_eca_ContentBlock,"UpdatedBy",@userName,"UpdatedDate",@localTime,"json_data",@formPost_data,"published_json_data",@published_json_data,'disable_utm_appending',@edit_eca_disable_utm_appending)
            ENDIF
            SET @action_area_text = IIF((@action_area == 'duplicate' OR @action_area == 'duplicate_from_other_email'),'duplicat',@action_area)
            SET @action_area_text = IIF(@action_area == 'decouple','decoupl',@action_area_text)
            IF @de_appC_cc_FriendlyName == 'Custom content block' THEN SET @de_appC_cc_FriendlyName = @edit_eca_ContentBlock ENDIF
            SET @f_name = Concat(@de_appC_cc_FriendlyName,IIF(NOT EMPTY(@de_eca_friendly_name),Concat(' | ',@de_eca_friendly_name),''))
            IF @action_area == 'duplicate_from_other_email' THEN
              SET @FormMessage = Concat('Successfully <strong>',@action_area_text,'ed</strong> content block <strong>',@f_name,'</strong> from the email <strong>',Lookup(@de_eca,'EmailName','EventID',@edit_eca_EventID),'</strong> into the email <strong>',@sourceIdValue,'</strong>.')
            ELSEIF @action_area == 'decouple' THEN
              SET @FormMessage = Concat('Successfully <strong>',@action_area_text,'ed</strong> content block <strong>',@f_name,'</strong> in the email <strong>',Lookup(@de_eca,'EmailName','EventID',@to_use_postData_cb_id),'</strong> from the email <strong>',@sourceIdValue,'</strong>.')
            ELSEIF @action_area == 'backup' THEN
              SET @FormMessage = Concat('Successfully used the <strong>backup content</strong> for the content block <strong>',@f_name,'</strong> in the email <strong>',Lookup(@de_eca,'EmailName','EventID',@to_use_postData_cb_id),'</strong>.')
            ELSE
              SET @FormMessage = Concat('Successfully <strong>',@action_area_text,'ed</strong> content block <strong>',@f_name,'</strong> in the email <strong>',@sourceIdValue,'</strong>.')
            ENDIF
            IF @create_or_update_backup == 'Delete' AND @formStatus != 0 THEN
              DeleteData(@de_cec_backup,'backup_id',@backup_id)
              DeleteData(@de_ecc_backup,'backup_id',@backup_id)
              DeleteData(@de_eca_backup,'backup_id',@backup_id)
            ENDIF
            IF @ref_cb != 1 OR @action_area == 'decouple' THEN
              SET @edit_emailTemplate_template_width = 650
              SET @content_width_header = 615
              SET @emailName = @sourceIdValue
              SET @de_e_additional_email_params = Lookup(@de_emailObject,'additional_email_params','EmailName',@sourceIdValue)
              SET @de_e_utmSource = Lookup(@de_emailObject,'utmSource','EmailName',@sourceIdValue)
              SET @de_e_utmMedium = Lookup(@de_emailObject,'utmMedium','EmailName',@sourceIdValue)
              SET @de_e_utmCampaign = Lookup(@de_emailObject,'utmCampaign','EmailName',@sourceIdValue)
              SET @de_e_utmContent = Lookup(@de_emailObject,'utmContent','EmailName',@sourceIdValue)
              SET @CCBSecret = IIF(@action_area != 'edit' AND @action_area != 'decouple' AND @action_area != 'backup',@cb_EventID,IIF(@action_area == 'decouple',@form_cb_id,@edit_eca_EventID))
              SET @de_e_Environment = @environmentAppCentre
              SET @cbPos = @edit_eca_PositionInEmail
              SET @cb_path = Lookup(@de_cb_types_list,'file_path_eml','name',@edit_eca_ContentBlock)
              ]%%<script runat="server">
              try {
                get_file(Platform.Variable.GetValue("@cb_path"),feature_branch,'ContentBlock')</script>%%[ SET @ContentBlock = TreatAsContent(@ContentBlock) ]%%<script runat="server">
              } catch (e){Platform.Variable.SetValue("@formStatus",0);Platform.Variable.SetValue("@formMessage",Platform.Function.Stringify(e.description));Platform.Variable.SetValue("@fn_target_field",Platform.Function.Stringify(e));var sourceSubObjTableValue = Platform.Variable.GetValue("@sourceSubObjTableValue");Platform.Variable.SetValue("@fn_SubObject",sourceSubObjTableValue);Platform.Variable.SetValue("@message_color",'danger');}</script>%%[
              IF @formStatus != 0 THEN
                UpdateData(@sourceTable,1,'EmailName',@sourceIdValue,'UpdatedDate',@LocalTime,'UpdatedBy',@Username)
                IF @preview_mode == 'focused' THEN
                  UpdateData('ent.Users',1,'Username',@userName,'preview_section',IIF(@action_area != 'edit' AND @action_area != 'decouple' AND @action_area != 'backup',@new_eca_EventID,@result_CB_ID))
                ENDIF
                IF @action_area != 'backup' THEN
                  UpsertData(@de_ecc,3,'content_id',@result_CB_ID,'EmailName',@sourceIdValue,'content_type','contentBlock','content_name',@edit_eca_ContentBlock,'friendly_name',@edit_eca_friendly_name,'PositionInEmail',@cbPos,'nr_of_links','','UpdatedDate',@localTime,'email_template',@de_e_email_template,'HTML',@ContentBlock)
                  InsertData(@de_ecc_saves,'content_id',@result_CB_ID,'EmailName',@sourceIdValue,'content_type','contentBlock','content_name',@edit_eca_ContentBlock,'friendly_name',@edit_eca_friendly_name,'PositionInEmail',@cbPos,'nr_of_links','','UpdatedDate',@localTime,'email_template',@de_e_email_template,'HTML',@ContentBlock,'save_id',GUID())
                  IF @make_backup == 1 THEN
                    IF @create_or_update_backup == 'Update' THEN
                      UpsertData(@de_ecc_backup,4,'content_id',@result_CB_ID,'EmailName',@sourceIdValue,'content_type','contentBlock','backup_id',@backup_id,'content_name',@edit_eca_ContentBlock,'friendly_name',@edit_eca_friendly_name,'PositionInEmail',@cbPos,'nr_of_links','','UpdatedDate',@localTime,'email_template',@de_e_email_template,'HTML',@ContentBlock)
                    ELSEIF @create_or_update_backup == 'Create' THEN
                      InsertData(@de_ecc_backup,'content_id',@result_CB_ID,'EmailName',@sourceIdValue,'content_type','contentBlock','backup_id',@new_backup_id,'content_name',@edit_eca_ContentBlock,'friendly_name',@edit_eca_friendly_name,'PositionInEmail',@cbPos,'nr_of_links','','UpdatedDate',@localTime,'email_template',@de_e_email_template,'HTML',@ContentBlock)
                    ENDIF
                  ENDIF
                ELSE
                  UpsertData(@de_ecc,3,'content_id',@result_CB_ID,'EmailName',@sourceIdValue,'content_type','contentBlock','content_name',@edit_eca_ContentBlock,'friendly_name',@edit_eca_friendly_name,'PositionInEmail',@cbPos,'nr_of_links','','UpdatedDate',@localTime,'email_template',@de_e_email_template,'HTML',@ContentBlock)
                  InsertData(@de_ecc_saves,'content_id',@result_CB_ID,'EmailName',@sourceIdValue,'content_type','contentBlock','content_name',@edit_eca_ContentBlock,'friendly_name',@edit_eca_friendly_name,'PositionInEmail',@cbPos,'nr_of_links','','UpdatedDate',@localTime,'email_template',@de_e_email_template,'HTML',@ContentBlock,'save_id',GUID())
                ENDIF
              ELSE
                IF @preview_mode == 'focused' THEN
                  UpdateData('ent.Users',1,'Username',@userName,'preview_section','')
                ENDIF
              ENDIF
            ELSEIF @formStatus != 0 THEN
              UpdateData(@sourceTable,1,'EmailName',@sourceIdValue,'UpdatedDate',@LocalTime,'UpdatedBy',@Username)
              IF @preview_mode == 'focused' THEN
                UpdateData('ent.Users',1,'Username',@userName,'preview_section',IIF(@action_area != 'edit' AND @action_area != 'decouple' AND @action_area != 'backup',@new_eca_EventID,@result_CB_ID))
              ENDIF
            ELSEIF @message_color != 'warning' THEN
              IF @preview_mode == 'focused' THEN
                UpdateData('ent.Users',1,'Username',@userName,'preview_section','')
              ENDIF
            ENDIF
          ENDIF
        ENDIF
      ENDIF
      ]%%{{/datasource}}%%[
    ENDIF
  ELSE
    SET @formStatus = 0 SET @message_color = 'danger'
    SET @errorMessage = @formMessage
  ENDIF
  IF @formStatus != 0 AND @make_backup == 1 THEN
    SET @formMessage = Concat(@formMessage,' Successfully <strong>',Concat(LowerCase(@create_or_update_backup),'d</strong>'),' ',IIF(@create_or_update_backup == 'Create','a','the selected'),' backup.')
  ENDIF
  IF @environmentAppCentre == 'UAT' THEN
    SET @formMessage = Concat(@formMessage, '@edit_contentBlock_dinkus: ',@edit_contentBlock_dinkus,' @edit_contentBlock_dinkus_option: ',Lookup(@de_cec,'Value','EventID',@edit_eca_EventID,'Name','dinkus_option','Template',@edit_eca_ContentBlock,'Active','1','Component','contentBlock','EmailName',@sourceIdValue),' if? ',IIF(TreatAscontent(Concat('%%{={{ }}=}%%','{{data.opens_dinkus}}')) == 'yes','yup','no'),' edit_eca_EventID: ',@edit_eca_EventID,' edit_eca_Contentblock: ',@edit_eca_ContentBlock,' sourceIdValue: ',@sourceIdValue,' @de_e_Environment: ',@de_e_Environment,' @edit_contentBlock_d_layout: ',@edit_contentBlock_d_layout,' d_name: ',@edit_contentBlock_d_name,' | edit_contentBlock_freeText_font_color: ',@edit_contentBlock_freeText_font_color,' do_not_action: ',@do_not_action,' action_area: ',@action_area, ' ref_cb: ',@ref_cb,' make_backup: ',@make_backup,' de_appC_cc_Name: ',@de_appC_cc_Name,' edit_contentBlock_CollectionID: ',@edit_contentBlock_CollectionID,' test: ',@test,' backup_id: ',@backup_id,' new backup_id: ',@new_backup_id, ' create_or_update_backup: ',@create_or_update_backup,' edit_eca_PositionInEmail: ',@edit_eca_PositionInEmail, ' CvbRowCount: ',@CvbRowCount,' testing: ',@testing,'@ew_cb_greeting: ',@new_cb_greeting,' edit_contentBlock_greeting: ',@edit_contentBlock_greeting,' t: ',@t)
  ENDIF
  IF EMPTY(@formStatus) THEN SET @formStatus = 1 SET @message_color = 'success'
  ELSEIF EMPTY(@message_color) OR EMPTY(@formMessage) THEN SET @formStatus = 0 SET @message_color = 'danger' SET @errorMessage = @formMessage ENDIF
  IF @message_color == 'warning' THEN SET @fn_Displayed = 1 SET @fn_SubObjValue = @sourceIdSubjValue SET @fn_SubObject = @sourceSubObjTableValue ENDIF
  IF @formStatus == 0 AND @message_color != 'warning' THEN SET @fn_SubObjValue = @sourceIdSubjValue SET @fn_SubObject = @sourceSubObjTableValue
    IF @preview_mode == 'focused' THEN UpdateData('ent.Users',1,'Username',@userName,'preview_section','') ENDIF
  ENDIF
  IF @do_not_action != 1 THEN
    InsertData(@stateDE,'EventID',@appEventID,'Object',IIF(EMPTY(@fn_Object),@appObject,@fn_Object),'SectionName',@currentAppSectionName,'SessionID',@cookie,'Message',@formMessage,'Displayed',IIF(EMPTY(@fn_Displayed),IIF(EMPTY(@fn_Action),1,0),@fn_Displayed),'DateAdded',@localTime,'Username',@username,'Status',IIF(EMPTY(@formStatus),1,@formStatus),'Value',@fn_Value,'ShowInactive',@showInactive,'Environment',IIF(EMPTY(@fn_Environment),@appStateEnvironment,@fn_Environment),'Section',@currentAppSection,'FormName',@formName,'Action',@fn_Action,'SubObject',IIF(NOT EMPTY(@navSel) OR EMPTY(@fn_Value),'',IIF(EMPTY(@fn_SubObject),@sourceSubObjTableValue,@fn_SubObject)),'SubObjValue',IIF(NOT EMPTY(@navSel) OR EMPTY(@fn_Value),'',IIF(EMPTY(@fn_SubObjValue),'',@fn_SubObjValue)),'FilterBU',@currentBrandFilter,'Progress',IIF(EMPTY(@fn_Progress) OR EMPTY(@fn_Value),IIF(@ParentApp != 'Analytics',0,30),@fn_Progress),'target_field','','brand_state',IIF(EMPTY(@fn_brand_state) OR EMPTY(@fn_Value),@userCurrentBrand,@fn_brand_state),'fields_state',IIF(EMPTY(@fn_fields_state) OR EMPTY(@fn_Value),@CurrentFields_state,@fn_fields_state),'appC_env',@environmentAppCentre)
  ENDIF
  IF NOT EMPTY(@errorMessage) THEN @formMessage = Concat(@formMessage,'<br>Files path: ',@files_path_eml) ENDIF
  IF @formStatus == 0 OR indexOf(@formMessage,'ExactTarget.OMM') > 0 THEN SET @errorMessage = @formMessage
    IF @role != 'Admin' AND indexOf(@formMessage,'ExactTarget.OMM') > 0 THEN
      SET @formMessage = 'The SFMC team is currently fixing a bug in this app and will get in touch once its ready to be used again. Thank you for your patience.'
    ENDIF
    IF indexOf(@formMessage,'ExactTarget.OMM') > 0 THEN
      SET @json_output = Concat('{"message_color":"',@message_color,'","form_message":',@formMessage,'}')
    ELSE
      SET @json_output = Concat('{"message_color":"',@message_color,'","form_message":"',@formMessage,'"}')
    ENDIF
  ELSE
    SET @json_output = Concat('{"message_color":"',@message_color,'","form_message":"',@formMessage,'"}')
  ENDIF
   ]%%%%=v(@json_output)=%%%%[
ELSEIF RowCount(LookupRows('ent.Users','SessionID',@cookie)) > 0 AND FormatDate(@app_LastLoginDate,"DD-MM-YYYY") != FormatDate(@localTime, "DD-MM-YYYY") THEN ]%%{"message":"reloadpage"}%%[
ELSE ]%%%%=RaiseError('Invalid request')=%%%%[
ENDIF ]%%