<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Settings Reuse Modal Test</title>
    <link rel="stylesheet" href="cmp_ai_editor.css" />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
      body {
        font-family: system-ui, -apple-system, sans-serif;
        padding: 2rem;
        background-color: #f8f9fa;
      }
      .test-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 2rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .test-section {
        margin-bottom: 2rem;
        padding: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
      }
      .test-section h3 {
        margin-top: 0;
        color: #495057;
      }
      .button-group {
        margin-top: 1rem;
      }
      .button-group button {
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        padding: 0.5rem 1rem;
        border: 1px solid #007bff;
        background: #007bff;
        color: white;
        border-radius: 0.25rem;
        cursor: pointer;
      }
      .button-group button:hover {
        background: #0056b3;
      }
      .status {
        margin-top: 1rem;
        padding: 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.9rem;
      }
      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .status.info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
      .status.warning {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
      }
      .mock-fields {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 0.25rem;
        margin: 1rem 0;
      }
      .mock-fields label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
      }
      .mock-fields input,
      .mock-fields select,
      .mock-fields textarea {
        width: 100%;
        padding: 0.375rem 0.75rem;
        margin-bottom: 0.5rem;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
      }
      .checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 0.5rem;
      }
      .checkbox-group label {
        display: flex;
        align-items: center;
        font-weight: normal;
        margin-bottom: 0;
      }
      .checkbox-group input[type="checkbox"] {
        width: auto;
        margin-right: 0.5rem;
        margin-bottom: 0;
      }
    </style>
  </head>
  <body>
    <div class="test-container">
      <h1>Settings Reuse Modal Test</h1>
      <p>
        This page demonstrates the new Settings Reuse Modal that prompts users
        when they use Edit with AI after already using the Generate button.
      </p>

      <div class="test-section">
        <h3>Mock Step 2 Fields</h3>
        <div class="mock-fields">
          <label>Content Source:</label>
          <div class="checkbox-group">
            <label
              ><input type="radio" name="content-source" value="none" checked />
              New</label
            >
            <label
              ><input type="radio" name="content-source" value="selection" />
              Content Preview</label
            >
            <label
              ><input type="radio" name="content-source" value="existing" />
              Existing</label
            >
            <label
              ><input type="radio" name="content-source" value="capi" />
              CAPI</label
            >
          </div>

          <label>Prompt:</label>
          <textarea
            name="fromSentence"
            rows="2"
            placeholder="Type your prompt here..."
          ></textarea>

          <label>Layout:</label>
          <div style="display: flex; gap: 1rem">
            <div>
              <label>Columns:</label>
              <input
                type="number"
                name="columns"
                value="1"
                min="1"
                style="width: 60px"
              />
            </div>
            <div>
              <label>Rows:</label>
              <input
                type="number"
                name="rows"
                value="1"
                min="1"
                style="width: 60px"
              />
            </div>
          </div>

          <label>Brand Style:</label>
          <select name="brandCssStyle">
            <option value="">Optional</option>
            <option value="classic">Classic</option>
            <option value="elegant">Elegant</option>
            <option value="modern">Modern</option>
          </select>

          <div class="checkbox-group">
            <label
              ><input type="checkbox" id="stack_content_vertical" checked />
              Stack vertically on mobile</label
            >
            <label
              ><input type="checkbox" id="extra_spacing_in_elements" checked />
              Extra spacing</label
            >
          </div>

          <label>Creativity Level:</label>
          <input
            type="range"
            id="creativityLevel"
            min="0"
            max="100"
            value="70"
          />
          <span id="creativityValue">70</span>

          <label>Goals:</label>
          <div class="checkbox-group">
            <label
              ><input
                type="checkbox"
                class="goal-checkbox"
                value="web_traffic"
              />
              Drive Web Traffic</label
            >
            <label
              ><input
                type="checkbox"
                class="goal-checkbox"
                value="excl_content"
              />
              Drive Newsletter Value</label
            >
            <label
              ><input type="checkbox" class="goal-checkbox" value="marketing" />
              Promote Products</label
            >
            <label
              ><input type="checkbox" class="goal-checkbox" value="beautify" />
              Beautify</label
            >
          </div>

          <label>Other Goal Description:</label>
          <textarea
            name="otherGoalDescription"
            rows="2"
            placeholder="Describe any other goals..."
          ></textarea>

          <label>AI Model:</label>
          <select name="aiModel">
            <option value="claude-3-5-haiku@20241022">Claude 3.5 Haiku</option>
            <option value="claude-3-5-sonnet@20240620">
              Claude 3.5 Sonnet
            </option>
            <option value="claude-3-5-sonnet-v2@20241022" selected>
              Claude 3.5 V2 Sonnet
            </option>
            <option value="claude-3-7-sonnet@20250219">
              Claude 3.7 Sonnet
            </option>
          </select>
        </div>

        <div class="button-group">
          <button onclick="simulateGenerateButtonClick()">
            Simulate Generate Button Click
          </button>
          <button onclick="simulateEditWithAI()">Simulate Edit with AI</button>
          <button onclick="modifySettings()">Modify Settings</button>
          <button onclick="resetToDefaults()">Reset to Defaults</button>
        </div>

        <div id="status" class="status" style="display: none"></div>
      </div>

      <div class="test-section">
        <h3>Test Scenarios</h3>
        <ol>
          <li>
            <strong>First Time User:</strong> Click "Simulate Edit with AI" - no
            modal should appear
          </li>
          <li>
            <strong>After Generate:</strong> Click "Simulate Generate Button
            Click", then "Simulate Edit with AI" - modal should appear
          </li>
          <li>
            <strong>With Modified Settings:</strong> Click "Modify Settings",
            then "Simulate Edit with AI" - modal should appear
          </li>
          <li>
            <strong>Don't Ask Again:</strong> Check the "Don't ask me again"
            option - modal won't appear for rest of session
          </li>
          <li>
            <strong>Default Settings:</strong> Click "Reset to Defaults", then
            "Simulate Edit with AI" - no modal should appear
          </li>
        </ol>
      </div>

      <div class="test-section">
        <h3>Global Variables Status</h3>
        <div id="globalStatus">
          <p>
            <strong>hasUsedGenerateButton:</strong>
            <span id="hasUsedStatus">false</span>
          </p>
          <p>
            <strong>skipSettingsReuseModal:</strong>
            <span id="skipModalStatus">false</span>
          </p>
          <p>
            <strong>Settings differ from defaults:</strong>
            <span id="settingsDifferStatus">false</span>
          </p>
        </div>
      </div>
    </div>

    <!-- Settings Reuse Modal (copied from main HTML file) -->
    <div
      class="modal fade layer_3_z_index"
      id="settingsReuseModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="settingsReuseModalLabel"
      aria-hidden="true"
      data-backdrop="static"
      data-keyboard="false"
    >
      <div
        class="modal-dialog modal-dialog-centered modal-dialog-custom"
        role="document"
      >
        <div class="modal-content custom-modal">
          <div class="modal-header">
            <h5 class="modal-title" id="settingsReuseModalLabel">
              Previous Settings Detected
            </h5>
            <button
              type="button"
              class="btn-close"
              id="settingsReuseModalClose"
              aria-label="Close"
              style="margin-top: -0.5vw"
            >
              &times;
            </button>
          </div>
          <div class="modal-body" style="font-size: 1vw">
            <p>
              You have previously used the Generate button with specific
              settings. Would you like to reuse those settings or start fresh?
            </p>
            <div class="alert alert-info mt-3" style="font-size: 0.9em">
              <strong>Previous settings include:</strong> Content source,
              creativity level, goals, model selection, and other preferences
              from Step 2.
            </div>
            <div class="form-check mt-3">
              <input
                type="checkbox"
                class="form-check-input"
                id="dontAskAgainCheckbox"
              />
              <label
                class="form-check-label"
                for="dontAskAgainCheckbox"
                style="font-size: 0.9em"
              >
                Don't ask me again during this session
              </label>
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              id="discardSettingsBtn"
            >
              Discard & Start Fresh
            </button>
            <button type="button" class="btn btn-primary" id="reuseSettingsBtn">
              Reuse Settings
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      // Global variables (simulating the real implementation)
      let hasUsedGenerateButton = false;
      let skipSettingsReuseModal = false;

      // Mock Settings Reuse Modal Manager (simplified version for testing)
      const SettingsReuseModalManager = {
        getDefaultFieldValues: function () {
          return {
            contentSource: "none",
            fromSentence: "",
            columns: 1,
            rows: 1,
            brandCssStyle: "",
            stackContentVertical: true,
            extraSpacingInElements: true,
            creativityLevel: 70,
            goalCheckboxes: [],
            otherGoalDescription: "",
            aiModel: "claude-3-5-sonnet-v2@20241022",
          };
        },

        getCurrentFieldValues: function () {
          const goalCheckboxes = [];
          $(".goal-checkbox:checked").each(function () {
            goalCheckboxes.push($(this).val());
          });

          return {
            contentSource: $("input[name='content-source']:checked").val(),
            fromSentence: $("textarea[name='fromSentence']").val() || "",
            columns: parseInt($("input[name='columns']").val()) || 1,
            rows: parseInt($("input[name='rows']").val()) || 1,
            brandCssStyle: $("select[name='brandCssStyle']").val() || "",
            stackContentVertical: $("#stack_content_vertical").is(":checked"),
            extraSpacingInElements: $("#extra_spacing_in_elements").is(
              ":checked"
            ),
            creativityLevel: parseInt($("#creativityLevel").val()),
            goalCheckboxes: goalCheckboxes,
            otherGoalDescription:
              $("textarea[name='otherGoalDescription']").val() || "",
            aiModel: $("select[name='aiModel']").val(),
          };
        },

        hasNonDefaultSettings: function () {
          const current = this.getCurrentFieldValues();
          const defaults = this.getDefaultFieldValues();

          if (current.contentSource !== defaults.contentSource) return true;
          if (current.fromSentence !== defaults.fromSentence) return true;
          if (current.columns !== defaults.columns) return true;
          if (current.rows !== defaults.rows) return true;
          if (current.brandCssStyle !== defaults.brandCssStyle) return true;
          if (current.stackContentVertical !== defaults.stackContentVertical)
            return true;
          if (
            current.extraSpacingInElements !== defaults.extraSpacingInElements
          )
            return true;
          if (current.creativityLevel !== defaults.creativityLevel) return true;
          if (current.goalCheckboxes.length !== defaults.goalCheckboxes.length)
            return true;
          if (current.otherGoalDescription !== defaults.otherGoalDescription)
            return true;
          if (current.aiModel !== defaults.aiModel) return true;

          return false;
        },

        clearFieldsToDefaults: function () {
          const defaults = this.getDefaultFieldValues();

          $("input[name='content-source']").prop("checked", false);
          $(
            "input[name='content-source'][value='" +
              defaults.contentSource +
              "']"
          ).prop("checked", true);
          $("textarea[name='fromSentence']").val(defaults.fromSentence);
          $("input[name='columns']").val(defaults.columns);
          $("input[name='rows']").val(defaults.rows);
          $("select[name='brandCssStyle']").val(defaults.brandCssStyle);
          $("#stack_content_vertical").prop(
            "checked",
            defaults.stackContentVertical
          );
          $("#extra_spacing_in_elements").prop(
            "checked",
            defaults.extraSpacingInElements
          );
          $("#creativityLevel").val(defaults.creativityLevel);
          $(".goal-checkbox").prop("checked", false);
          $("textarea[name='otherGoalDescription']").val(
            defaults.otherGoalDescription
          );
          $("select[name='aiModel']").val(defaults.aiModel);

          updateCreativityDisplay();
          updateGlobalStatus();
        },

        checkAndShowModalIfNeeded: function () {
          if (skipSettingsReuseModal) {
            showStatus(
              "Skipping modal - user chose not to be asked again",
              "info"
            );
            return false;
          }

          if (!hasUsedGenerateButton) {
            showStatus(
              "Skipping modal - Generate button not used before",
              "info"
            );
            return false;
          }

          if (!this.hasNonDefaultSettings()) {
            showStatus("Skipping modal - settings already at defaults", "info");
            return false;
          }

          $("#settingsReuseModal").modal("show");
          return true;
        },
      };

      // Event handlers
      $(document).ready(function () {
        // Update creativity level display
        $("#creativityLevel").on("input", updateCreativityDisplay);

        // Update status when settings change
        $("input, select, textarea").on("change input", updateGlobalStatus);

        // Modal event handlers
        $("#reuseSettingsBtn").click(function () {
          $("#settingsReuseModal").modal("hide");
          if ($("#dontAskAgainCheckbox").is(":checked")) {
            skipSettingsReuseModal = true;
          }
          showStatus("User chose to reuse settings", "success");
          updateGlobalStatus();
        });

        $("#discardSettingsBtn").click(function () {
          $("#settingsReuseModal").modal("hide");
          if ($("#dontAskAgainCheckbox").is(":checked")) {
            skipSettingsReuseModal = true;
          }
          SettingsReuseModalManager.clearFieldsToDefaults();
          showStatus(
            "User chose to discard settings and start fresh",
            "success"
          );
        });

        $("#settingsReuseModalClose").click(function () {
          $("#settingsReuseModal").modal("hide");
          showStatus("User closed modal - Edit with AI cancelled", "warning");
        });

        // Reset checkbox when modal is shown
        $("#settingsReuseModal").on("show.bs.modal", function () {
          $("#dontAskAgainCheckbox").prop("checked", false);
        });

        updateGlobalStatus();
      });

      function simulateGenerateButtonClick() {
        hasUsedGenerateButton = true;
        showStatus(
          "Generate button clicked - future Edit with AI actions will show modal if settings differ from defaults",
          "success"
        );
        updateGlobalStatus();
      }

      function simulateEditWithAI() {
        const shouldShowModal =
          SettingsReuseModalManager.checkAndShowModalIfNeeded();
        if (!shouldShowModal) {
          showStatus("Edit with AI proceeded without modal", "info");
        }
      }

      function modifySettings() {
        $("textarea[name='fromSentence']").val("Create an engaging newsletter");
        $("input[name='columns']").val(2);
        $("select[name='brandCssStyle']").val("classic");
        $("#creativityLevel").val(85);
        $(".goal-checkbox").first().prop("checked", true);
        $("select[name='aiModel']").val("claude-3-5-haiku@20241022");
        updateCreativityDisplay();
        updateGlobalStatus();
        showStatus(
          "Settings modified to trigger modal on next Edit with AI",
          "info"
        );
      }

      function resetToDefaults() {
        SettingsReuseModalManager.clearFieldsToDefaults();
        showStatus("Settings reset to defaults", "info");
      }

      function updateCreativityDisplay() {
        $("#creativityValue").text($("#creativityLevel").val());
      }

      function updateGlobalStatus() {
        $("#hasUsedStatus").text(hasUsedGenerateButton);
        $("#skipModalStatus").text(skipSettingsReuseModal);
        $("#settingsDifferStatus").text(
          SettingsReuseModalManager.hasNonDefaultSettings()
        );
      }

      function showStatus(message, type) {
        const $status = $("#status");
        $status
          .removeClass("success info warning")
          .addClass(type)
          .text(message)
          .show();

        setTimeout(function () {
          $status.fadeOut();
        }, 5000);
      }
    </script>
  </body>
</html>
