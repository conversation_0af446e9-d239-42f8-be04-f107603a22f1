%%[ IF @action_area == 'edit' THEN ]%%
  <input type="hidden" value="%%=Concat('Action:Edit//SubObjValue:',@de_eca_EventID,'//displayed:1//SubObject:Content Block Settings//Value:',@sourceIdValue)=%%" name="formPost" >
  <input type="hidden" value="edit" name="action_area" >
%%[ ELSE ]%%
  <input type="hidden" value="%%=Concat('Action:Create//Id:',@de_appC_cc_Name,'//SubObject:Content block settings//Value:',@sourceIdValue)=%%" name="formPost" >
  <input type="hidden" value="add" name="action_area" >
%%[ ENDIF ]%%
<input type="hidden" value="%%=v(@de_appC_cc_Name)=%%" name="cb_name" >
<input type="hidden" name="previous_appStateId" value="%%=v(@cb_EventID)=%%">
<input type="hidden" name="CVBRowCount" value="%%=v(@CVBRowCount)=%%">
<input type="hidden" name="de_eca_EventID" value="%%=v(@de_eca_EventID)=%%">
<input type="hidden" name="de_eca_PositionInEmail" value="%%=v(@de_eca_PositionInEmail)=%%">
<input type="hidden" name="de_appC_cc_Name" value="%%=v(@de_appC_cc_Name)=%%">
<input type="hidden" name="edit_eca_ContentBlock" value="%%=v(@de_eca_ContentBlock)=%%">
<input type="hidden" name="de_eca_use_more_cts" value="%%=v(@de_eca_use_more_cts)=%%">
%%[ IF NOT EMPTY(@de_eca_ref_cb_id) THEN ]%%
  <input type="hidden" name="ref_cb" value="1">
  <input type="hidden" name="ref_cb_id" value="%%=v(@de_eca_ref_cb_id)=%%">
%%[ ENDIF ]%%
%%[ IF @Role != 'Editor' AND @Role != 'EditorPlus' THEN ]%%
  <div class="accordion" id="BlockSettings%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%">
    <div class="card">
      <div class="card-header"  id="BlockSettings%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%">
        <h2 class="mb-0">
        <button class="btn btn-link emailEditordropDownMenuButton collapsed" type="button" id="open_blockSettings_tab" data-toggle="collapse" data-target="#BlockSettingsCollapse%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%" aria-expanded="true" aria-controls="BlockSettingsCollapse%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%" onclick="true_if_clicked('block_tab_opened%%=v(@de_eca_EventID)=%%');">
          <input type="hidden" id="block_tab_opened%%=v(@de_eca_EventID)=%%" name="block_section" value="0">
        Block Settings <i class="fas fa-angle-down rotate-icon emailEditorDropdownIcon" ></i>
        <div class="subText">
          Change the internal name, template styling, hide or show settings & block position.
        </div>
        </button>
        </h2>
      </div>
      <div id="BlockSettingsCollapse%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%" class="collapse" aria-labelledby="BlockSettings%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%" data-parent="#accordion_group%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%">
        <div class="card-body" style="padding: 0.8rem 0.8rem 0rem 0.8rem!important;">
          <div class="explainerfourAndHalf">Content block name (max char: 32)</div>
          <input class="question2" maxlength="32" name="friendly_name" value="%%=v(@de_eca_friendly_name)=%%" >
          <div class="subText2">
            Give this content block an internal name.
          </div>
          <div class="explainerfourAndHalf">Position in email</div>
          <select class="question2" name="PositionInEmail" id="PositionInEmail">
            <option value="%%=IIF(@action_area == 'edit',@de_eca_PositionInEmail,Add(@CVBRowCount,1))=%%">%%=IIF(@action_area == 'edit',@de_eca_PositionInEmail,Add(@CVBRowCount,1))=%%</option>
            %%[ IF @action_area == 'edit' THEN ]%%
            %%[ for @cbp = 1 to @CVBRowCount do IF @cbp != @de_eca_PositionInEmail THEN ]%%<option value="%%=v(@cbp)=%%">%%=v(@cbp)=%%</option>%%[ ENDIF next @cbp ]%%
            %%[ ELSE ]%%
            %%[ for @cbp = 1 to @CVBRowCount do ]%%<option value="%%=v(@cbp)=%%">%%=v(@cbp)=%%</option>%%[ next @cbp ]%%
            %%[ ENDIF ]%%
          </select>
          <div class="subText2">
            Select which content block position you want this content block to move to.
          </div>
          %%[ IF EMPTY(@de_eca_ref_cb_id) THEN ]%%
            %%[ IF @de_eca_ContentBlock != 'customBlockByKey' THEN ]%%
              %%[ IF @custom_block != true THEN ]%%
                <table class="tw_100">
                  <tr>
                    <td class="td_w_32_px">
                      <label class="checkcontainer2">
                        <input type="checkbox" name="default_template_settings" value="1" %%=v(@default_template_settings_check_cb)=%% id="default_template_settings_id%%=v(@de_eca_EventID)=%%" onclick="hide_or_show_group('default_template_settings_id%%=v(@de_eca_EventID)=%%','default_template_settings_fields_contentTitle%%=v(@de_eca_EventID)=%%','default_template_settings_fields_articleLayout%%=v(@de_eca_EventID)=%%','ct_divider_custom_fields%%=Concat(@cts_append,@ct_count,@de_eca_EventID)=%%','None');show_or_hide_on_click('custom_styling_fields_accordion%%=v(@de_eca_EventID)=%%');%%[ IF @de_eca_ContentBlock == 'signup_block' THEN ]%%show_or_hide_on_click('confirmation_page_accordions_%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%');%%[ ENDIF ]%%" style='display:none;'>
                        <span class="checkmark" ></span>
                      </label>
                    </td>
                    <td class="td_pL_32">
                      <div class="explainerfourAndHalf">Use default email template style settings (<strong>%%=v(@src_email_template)=%%</strong>)</div>
                    </td>
                  </tr>
                </table>
                <div class="subText2">
                  Check this box to get the default style settings. Uncheck to enable custom styling. You can change the default style via tabs "Email Settings and Layout > Email Layout".
                </div>
              %%[ ENDIF ]%%
              %%[ IF @action_area == 'edit' AND (@de_eca_ContentBlock == 'curated_collection' OR @de_eca_ContentBlock == 'recommended_collection' OR @de_eca_ContentBlock == 'video_collection') THEN ]%%
                <table class="tw_100">
                  <tr>
                    <td class="td_w_32_px">
                      <label class="checkcontainer2">
                        <input type="checkbox" name="use_more_cts" value="1" %%=v(@use_more_cts_check_cb)=%% id="use_more_cts_id%%=v(@de_eca_EventID)=%%" style='display:none;'>
                        <span class="checkmark" ></span>
                      </label>
                    </td>
                    <td class="td_pL_32">
                      <div class="explainerfourAndHalf">Use multiple content titles (<strong>based on max article count</strong>)</div>
                    </td>
                  </tr>
                </table>
                <div class="subText2">
                  Check this box to use multiple content titles - relative to the amount of maximum articles specified in the "Maximum articles" field in the "Collection Settings" tab. To change the amount, make sure to specify the amount of articles in the "Maximum articles" first, then save to reflect the change.
                </div>
              %%[ ENDIF ]%%
            %%[ ENDIF ]%%
          %%[ ENDIF ]%%
          <div class="accordion" id="hide_options_%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%">
            <div class="card">
              <div class="card-header">
                <h2 class="mb-0" id="ho_%%=v(@action_area)=%%_links_%%=v(@de_eca_EventID)=%%_header">
                  <button class="btn btn-link emailEditordropDownMenuButton collapsed" type="button" data-toggle="collapse" data-target="#hide_options_tab_%%=v(@action_area)=%%_%%=v(@de_eca_EventID)=%%" aria-expanded="false" aria-controls="hide_options_tab_%%=v(@action_area)=%%_%%=v(@de_eca_EventID)=%%">
                    Additional hide or show settings
                    <i class="fas fa-angle-down rotate-icon emailEditorDropdownIcon" aria-hidden="true"></i>
                    <div class="subText">
                      Apply more granular hide or show settings for the content block here.
                    </div>
                  </button>
                </h2>
              </div>
              <div id="hide_options_tab_%%=v(@action_area)=%%_%%=v(@de_eca_EventID)=%%" class="collapse" aria-labelledby="ho_%%=v(@action_area)=%%_links_%%=v(@de_eca_EventID)=%%_header" data-parent="#hide_options_%%=v(@action_area)=%%%%=v(@de_eca_EventID)=%%">
                <div class="card-body" style="padding-bottom: 0.1rem!important;">
                  %%[ IF NOT EMPTY(@de_eca_ref_cb_id) THEN ]%%
                    <table class="tw_100">
                        <tr>
                          <td class="td_w_32_px">
                            <label class="checkcontainer2">
                              <input type="checkbox" name="use_ref_cb_hide" value="1" %%=IIF(@de_eca_use_ref_cb_hide == true,'checked','')=%% id="ref_cb_hide_id%%=v(@de_eca_EventID)=%%" style='display:none;' onclick="hide_or_show_checkbox('ref_cb_hide_id%%=v(@de_eca_EventID)=%%','hide_show_fields%%=v(@de_eca_EventID)=%%');">
                              <span class="checkmark" ></span>
                            </label>
                          </td>
                          <td class="td_pL_32">
                            <div class="explainerfourAndHalf">Use the original block's additional hide or show settings</div>
                          </td>
                        </tr>
                    </table>
                    <div class="subText2">
                      Use the original block's additional hide or show settings. 
                    </div>
                  %%[ ENDIF ]%%
                  <div %%[ IF NOT EMPTY(@de_eca_ref_cb_id) THEN ]%% class="related_fields" %%[ ENDIF ]%% id="hide_show_fields%%=v(@de_eca_EventID)=%%" style="margin-bottom:.8rem;%%[ IF NOT EMPTY(@de_eca_ref_cb_id) AND @de_eca_use_ref_cb_hide == true THEN ]%%display:none;%%[ ELSE ]%%display:block;%%[ ENDIF ]%%">
                    %%[ IF Lookup(@de_abi,'allow_mkt_in_email','Brand',@src_Brand) == true THEN ]%%
                    <table class="tw_100">
                      <tr>
                        <td class="td_w_32_px">
                          <label class="checkcontainer2">
                              %%[IF @de_eca_ContentBlock == 'signup_block' THEN]%%
                                  <input
                                  type="checkbox" name="marketing" value="1"
                                  id="marketing_settings_id%%=v(@de_eca_EventID)=%%" 
                                  style='display:none;'
                                  checked/>
                                  <span class="checkmark" style="opacity: 0.5; pointer-events: none"></span>
                              %%[ELSE]%%
                                  <input
                                  type="checkbox" name="marketing" value="1"
                                  %%=v(@de_eca_marketing_check_cb)=%%
                                  id="marketing_settings_id%%=v(@de_eca_EventID)=%%" 
                                  style='display:none;'>
                                  <span class="checkmark"></span>
                              %%[ENDIF]%%
                          </label>
                        </td>
                        <td class="td_pL_32">
                          <div class="explainerfourAndHalf">Only show block to marketing-opted-in customers</div>
                        </td>
                      </tr>
                    </table>
                    <div class="subText2">
                      Check this box to hide the block from non-marketing <strong>%%=v(@src_Masthead)=%%</strong> contacts.
                      </div>
                    %%[ ENDIF ]%%
                    <table class="tw_100">
                      <tr>
                        <td class="td_w_32_px">
                          <label class="checkcontainer2">
                            <input type="checkbox" name="hide_from_prem_subs" value="1" %%=v(@de_eca_prem_check_cb)=%% style='display:none;'>
                            <span class="checkmark" ></span>
                          </label>
                        </td>
                        <td class="td_pL_32">
                          <div class="explainerfourAndHalf">Always hide block from premium customers</div>
                        </td>
                      </tr>
                    </table>
                    <div class="subText2">
                      Check this box to hide the block from premium "<strong>%%=v(@src_Masthead)=%%</strong>" subscribers.
                    </div>
                    <table class="tw_100">
                      <tr>
                        <td class="td_w_32_px">
                          <label class="checkcontainer2">
                            <input type="checkbox" name="use_hide_after_date" value="1" %%=v(@hide_after_date_cb)=%% id="hide_after_date_checkbox_%%=v(@de_eca_EventID)=%%" onclick="show_or_hide_checkbox('hide_after_date_checkbox_%%=v(@de_eca_EventID)=%%','hide_after_date_field_%%=v(@de_eca_EventID)=%%');" style='display:none;'>
                            <span class="checkmark" ></span>
                          </label>
                        </td>
                        <td class="td_pL_32">
                          <div class="explainerfourAndHalf">Set a "hide-block-from" date</div>
                        </td>
                      </tr>
                    </table>
                    <table %%=IIF(@hide_after_date_cb == 'checked' AND @action_area != 'add','','style="display:none;"')=%% id="hide_after_date_field_%%=v(@de_eca_EventID)=%%">
                      <tr>
                        <td>
                          <input type="date" class="question2" name="hide_after_date" value="%%=v(@de_eca_hide_after_date)=%%" style="margin-top:0!important;">
                        </td>
                      </tr>
                    </table>
                    <div class="subText2">
                      Set a "hide-block-from" date for the content block to hide it from that date onwards.
                    </div>
                    <table class="tw_100">
                      <tr>
                        <td class="td_w_32_px">
                          <label class="checkcontainer2">
                            <input type="checkbox" name="use_hide_before_date" value="1" %%=v(@hide_before_date_cb)=%% id="hide_before_date_checkbox_%%=v(@de_eca_EventID)=%%" onclick="show_or_hide_checkbox('hide_before_date_checkbox_%%=v(@de_eca_EventID)=%%','hide_before_date_field_%%=v(@de_eca_EventID)=%%');" style='display:none;'>
                            <span class="checkmark" ></span>
                          </label>
                        </td>
                        <td class="td_pL_32">
                          <div class="explainerfourAndHalf">Set a "hide-block-until" date</div>
                        </td>
                      </tr>
                    </table>
                    <table %%=IIF(@hide_before_date_cb == 'checked' AND @action_area != 'add','','style="display:none;"')=%% id="hide_before_date_field_%%=v(@de_eca_EventID)=%%">
                      <tr>
                        <td>
                          <input type="date" class="question2" name="hide_before_date" value="%%=v(@de_eca_hide_before_date)=%%" style="margin-top:0!important;">
                        </td>
                      </tr>
                    </table>
                    <div class="subText2">
                      Set a "hide-block-until" date for the content block to hide it before a certain date. 
                    </div>
                    <div class="explainerfourAndHalf">Always hide on certain days of the week</div>
                    <table>
                      <tr>
                        <td style="width: 14.28%!important;">Mon</td>
                        <td style="width: 14.28%!important;">Tue</td>
                        <td style="width: 14.28%!important;">Wed</td>
                        <td style="width: 14.28%!important;">Thu</td>
                        <td style="width: 14.28%!important;">Fri</td>
                        <td style="width: 14.28%!important;">Sat</td>
                        <td style="width: 14.28%!important;">Sun</td>
                      </tr>
                      <tr>
                        <td style="width: 14.28%!important;">
                          <label class="checkcontainer" style="display:inline;">
                            <input type="checkbox" name="hide_set_of_days" value="Monday" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Monday') > 0,'checked','')=%% style='display:none;'>
                            <span class="checkmark" ></span>
                          </label>
                        </td>
                        <td style="width: 14.28%!important;">
                          <label class="checkcontainer" style="display:inline;">
                            <input type="checkbox" name="hide_set_of_days" value="Tuesday" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Tuesday') > 0,'checked','')=%% style='display:none;'>
                            <span class="checkmark" ></span>
                          </label>
                        </td>
                        <td style="width: 14.28%!important;">
                          <label class="checkcontainer" style="display:inline;">
                            <input type="checkbox" name="hide_set_of_days" value="Wednesday" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Wednesday') > 0,'checked','')=%% style='display:none;'>
                            <span class="checkmark" ></span>
                          </label>
                        </td>
                        <td style="width: 14.28%!important;">
                          <label class="checkcontainer" style="display:inline;">
                            <input type="checkbox" name="hide_set_of_days" value="Thursday" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Thursday') > 0,'checked','')=%% style='display:none;'>
                            <span class="checkmark" ></span>
                          </label>
                        </td>
                        <td style="width: 14.28%!important;">
                          <label class="checkcontainer" style="display:inline;">
                            <input type="checkbox" name="hide_set_of_days" value="Friday" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Friday') > 0,'checked','')=%% style='display:none;'>
                            <span class="checkmark" ></span>
                          </label>
                        </td>
                        <td style="width: 14.28%!important;">
                          <label class="checkcontainer" style="display:inline;">
                            <input type="checkbox" name="hide_set_of_days" value="Saturday" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Saturday') > 0,'checked','')=%% style='display:none;'>
                            <span class="checkmark" ></span>
                          </label>
                        </td>
                        <td style="width: 14.28%!important;">
                          <label class="checkcontainer" style="display:inline;">
                            <input type="checkbox" name="hide_set_of_days" value="Sunday" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Sunday') > 0,'checked','')=%% style='display:none;'>
                            <span class="checkmark" ></span>
                          </label>
                        </td>
                      </tr>
                    </table>
                    <div class="subText2" style="padding-top:5px;">
                      Always hide the block on certain days of the week.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
%%[ ENDIF ]%%