<script runat="server">
try {
 var cookie = Platform.Request.GetCookieValue('token');
 var sys_env = Platform.Request.GetQueryStringParameter('sys_env');
 var biz_env = Platform.Request.GetQueryStringParameter('biz_env');
 Platform.Variable.SetValue("@sys_env",sys_env);
 Platform.Variable.SetValue("@environmentAppCentre",sys_env);
 Platform.Variable.SetValue("@biz_env",biz_env);
 Platform.Variable.SetValue("@cookie",cookie);
 </script>
 %%[
 SET @localTime = SystemdateToLocalDate(NOW())
 SET @stateDE = "ent.AppC_Notifications"
 /* SET @test_user = '<EMAIL>' */
 IF NOT EMPTY(@test_user) THEN
  SET @appStateId = Replace(GUID(),'-','_')
  SET @userName = @test_user
  SET @testing = 1
 ELSE
  SET @appStateId = Replace(GUID(),'-','_')
  SET @userName = Lookup("ent.Users","UserName","SessionID",@cookie)
 ENDIF
 SET @appStateId = QueryParameter("appStateId")
 SET @lastLoginDate = Lookup("ent.Users","LastLoginDate","SessionID",@cookie)
 IF @lastLoginDate == '' THEN 
   SET @lastLoginDate = '1900-01-01' 
 ENDIF
 SET @lastLoginDateFormatted = FormatDate(@lastLoginDate,"YYYY-MM-DD")
 SET @localTimeFormatted = FormatDate(@localTime,"YYYY-MM-DD")
 SET @userCookie = Lookup("ent.Users","SessionID","SessionID",@cookie)
 ]%%
 <script runat="server">
 Platform.Load("Core", "1");
 var userName;
 var cookie               = Platform.Variable.GetValue("@cookie");
 var component            = 'Email editor/AI editor/cpg_ai-editor.amp';
 var localTime            = Platform.Variable.GetValue("@localTime");
 var userCookie           = Platform.Function.Lookup("ent.Users", "SessionID", "SessionID", cookie);
 var userName            = Platform.Variable.GetValue("@userName");
 var testing             = Platform.Variable.GetValue("@testing");
 var formattedLastLoginDate = Platform.Variable.GetValue("@lastLoginDateFormatted");
 var formattedLocalTime     = Platform.Variable.GetValue("@localTimeFormatted");
 var env = biz_env;
 var access = 1;
 var feature_branch = (sys_env != 'DEV' && sys_env != 'SIT' && sys_env != 'UAT' && sys_env != 'Release' && sys_env != 'PROD') ? sys_env : '';
 Platform.Variable.SetValue("@feature_branch", feature_branch);
 Platform.Variable.SetValue("@appStateEnvironment",biz_env);

 function log_ui_error(var_name, errMsg, raw_error) { var errMsg;
   try { errMsg = (raw_error) ? errMsg + '<br>' + Platform.Function.Stringify(raw_error) : errMsg;
     var file_paths = Platform.Variable.GetValue("@paths") || "";
     if (errMsg) {
      Platform.Response.Write(file_paths + '<br>' + errMsg); Platform.Variable.SetValue("@errorMessage", errMsg);
     }
     Platform.Function.InsertData("ent.appC_ui_error_log",
       ["event_id", "SessionID", "file_paths", "var_name", "error_message", "created_date", "created_by", "sys_env","biz_env"],
       [Platform.Function.GUID(), cookie, file_paths, var_name, errMsg, localTime, userName, sys_env, env]
     );
   } catch(e) { Platform.Response.Write("Error logging: " + Platform.Function.Stringify(e)); }
 }

 /* access check */
 if (feature_branch) {
   if (!userName) {
     log_ui_error('access_check','Invalid request..'); access = 0;
   } else {
     var fb_access = Platform.Function.Lookup("ent.Users", "feature_branch_access", "UserName", userName);
     fb_access = (fb_access === true);
     if (!fb_access) {
       log_ui_error('access_check','The username "'+userName+'" does not have access to the feature branch environments.'); access = 0;
     }
   }
 } else if (sys_env != 'PROD') {
   var env_access = Platform.Function.Lookup("ent.Users", "appc_"+sys_env+"_access", "UserName", userName);
   env_access = (env_access === true);
   if (!env_access) {
     log_ui_error('access_check','The username "'+userName+'" does not have access to the '+sys_env+' environment.'); access = 0;
   }
 }

 function safeJsonParse(jsonString) {
   if (typeof jsonString !== "string") { return jsonString; }
   jsonString = jsonString.replace(/[\u0000-\u001F]/g, "");
   try { var result = eval("(" + jsonString + ")"); return result; }
   catch (e) { log_ui_error(name, "Error parsing JSON: ",e); return; }
 }

 function get_file(path, branch, name) { var b64d;
   try { var paths = Platform.Variable.GetValue("@paths") || "";
     Platform.Variable.SetValue("@paths", (!paths) ? " ➜ " + path : paths + "<br> ➜ " + path);
     if (!path) { log_ui_error(name, "The path param is empty."); return; }
     if (sys_env != 'PROD' && sys_env != 'Release' && /archive\/|pocs\/|cpage\//.test(path)) {
       log_ui_error(name, "Remove file from the archive, cpage or POCs folders."); return;
     }
     if (branch) {
       try { var status = [];
         var requestUrl = ['https://api.github.com/repos', 'newscorp-ghfb', 'app-centre', 'contents', Platform.Function.UrlEncode(path, false)].join('/') + '?ref=' + Platform.Function.UrlEncode(branch);
         var resp = Platform.Function.HTTPGet(requestUrl, false, 0, ["Authorization", "User-Agent"], ["token " + Platform.Function.Lookup("ent.api_keys", "Key", "Id", "GitHub"), "SSJS-GitHub-App"], status);
         var j_resp; j_resp = safeJsonParse(resp);
         if (j_resp && j_resp.content) {
           b64d = Platform.Function.Base64Decode(j_resp.content.replace(/(\r\n|\n|\r)/gm, ""));
           if (b64d) { Platform.Variable.SetValue("@b64d", b64d);
             if (name) { Platform.Variable.SetValue("@" + name, b64d);
             } else { </script>%%=TreatAsContent(@b64d)=%%<script runat="server"> }
           } else { log_ui_error(name, "0. File not in github"); return; }
         } else { log_ui_error(name, "1. Unexpected response structure: ",resp); return; }
       } catch (e) { log_ui_error(name, "2. Error in file retrieval from GitHub: ",e); return; }
     } else {
       try { b64d = Platform.Function.Lookup("ent.gh_appC_files_" + sys_env, "Code", "path", path);
         if (b64d) { Platform.Variable.SetValue("@b64d", b64d);
           if (name) { Platform.Variable.SetValue("@" + name, b64d);
           } else { </script>%%=TreatAsContent(@b64d)=%%<script runat="server"> }
         } else { log_ui_error(name, "3. File not found in DE: " + path); return; }
       } catch (e) { log_ui_error(name, "4. Error looking up file in DE: ",e); return; }
     }
   } catch (e) { log_ui_error(name, '5. Error setting variable for file: ',e); return; }
 }

 try {
  if (formattedLastLoginDate === formattedLocalTime && userCookie && (cookie === userCookie && access != 0) || testing == 1) {
    </script>
    <!DOCTYPE html>
    <html>
    <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/tinymce@7.5.1/tinymce.min.js" integrity="sha256-MVGU+LIEDhP/Nop4vrmoLFM2kgE3uVzWBiV5YhKNG1Q=" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/tinymce@7.5.1/skins/ui/oxide/content.min.css" integrity="sha256-XUavd8vfDG5gvN86WlyX+bAUpZqfJTJHoVpr0AJzrkw=" crossorigin="anonymous">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@simonwep/pickr/dist/themes/classic.min.css"/> <!-- or another theme -->
    <script src="https://cdn.jsdelivr.net/npm/@simonwep/pickr/dist/pickr.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/js-base64/3.7.2/base64.min.js"></script>
    <!-- <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"> -->
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    %%=TreatAsContent(HttpGet('https://dl.dropbox.com/scl/fi/n01rxtxsnvga8du4b0nx6/cmp_ai_editor.css?rlkey=fnr5o2ks7ldwrss7y7y3dw260&st=shdza6c4'))=%%
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.16.0/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script tyle="text/javascript">
      async function appC_ui_component(component,target,tab,moreParams,showModal,spinningWheel,refreshIframe,idOneOff,disableButton,enableButton) {
        if (refreshIframe == '1') {
          %%[ IF @preview_mode != 'focused' THEN ]%%
            $("#emailPreviewIframe").css("display","none");
          %%[ ENDIF ]%%
        };
        if (disableButton) { let button = '#'+disableButton; $(button).prop('disabled',true); $(button).css('background-color','grey'); $(button).html('Loading'); }
        if (spinningWheel == '1') {
          let spinning_modal = '<div style="padding: 1rem 1.5rem 1rem 1.5rem;"><div style="text-align:center;"> Loading</div><div style="text-align:center;"><br><div class="modall-dialog modal-sm" style="text-align:center;max-width:100%;"><div class="modall-content"><span class="fa fa-circle-o-notch fa-spin fa-fw fa-2x" aria-hidden="true"></span></div></div></div></div>';
          document.getElementById(target).innerHTML = spinning_modal;
          %%[ IF @environmentAppCentre != 'UAT' AND @environmentAppCentre != 'Release' THEN ]%%
          console.log("spinned the wheel - appC_ui_component()");
          %%[ ENDIF ]%%
        };
        if (showModal != '0') { $('.modall').modal('show'); };
        var file = "%%=Concat('https://cloud.e.newsdigitalmedia.com.au/gh_',LowerCase(@appStateEnvironment),'_appC_ui_components?key=',@cookie,'&appStateId=',@appStateId,'&sys_env=',@environmentAppCentre,'&component=')=%%"+encodeURIComponent(component)+'&'+moreParams+'&feature_branch=%%=v(@feature_branch)=%%&biz_env=%%=v(@biz_env)=%%';
        %%[ IF @environmentAppCentre != 'UAT' AND @environmentAppCentre != 'Release' THEN ]%%
        console.log("%%=Concat('https://cloud.e.newsdigitalmedia.com.au/gh_',LowerCase(@appStateEnvironment),'_appC_ui_components?key=',@cookie,'&appStateId=',@appStateId,'&sys_env=',@environmentAppCentre,'&component=')=%%"+encodeURIComponent(component)+'&'+moreParams+'&feature_branch=%%=v(@feature_branch)=%%');
        %%[ ENDIF ]%%
        let myObject = await fetch(file);
        let myText = await myObject.text();
        document.getElementById(target).innerHTML = myText;
        if (idOneOff) { $("#" + idOneOff).removeAttr('onclick'); };
        if (showModal != '0') { $('.modall').modal('hide'); };
        if (component == 'Home/cmp_dashboard.amp') { load_home_page_logic(); };
        if (component == 'Program editor/cmps/cmp_programs_list.amp') { load_programs_app_logic(); };
        if (component == 'Program editor/cmps/cmp_p_form.amp' && moreParams.indexOf('app=program_editor&aa=edit') > 0) { $('#programToggleForm'); };
        %%[ IF @preview_mode == 'focused' THEN ]%%
          if (refreshIframe == '1') {
            $("#emailPreviewIframe").css("display","none");
            if (tab == 'template') { 
              load_subject_and_preheader(); 
            };
            if (tab && component != 'Email editor/template settings and layout tab/cmp_settings_and_layout.amp') { change_block_view(tab); };
          };
        %%[ ELSE ]%%
          if (refreshIframe == '1') {
            refreshIframebyId('emailPreviewIframe');
          };
        %%[ ENDIF ]%%
        %%[ IF @environmentAppCentre != 'UAT' AND @environmentAppCentre != 'Release' THEN ]%%
        console.log('The component "' + component + '" was placed in id "' + target + '"');
        console.log('appC_ui_component('+component+','+target+','+tab+','+moreParams+','+showModal+','+spinningWheel+','+refreshIframe+','+idOneOff+','+disableButton+','+enableButton+')');
        %%[ ENDIF ]%%
        if (target === 'brand_rows' || target === 'all_accessible_emails_rows') {
          filterSearchInputPostion(target);
        }

        if (enableButton) { let button = '#'+enableButton; $(button).prop('disabled',false); $(button).css('background-color','#497edd'); $(button).html('Save'); }
        if (component == 'Email editor/cbs/cmp_free_text_block.amp') {
            load_free_text_logic(tab);
            %%[ IF @environmentAppCentre != 'UAT' AND @environmentAppCentre != 'Release' THEN ]%%
            console.log('loaded free text block js logic.');
            %%[ ENDIF ]%%
          } else if (component == 'quote_block') {
            load_quote_text_logic(tab);
            %%[ IF @environmentAppCentre != 'UAT' AND @environmentAppCentre != 'Release' THEN ]%%
            console.log('loaded quote block js logic.');
            %%[ ENDIF ]%%
          }
        if (refreshIframe != '1') {
          %%[ IF @preview_mode == 'focused' THEN ]%%
            $("#emailPreviewIframe").css("display","block");
          %%[ ENDIF ]%%
        };
      }
    </script>
    %%=TreatAsContent(HttpGet('https://dl.dropbox.com/scl/fi/mobublgjnlstk2lamk0nx/cmp_ai-editor_html.amp?rlkey=5khhtv5xy66t5snlzvhi3f3k4&st=pqtr83nn'))=%%
    %%=TreatAsContent(HttpGet('https://dl.dropbox.com/scl/fi/lgfcwa9ok7zvsxm32k0x7/cmp_ai_editor_js.amp?rlkey=c1q0kvy3b214t0u5t0r5jg2w2&st=7lipzwje'))=%%
    %%=TreatAsContent(HttpGet('https://dl.dropbox.com/scl/fi/oc4wxjliohhujoby73ocg/cmp_ai_pop-up_js.amp?rlkey=osxwl2uazik9wi9jbyxd8z95i&st=xgatzwmv'))=%%
    </html>
    <script runat="server">
  } else if (access == 0 && userName) {
    Platform.Response.Write("The username <strong>"+userName+"</strong> does not have access to the <strong>"+sys_env+"</strong> environment.");
  } else {
    Platform.Response.Write("This component cannot be displayed as the user session has expired.");
  }
 } catch(e) {
   log_ui_error(name, "Fatal error in main execution: ",e);
 }
} catch(e) {
   Platform.Response.Write(Platform.Function.Stringify(e));
}
</script>