%%[
SET @Action = Lookup(@stateDE,"Action","EventID",@appStateId)
SET @app = Lookup(@stateDE,"Object","EventID",@appStateId)
SET @active = Lookup("ent.Users","Active","SessionID",@cookie)
SET @role = Lookup("ent.Users","UserRole","SessionID",@cookie)
SET @contactKey = Lookup("ent.Users","ContactKey","SessionID",@cookie)
SET @emailAddress = Lookup("ent.Users","EmailAddress","SessionID",@cookie)
SET @appAccess = Lookup("ent.Users","AccessLevel","SessionID",@cookie)
SET @secret = Lookup("ent.Users","Secret","SessionID",@cookie)
SET @environmentAppCentre = @sys_env
SET @appStateEnvironment = @biz_env

IF EMPTY(@biz_env) THEN SET @biz_env = "PROD" ENDIF
SET @de_email_json = IIF(@biz_env == "UAT","ent.email_json_cache_UAT","ent.email_json_cache")
SET @userBrand = Lookup("ent.Users","Brand","SessionID",@cookie)
SET @userCurrentBrand = IIF(EMPTY(Lookup("ent.Users","CurrentBrand","SessionID",@cookie)),@userBrand,Lookup("ent.Users","CurrentBrand","SessionID",@cookie))
SET @ShowInactive = Lookup(@stateDE,"ShowInactive","EventID",@appStateId)
SET @activeStatusName = IIF(@ShowInactive == true,"Inactive","Active")
SET @NStatusText = IIF(@ShowInactive == true,"Activate","Deactivate")
SET @activeStatusName_opposite = IIF(@activeStatusName == "Active","Inactive","Active")
SET @sourceActiveBadge = IIF(@activeStatusName == "Active","checked","")
SET @sourceEnvBadge = IIF(@biz_env == "UAT","","checked")
SET @DisplayMessage = IIF(Lookup(@stateDE,"Displayed","EventID",@appStateId) == false,1,0)
SET @EventAction = Lookup(@stateDE,"Action","EventID",@appStateId)
SET @CurrentAppSection = IIF(EMPTY(Lookup(@stateDE,"Section","EventID",@appStateId)),1,Lookup(@stateDE,"Section","EventID",@appStateId))
SET @CurrentAppSectionName = Lookup(@stateDE,"SectionName","EventID",@appStateId)
SET @sourceSubObjTableValue = Lookup(@stateDE,"SubObject","EventID",@appStateId)
SET @sourceObject = Lookup(@stateDE,"Object","EventID",@appStateId)
SET @sourceIdSubjValue = Lookup(@stateDE,"SubObjValue","EventID",@appStateId)
SET @EventStatus = Lookup(@stateDE,"Status","EventID",@appStateId)
SET @sourceIdValue = Lookup(@stateDE,"Value","EventID",@appStateId)
SET @CurrentFormName = Lookup(@stateDE,"FormName","EventID",@appStateId)
SET @CurrentProgress = Lookup(@stateDE,"Progress","EventID",@appStateId)
SET @CurrentTarget_field = Lookup(@stateDE,"target_field","EventID",@appStateId)
SET @CurrentBrand_state = Lookup(@stateDE,"brand_state","EventID",@appStateId)
SET @CurrentFields_state = Lookup(@stateDE,"fields_state","EventID",@appStateId)
SET @sysFieldsDe = IIF(@biz_env == 'UAT','ent.system_field__obj_UAT','ent.system_field__obj')
SET @de_cb_types_list = IIF(@biz_env == 'UAT','ent.cb_types_list_uat','ent.cb_types_list_prod')
SET @sourceTable = IIF(@biz_env == 'UAT','ent.EmailObject_UAT','ent.EmailObject')
SET @de_eca = IIF(@biz_env == 'UAT','ent.email_contentBlock_areas_UAT','ent.email_contentBlock_areas')
SET @src_Secret = QueryParameter('e')
SET @sourceIdField = 'EmailName'
SET @sourceIdValue = Lookup(@sourceTable,@sourceIdField,'Secret',@src_Secret)
SET @CVBRowCount = RowCount(LookupRows(@de_eca,'EmailName',@sourceIdValue))

SET @de_e_additional_email_params = Lookup(@sourceTable,'additional_email_params','EmailName',@sourceIdValue)
SET @de_e_utmSource = Lookup(@sourceTable,'utmSource','EmailName',@sourceIdValue)
SET @de_e_utmMedium = Lookup(@sourceTable,'utmMedium','EmailName',@sourceIdValue)
SET @de_e_utmCampaign = Lookup(@sourceTable,'utmCampaign','EmailName',@sourceIdValue)
SET @de_e_utmContent = Lookup(@sourceTable,'utmContent','EmailName',@sourceIdValue)
SET @de_e_Masthead = Lookup(@sourceTable,'Masthead','EmailName',@sourceIdValue)

SET @vidoraAPIKeysDE = "ENT.Vidora_APIKeys"
SET @domain_link_masthead = Lookup(@vidoraAPIKeysDE, 'DomainLink', 'ABI_Masthead',@de_e_Masthead)

SET @src_Brand = @userCurrentBrand
SET @action_area = QueryParameter("action_area")
SET @customDropdown_obj = IIF(@biz_env == 'UAT','ent.customDropdown__obj_UAT','ent.customDropdown__obj')
SET @de_ecc = IIF(@biz_env == 'UAT','ent.email_content_cache_UAT','ent.email_content_cache')
SET @de_llm_results = IIF(@biz_env == 'UAT','ent.LLM_content_results_UAT','ent.LLM_content_results')
SET @guid = Replace(GUID(),'-','_')
IF empty(@action_area) then
  SET @action_area = "add"
ELSEIF @action_area == 'edit' THEN
  SET @block_id = QueryParameter("block_id")
  SET @emailName = QueryParameter('emailName')
  SET @de_eca_friendly_name = Lookup(@de_eca,'friendly_name','EventID',@block_id)
ENDIF
IF EMPTY(RequestParameter('event_id')) THEN
  SET @event_id = @guid
ELSE
  SET @event_id = RequestParameter('event_id')
  SET @de_friendly_name = Lookup(@de_ecc,'friendly_name','content_id',@event_id)
  SET @de_emailName = Lookup(@de_ecc,'emailName','content_id',@event_id)
  SET @de_content_name = Lookup(@de_ecc,'content_name','content_id',@event_id)

  IF @action_area == 'edit' THEN
    SET @de_eca_EventID = @event_id
  ELSE
    SET @de_appC_cc_Name = 'ai_free_form_block'
    SET @de_appC_cc_EventID = Lookup(@de_cb_types_list,'id','name','ai_free_form_block')
    SET @de_eca_EventID = @de_appC_cc_EventID
  ENDIF
  IF @action_area == 'edit' THEN
    for @var = 1 to RowCount(LookupRows(@sysFieldsDe,'Template','none','component','email_content_areas','Active','1','default_group','0')) do
      SET @deField = Field(Row(LookupRows(@sysFieldsDe,'Template','none','component','email_content_areas','Active','1','default_group','0'),@var),'field_name')
      SET @deData_type = Field(Row(LookupRows(@sysFieldsDe,'Template','none','component','email_content_areas','Active','1','default_group','0'),@var),'data_type')
      SET @deValue = Lookup(@de_eca,@deField,'EventID',@de_eca_EventID)
      TreatAsContent(CONCAT('%','%[ SET @de_eca_',@deField,' = "',@deValue,'" ]%','%'))
    next @var
    
    IF @de_eca_hide_before_date == '1/1/1900 12:00:00 AM' THEN SET @de_eca_hide_before_date = @localTime ENDIF
    IF @de_eca_hide_after_date == '1/1/1900 12:00:00 AM' THEN SET @de_eca_hide_after_date = @localTime ENDIF
    SET @de_eca_hide_before_date = Replace(Format(@de_eca_hide_before_date,'u', 'Date'),' 00:00:00Z','')
    SET @de_eca_hide_after_date = Replace(Format(@de_eca_hide_after_date,'u', 'Date'),' 00:00:00Z','')

    SET @hide_before_date_cb = IIF(@de_eca_use_hide_before_date == true,'checked','')
    SET @hide_before_date_Expired = IIF(@hide_before_date_cb == 'checked',IIF(@de_eca_hide_before_date < @localTime,'(no longer in effect)',''),'')
    SET @hide_after_date_cb = IIF(@de_eca_use_hide_after_date == true,'checked','')
    SET @hide_after_date_Expired = IIF(@hide_after_date_cb == 'checked',IIF(@de_eca_hide_after_date > @localTime,'(not in effect yet)',''),'')
    SET @de_eca_marketing_check_cb = IIF(@de_eca_marketing == true,'checked','')
    SET @de_eca_prem_check_cb = IIF(@de_eca_hide_from_prem_subs == true,'checked','')
    SET @use_more_cts_check_cb = IIF(@de_eca_use_more_cts == true,'checked','')
    SET @cb_visible = IIF(@de_eca_visible == true,'checked','')
    SET @cb_hide_status = IIF(@de_eca_visible != true,'Unhide','Hide')
    SET @cb_hide_status_opposite = IIF(@de_eca_visible == true,'Unhide','Hide')
    SET @de_appC_cc_Name = @de_eca_ContentBlock
    SET @de_eca_ref_cb_UpdatedBy = Lookup(@de_eca,'UpdatedBy','EventID',@de_eca_ref_cb_id)
    SET @firstName_ref_cb_owner = Lookup('ent.Users','FirstName','UserName',@de_eca_ref_cb_UpdatedBy)
    SET @emailAddress_ref_cb_owner = Lookup('ent.Users','EmailAddress','UserName',@de_eca_ref_cb_UpdatedBy)
    SET @de_eca_ref_cb_EmailName = Lookup(@de_eca,'EmailName','EventID',@de_eca_ref_cb_id)
    SET @de_eca_ref_cb_ContentBlock = Lookup(@de_eca,'ContentBlock','EventID',@de_eca_ref_cb_id)
    SET @de_eca_ref_cb_friendly_name = Lookup(@de_eca,'friendly_name','EventID',@de_eca_ref_cb_id)
    IF NOT EMPTY(@de_eca_ref_cb_friendly_name) THEN SET @de_eca_ref_cb_friendly_name = Concat(' | ',@de_eca_ref_cb_friendly_name) ENDIF
    SET @ref_cb_Brand = Lookup(@sourceTable,'Brand','EmailName',@de_eca_ref_cb_EmailName)
    SET @ref_cb_email_desc = Lookup(@sourceTable,'Description','EmailName',@de_eca_ref_cb_EmailName)
    IF NOT EMPTY(@ref_cb_email_desc) THEN SET @ref_cb_email_desc = Concat(' - (',@ref_cb_email_desc,')') ENDIF
    IF NOT EMPTY(Lookup(@de_eca,'ref_cb_id','EventID',@de_eca_EventID,'EmailName',@sourceIdValue)) THEN
      SET @ref_cb = 1
    ELSE
      SET @ref_cb = 0
    ENDIF
    IF EMPTY(Lookup(@de_eca,'json_data','EventID',@de_eca_EventID,'EmailName',@sourceIdValue)) OR @ref_cb == 1 THEN
      SET @do_not_show_duplicate_button = 1
    ELSE
      SET @do_not_show_duplicate_button = 0
    ENDIF
  ENDIF
ENDIF
SET @preview_section = @de_eca_EventID
]%%
<body>
  <div id="client_side_message" class="client-side-message"></div>
  <div class="main-container">
    <div class="my-3 settings-container" id="settings-container">
      <!-- Bootstrap Nav Tabs -->
      <ul class="nav nav-tabs" id="settingsTab" role="tablist">
        <li class="nav-item">
          <a class="nav-link active" id="about-tab" data-toggle="tab" href="#about" role="tab" aria-controls="about" aria-selected="true">Generate</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="ai-results-tab" data-toggle="tab" href="#ai-results" role="tab" aria-controls="ai-results" aria-selected="false" onclick="appC_ui_component('Email editor/AI editor/cmp_ai_results_list.amp','ai-results-content','','event_id=%%=v(@event_id)=%%&e=%%=v(@src_Secret)=%%&action_area=%%=v(@action_area)=%%','0','1','0','ai-results-tab','','');">AI results</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="previous-saves-tab" data-toggle="tab" href="#previous-saves" role="tab" aria-controls="previous-saves" aria-selected="false" onclick="appC_ui_component('Email editor/AI editor/cmp_content_saves_list.amp','previous-saves-content','','event_id=%%=v(@event_id)=%%&e=%%=v(@src_Secret)=%%&action_area=%%=v(@action_area)=%%','0','1','0','previous-saves-tab','','');">Recent saves</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="clipboard-tab" data-toggle="tab" href="#clipboard" role="tab" aria-controls="clipboard" aria-selected="false">Clipboard</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="content-library-tab" data-toggle="tab" href="#content-library" role="tab" aria-controls="content-library" aria-selected="false" onclick="appC_ui_component('Email editor/AI editor/cmp_shared_cb_list.amp','library-content','','action_area=add&e=%%=v(@src_Secret)=%%','0','1','0','content-library-tab','','');">Blocks in emails</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="block-settings-tab" data-toggle="tab" href="#block-settings" role="tab" aria-controls="block-settings" aria-selected="false">Settings</a>
        </li>
      </ul>
      <div class="tab-content" id="settingsTabContent">
        <!-- About Tab -->
        <div class="tab-pane fade show active" id="about" role="tabpanel" aria-labelledby="about-tab">
          <form method="POST" onsubmit="handleSubmit(event)" id="send_prompt_to_llm">
            <input type="hidden" name="submitted" value="true">
            <input type="hidden" name="goals" id="goalsHidden" value="[]">
            <input type="hidden" name="action_area" value="%%=v(@action_area)=%%">
            <input type="hidden" name="type" value="ai_free_form_block">
            <input type="hidden" name="src_secret" value="%%=v(@src_secret)=%%" />
            <input type="hidden" name="block_section" value="0" />
            %%[ IF @action_area == 'edit' THEN ]%%
              <input type="hidden" value="%%=Concat('Action:Edit//SubObjValue:',@event_id,'//displayed:1//SubObject:Content Block Settings//Value:',@sourceIdValue)=%%" name="formPost" >
              <input type="hidden" value="edit" name="action_area" >
            %%[ ELSE ]%%
              <input type="hidden" value="%%=Concat('Action:Create//Id:',@de_appC_cc_Name,'//SubObject:Content block settings//Value:',@sourceIdValue)=%%" name="formPost" >
              <input type="hidden" value="add" name="action_area" >
            %%[ ENDIF ]%%
            <input type="hidden" value="%%=v(@de_appC_cc_Name)=%%" name="cb_name" >
            <input type="hidden" name="previous_appStateId" value="%%=v(@event_id)=%%">
            <input type="hidden" name="CVBRowCount" value="%%=v(@CVBRowCount)=%%">
            <input type="hidden" name="de_eca_EventID" value="%%=v(@event_id)=%%">
            <input type="hidden" name="de_eca_PositionInEmail" value="%%=IIF(@action_area == 'edit',@de_eca_PositionInEmail,Add(@CVBRowCount,1))=%%">
            <input type="hidden" name="de_appC_cc_Name" value="%%=v(@de_appC_cc_Name)=%%">
            <input type="hidden" name="edit_eca_ContentBlock" value="ai_free_form_block">
            %%[ IF NOT EMPTY(@de_eca_ref_cb_id) THEN ]%%
              <input type="hidden" name="ref_cb" value="1">
              <input type="hidden" name="ref_cb_id" value="%%=v(@de_eca_ref_cb_id)=%%">
            %%[ ENDIF ]%%
            <input type="hidden" name="name" value="ai_free_form_block">
            <div class="progress-bar-1a2b">
              <div class="step-indicator-1a2b active" data-step="1">
                <div class="step-bubble-1a2b">1</div>
              </div>
              <div class="step-indicator-1a2b" data-step="2">
                <div class="step-bubble-1a2b">2</div>
              </div>
              <div class="step-indicator-1a2b" data-step="3">
                <div class="step-bubble-1a2b">3</div>
              </div>
            </div>
            <div class="step-panel-1a2b active" data-step="1">
              <div class="row">
                <div class="p-3 about-section">
                  <h2 class="heading-2-1a2b" style="margin-top: 0;">About the AI-Powered Free Form Block</h2>
                  <p class="paragraph-1a2b">
                    This block features an email editor with AI tools to create, edit and review content. It also features real–time editing, drag–and–drop functionality, and automatically uploads images when pasting into the editor.
                  </p>

                  <h2 class="heading-2-1a2b">Important info</h2>
                  <ul class="list-1a2b">
                    <li class="list-item-1a2b">News Corp <strong>stores user generated AI prompts</strong> for security reasons.</li>
                    <li class="list-item-1a2b">Ensure prompts are clear and specific.</li>
                    <li class="list-item-1a2b">Avoid prompts containing sensitive, security or personal information.</li>
                    <li class="list-item-1a2b">Never reuse/copy template designs from companies outside the network.
                    <li class="list-item-1a2b">Do not heavily rely on AI for copy — you've got this.</li>
                    <li class="list-item-1a2b">Always double–check your inputs before submitting a prompt!</li>
                  </ul>

                  <h2 class="heading-2-1a2b">Features</h2>

                  <h3 class="heading-2-1a2b">1. AI-Powered Content Creation</h3>
                  <ul class="list-1a2b">
                    <li class="list-item-1a2b"><strong>Smart Content Suggestions:</strong> You can leverage AI to help generate content ideas and text for your email blocks.</li>
                    <li class="list-item-1a2b"><strong>Customizable Prompts:</strong> You can guide the AI by providing specific instructions (prompts) to tailor the output.</li>
                    <li class="list-item-1a2b"><strong>Chain Prompt Generation:</strong> You can choose pre-defined goals (like brand consistency or beautify) to help the AI create content that aligns with your objectives.</li>
                    <li class="list-item-1a2b"><strong>Model Selection:</strong> Select different AI models to generate content.</li>
                    <li class="list-item-1a2b"><strong>Precision vs Creativity:</strong> You can adjust how creative or precise the content the AI generates.</li>
                    <li class="list-item-1a2b"><strong>Iterative Refinement:</strong> Work with the AI through multiple rounds to refine and perfect your content.</li>
                    <li class="list-item-1a2b"><strong>Multiple Output Options:</strong> Generate various content versions and choose the one that best fits your needs.</li>
                  </ul>

                  <h3 class="heading-2-1a2b">2. Rich-Text Editing with TinyMCE</h3>
                  <ul class="list-1a2b">
                    <li class="list-item-1a2b"><strong>Familiar Text Editing:</strong> The editor includes TinyMCE, which provides a user-friendly interface similar to other word processors, for easy text formatting.</li>
                    <li class="list-item-1a2b"><strong>Direct Content Manipulation:</strong> You can directly edit text, add links, insert images, and style your content right within the editor.</li>
                    <li class="list-item-1a2b"><strong>Style Options:</strong> You can change fonts, colors, text sizes, alignment, and other text properties.</li>
                    <li class="list-item-1a2b"><strong>Table and list creation:</strong> Add and edit tables and bullet point lists.</li>
                    <li class="list-item-1a2b"><strong>Image handling:</strong> Drag and drop images into the editor and copy and paste.</li>
                    <li class="list-item-1a2b"><strong>Auto-Image Uploads:</strong> Images are automatically uploaded when pasted into the editor, streamlining the content creation process.</li>
                    <li class="list-item-1a2b"><strong>Personalization Strings:</strong> Insert dynamic personalization tokens that will be replaced with recipient-specific information when emails are sent.</li>
                  </ul>

                  <h3 class="heading-2-1a2b">3. Content Preview and Interactive Design</h3>
                  <ul class="list-1a2b">
                    <li class="list-item-1a2b"><strong>Real-Time Preview:</strong> You'll see how your email block looks as you design it.</li>
                    <li class="list-item-1a2b"><strong>Drag-and-Drop:</strong> You can easily rearrange elements in your content by simply dragging and dropping them.</li>
                    <li class="list-item-1a2b"><strong>Responsive Design Preview:</strong> Check how your block appears on both desktop and mobile screens.</li>
                    <li class="list-item-1a2b"><strong>Live Edits:</strong> As you make changes in the rich-text editor or rearrange elements, the content preview updates instantly.</li>
                    <li class="list-item-1a2b"><strong>Resize:</strong> Resize the elements with a handy width field.</li>
                    <li class="list-item-1a2b"><strong>Border radius:</strong> Add or edit rounded corners to elements.</li>
                    <li class="list-item-1a2b"><strong>Set Visibility:</strong> Set visibility rules to set block position, hide from specific people or on a time.</li>
                    <li class="list-item-1a2b"><strong>Duplicate:</strong> Duplicate elements in the editor.</li>
                    <li class="list-item-1a2b"><strong>Delete:</strong> Delete elements in the editor.</li>
                  </ul>
                </div>
              </div>
              <div class="navigation-buttons-1a2b">
                <button type="button" class="nav-button-1a2b next-button-1a 2b AI_create_and_edit" id="nextButtonScreen1" disabled>Click here to get started</button>
                <span id="nextButtonExplanation" class="nav-button-explanation"></span>
              </div>
            </div>
            <div class="step-panel-1a2b" data-step="2" style="margin-bottom: 4vw;">
              <div class="row">
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-body card-body-no-padding">
                      <h5 class="heading-2-1a2b heading-font-size" id="promptHeading" style="display:inline-block;">Provide or </h5>
                      <div class="ai-highlight-wrapper" data-ai-highlight="true" style="position: relative;display: inline-block;outline: transparent solid 5px;outline-offset: -5px;box-shadow: rgba(255, 0, 0, 0.7) 0px 0px 5px 1px, rgba(0, 255, 255, 0.5) 0px 0px 10px 3px;background-image: linear-gradient(90deg, rgb(255, 0, 0), rgb(255, 103, 0), rgb(255, 146, 0), rgb(255, 220, 0), rgb(220, 255, 0), rgb(189, 255, 0), rgb(48, 255, 0), rgb(0, 255, 207), rgb(0, 235, 255), rgb(0, 143, 255), rgb(0, 68, 255), rgb(143, 0, 255), rgb(196, 0, 255), rgb(255, 0, 169), rgb(255, 0, 0));background-clip: padding-box;background-origin: padding-box;background-size: 400% 400%;background-position: 0% 50%;animation: 30s linear 0s infinite normal none running aiGradientBorder;border-radius: .5vw;margin-left: .5vw;"><button type="button" id="NewPromptBtn" class="nav-button-1a2b text-button-1a2b" style="display:inline-block;border-radius: .5vw;">
                        Generate a prompt ✨ 
                      </button></div>
                      <div id="from_sentence" style="margin-top: .7vw;">
                        <textarea name="fromSentence" maxlength="4000" class="form-select-1a2b form-textarea" placeholder="Type in your prompt here or use AI to generate a few prompt suggestions..."></textarea>
                      </div>
                    </div>
                  </div>
                  <div class="card mb-3">
                    <div class="card-body card-body-left-padding-only">
                      <h5 class="heading-2-1a2b heading-font-size" style="margin-top: -.6vw;margin-bottom: .5vw;">Content Layout &amp; Style </h5>
                      <div class="form-group columns-rows-fields">
                        <div class="d-flex align-items-center">
                          <div class="mr-3">
                            <label class="form-label-1a2b">Columns:</label>
                            <input type="number" name="columns" class="form-input-1a2b full-width-input" min="1" value="2" style="padding-bottom: .1vw;margin-bottom: .3vw;" />
                          </div>
                          <div class="ml-3">
                            <label class="form-label-1a2b">Rows:</label>
                            <input type="number" name="rows" class="form-input-1a2b full-width-input" min="1" value="5" style="padding-bottom: .1vw;margin-bottom: .3vw;" />
                          </div>
                        </div>
                      </div>
                      <div class="form-group">
                        <label class="form-label-1a2b">Brand Style</label>
                        <select name="brandCssStyle" class="form-select-1a2b custom-select" style="width: 68%;">
                          <option selected value="">Optional</option>
                          <option value='{"header":{"font-family":"Times Classic Bold, Times New Roman, Times, serif","color":"#237D9B","font-weight":"bold","font-size":"28px"},"highlight":{"color":"#ed5843"},"paragraph":{"color":"#333333","font-weight":"normal","font-size":"20px"},"link":{"color":"#237D9B","text-decoration":"none"},"border":{"color":"#237D9B","width":"6px","style":"solid"},"background-color":"#F5F5F5","embedded_element":{"header":{"color":"#00405c","font-weight":"bold","font-size":"24px"},"paragraph":{"color":"#333333","font-weight":"normal","font-size":"20px"},"link":{"color":"#237D9B"},"border":{"color":"#237D9B","width":"4px"},"background-color":"#FFFFFF","embedded_element":{"header":{"color":"#ed5843","font-weight":"bold","font-size":"20px"},"paragraph":{"color":"#FFFFFF","font-weight":"normal","font-size":"16px"},"link":{"color":"#ed5843"},"border":{"color":"#237D9B","width":"2px"},"background-color":"#00405c"}}}'>The Australian</option>
                          <option value='{"header":{"font-family":"Georgia, serif","color":"#202223","font-weight":"normal","line-height":"1.29","font-size":"32px"},"highlight":{"font-family":"Georgia, serif","color":"#ed5843","font-weight":"normal","line-height":"1.29","font-size":"32px"},"paragraph":{"font-family":"Arial, sans-serif","color":"#606669","font-weight":"normal","line-height":"1.29","font-size":"14px"},"link":{"font-family":"Arial, sans-serif","color":"#A1A6A9","text-decoration":"none"},"border":{"color":"#E0E1E2","width":"1px","style":"solid"},"background-color":"#F7F7F7","embedded_element":{"background-color":"#F7F0E6","embedded_element":{"header":{"line-height":"1.2","color":"#000000"},"paragraph":{"font-family":"Georgia, serif","font-size":"18px","color":"#202223","line-height":"1.28"},"background-color":"#FFFFFF"}}}'>Body and Soul</option>
                          <option value='{"outer_template_1":{"max-width":"650px","borders":{"top":{"color":"#606669","style":"solid","width":"0px"},"right":{"color":"#606669","style":"solid","width":"0px"},"bottom":{"color":"#606669","style":"solid","width":"0px"},"left":{"color":"#606669","style":"solid","width":"0px"}},"background-color":"#F5F5F5","border-radius":"0px","padding":"0px"},"inner_template_1":{"borders":{"top":{"color":"#000000","style":"none","width":"0px"},"right":{"color":"#000000","style":"none","width":"0px"},"bottom":{"color":"#000000","style":"none","width":"0px"},"left":{"color":"#000000","style":"none","width":"0px"}},"background-color":"#FFFFFF","border-radius":"0px","padding":"0px"},"free_text_block_1":{"margin":{"top":"0px","right":"0px","bottom":"0px","left":"0px"},"border":"0px none","overflow":"hidden","border-radius":"0px","padding":{"top":"32px","right":"17.5px","bottom":"0px","left":"17.5px"},"background-color":"#F5F5F5"}}'>Metros</option>
                          <option value='{
                            "mainStyles": {
                              "header": {"font-family":"Times Classic Bold, Times New Roman, Times, serif", "color":"#237D9B", "font-weight":"bold", "font-size":"28px"},
                              "highlight": {"color":"#ed5843"},
                              "paragraph": {"color":"#333333", "font-weight":"normal", "font-size":"20px"},
                              "link": {"color":"#237D9B", "text-decoration":"none"},
                              "border": {"color":"#237D9B", "width":"6px", "style":"solid"},
                              "background-color": "#F5F5F5"
                            },
                            "firstLevelContent": {
                              "header": {"font-family":"Times Classic Bold, Times New Roman, Times, serif", "color":"#00405c", "font-weight":"bold", "font-size":"24px"},
                              "paragraph": {"color":"#333333", "font-weight":"normal", "font-size":"20px"},
                              "link": {"color":"#237D9B"},
                              "border": {"color":"#237D9B", "width":"4px"},
                              "background-color": "#FFFFFF"
                            },
                            "secondLevelContent": {
                              "header": {"font-family":"Times Classic Bold, Times New Roman, Times, serif", "color":"#ed5843", "font-weight":"bold", "font-size":"20px"},
                              "paragraph": {"color":"#FFFFFF", "font-weight":"normal", "font-size":"16px"},
                              "link": {"color":"#ed5843"},
                              "border": {"color":"#237D9B", "width":"2px"},
                              "background-color": "#00405c"
                            }
                          }'>The Australian 2</option>
                          <option value='{
                            "mainStyles": {
                              "header": {"font-family":"Georgia, serif", "color":"#202223", "font-weight":"normal", "line-height":"1.29", "font-size":"32px"},
                              "highlight": {"font-family":"Georgia, serif", "color":"#ed5843", "font-weight":"normal", "line-height":"1.29", "font-size":"32px"},
                              "paragraph": {"font-family":"Arial, sans-serif", "color":"#606669", "font-weight":"normal", "line-height":"1.29", "font-size":"14px"},
                              "link": {"font-family":"Arial, sans-serif", "color":"#A1A6A9", "text-decoration":"none"},
                              "border": {"color":"#E0E1E2", "width":"1px", "style":"solid"},
                              "background-color": "#F7F7F7"
                            },
                            "firstLevelContent": {
                              "background-color": "#F7F0E6"
                              "header": {"font-family":"Georgia, serif", "color":"#202223", "font-weight":"normal", "line-height":"1.29", "font-size":"32px"},
                              "highlight": {"font-family":"Georgia, serif", "color":"#ed5843", "font-weight":"normal", "line-height":"1.29", "font-size":"32px"},
                              "paragraph": {"font-family":"Arial, sans-serif", "color":"#606669", "font-weight":"normal", "line-height":"1.29", "font-size":"14px"},
                            },
                            "secondLevelContent": {
                              "header": {"font-family":"Georgia, serif", "font-size":"24px", "line-height":"1.2", "color":"#000000"},
                              "paragraph": {"font-family":"Georgia, serif", "font-size":"18px", "color":"#202223", "line-height":"1.28"},
                              "background-color": "#FFFFFF"
                            }
                          }'>Body and Soul 2</option>
                          <option value='{
                            "mainStyles": {
                              "header": {"font-family":"Georgia, sans-serif", "color":"#007BFF", "font-weight":"bold", "font-size":"24px"},
                              "highlight": {"color":"#606669"},
                              "paragraph": {"color":"#000000", "font-weight":"bold", "font-size":"22px"},
                              "link": {"color":"#606669", "text-decoration":"none"},
                              "border": {"color":"#E0E1E2", "width":"0px", "style":"solid"},
                              "background-color": "#F5F5F5"
                            },
                            "firstLevelContent": {
                              "header": {"color":"#FFFFFF", "font-weight":"normal", "font-size":"20px"},
                              "font-family":"Roboto,Open sans,Helvetica,Arial,sans-serif",
                              "paragraph": {"color":"#FFFFFF", "font-weight":"normal", "font-size":"18px"},
                              "link": {"color":"#FFFFFF"},
                              "border": {"color":"#E0E1E2", "width":"0px"},
                              "background-color": "#007bff"
                            },
                            "secondLevelContent": {
                              "header": {"color":"#333333", "font-weight":"normal", "font-size":"18"},
                              "paragraph": {"color":"#606669", "font-weight":"normal", "font-size":"16px"},
                              "link": {"color":"#606669"},
                              "border": {"color":"#E0E1E2", "width":"0px"},
                              "background-color": "#F5F5F5"
                            }
                          }'>Metros 2</option>
                          <option value='{"header":{"font-family":"Arial, sans-serif","color":"#007BFF","font-weight":"bold","line-height":"1.2","font-size":"24px"},"paragraph":{"font-family":"Arial, sans-serif","color":"#555","font-weight":"normal","line-height":"1.5","font-size":"16px"},"link":{"font-family":"Arial, sans-serif","color":"#0070d2","text-decoration":"underline"}}'>Classic</option>
                          <option value='{"header":{"font-family":"Georgia, serif","color":"#222","font-weight":"bold","line-height":"1.3","font-size":"26px"},"paragraph":{"font-family":"Georgia, serif","color":"#444","font-weight":"normal","line-height":"1.6","font-size":"18px"},"link":{"font-family":"Georgia, serif","color":"#006699","text-decoration":"none"}}'>Elegant</option>
                          <option value='{"header":{"font-family":"Helvetica, sans-serif","color":"#111","font-weight":"700","line-height":"1.1","font-size":"22px"},"paragraph":{"font-family":"Helvetica, sans-serif","color":"#333","font-weight":"400","line-height":"1.4","font-size":"15px"},"link":{"font-family":"Helvetica, sans-serif","color":"#0088cc","text-decoration":"underline"}}'>Modern</option>
                        </select>
                      </div>
                      <div class="form-check form-check-inline goal-inline" style="margin-top:.5vw;">
                        <input type="checkbox" class="form-check-input" id="stack_content_vertical" checked name="stack_content_vertical" value="true">
                        <label class="form-check-label" for="stack_content_vertical">Stack columns vertically on mobile</label>
                      </div>
                      <div class="form-check form-check-inline goal-inline" style="margin-top:.5vw;">
                        <input type="checkbox" class="form-check-input" id="extra_spacing_in_elements" checked name="extra_spacing_in_elements" value="true">
                        <label class="form-check-label" for="extra_spacing_in_elements">Apply extra spacing between elements</label>
                      </div>
                      <!--
                      <div class="form-group">
                        <div class="d-flex align-items-center">
                          <div class="mr-3" style="flex: 0 0 48%;">
                            <label class="form-label-1a2b">Reader Level</label>
                            <select name="readerLevel" class="form-select-1a2b custom-select" style="width: 100%;">
                              <option selected value="">Optional</option>
                              <option value="beginner">Beginner – Simple and clear</option>
                              <option value="intermediate">Intermediate – Balanced complexity</option>
                              <option value="advanced">Advanced – Technical and detailed</option>
                              <option value="expert">Expert – Industry–specific</option>
                            </select>
                          </div>
                          <div style="flex: 0 0 48%;">
                            <label class="form-label-1a2b">Writing Style</label>
                            <select name="writingStyle" class="form-select-1a2b custom-select" style="width: 100%;">
                              <option selected value="">Optional</option>
                              <option value="friendly">Friendly and Approachable</option>
                              <option value="professional">Professional and Business–like</option>
                              <option value="enthusiastic">Enthusiastic and Energetic</option>
                              <option value="authoritative">Authoritative and Expert</option>
                              <option value="empathetic">Empathetic and Understanding</option>
                            </select>
                          </div>
                        </div>
                      </div>
                      -->
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card mb-3">
                    <div class="card-body card-body-right-padding-only">
                      <h5 class="heading-2-1a2b heading-font-size">Select Content Source</h5>
                      <div class="form-group mb1vw">
                        <label class="form-label-1a2b form-label-margin">
                          <input type="radio" class="radio-input-1a2b radio-input-margin" name="content-source" value="none" checked/>
                          New
                        </label>
                        <label class="form-label-1a2b form-label-margin" style="display:none;">
                          <input type="radio" class="radio-input-1a2b radio-input-margin" name="content-source" value="capi"/>
                          CAPI
                        </label>
                        <label class="form-label-1a2b form-label-margin" style="display:none;">
                          <input type="radio" class="radio-input-1a2b radio-input-margin" name="content-source" value="existing"/>
                          Saved
                        </label>
                        <label class="form-label-1a2b form-label-margin">
                          <input type="radio" class="radio-input-1a2b radio-input-margin" name="content-source" disabled value="selection"/>
                          Content Preview
                        </label>
                      </div>

                       <!-- CAPI Selection with Search -->
                      <div id="capiSelection" style="display: none;">
                        <div class="searchable-dropdown-container" style="position: relative; margin-top: 1vw;">
                          <input type="text" class="form-control search-input" placeholder="Search CAPI collections..." style="margin-bottom: 0.5vw;">
                          <div class="dropdown-options-container" style="display: none; position: absolute; width: 100%; max-height: 200px; overflow-y: auto; z-index: 1000; background: white; border: 1px solid #ced4da; border-radius: 0.25rem;">
                            <!-- Options will be dynamically populated here -->
                          </div>
                          <select class="form-select-1a2b custom-select" id="capi_id_dropdown" style="display: none;" name="capi_id" >
                            <option value="">-- Select a collection Id --</option>
                            
                          </select>
                        </div>
                      </div>

                      <!-- Existing Block Selection with Search -->
                      <div id="existingBlockSelection" style="display: none;">
                        <div class="searchable-dropdown-container" style="position: relative; margin-top: 1vw;">
                          <input type="text" class="form-control search-input" placeholder="Search blocks..." style="margin-bottom: 0.5vw;">
                          <div class="dropdown-options-container" style="display: none; position: absolute; width: 100%; max-height: 200px; overflow-y: auto; z-index: 1000; background: white; border: 1px solid #ced4da; border-radius: 0.25rem;">
                            <!-- Options will be dynamically populated here -->
                          </div>
                          <select id="eventSelect-1a2b" name="cb_id" class="form-select-1a2b custom-select" id="cb_id_dropdown" style="display: none;">
                            <option disabled selected value="">-- Select a block --</option>
                            
                          </select>
                        </div>
                      </div>

                    </div>
                  </div>
                  <div class="card mb-3">
                    <div class="card-body card-body-no-margin">
                      <h5 class="heading-2-1a2b heading-font-size text-center-align">◄ Precision vs Creativity ►</h5>
                      <div class="slider-container">
                        <input type="range" name="creativityLevel" id="creativityLevel" class="slider-1a2b" min="0" max="100" value="70">
                      </div>
                    </div>
                  </div>
                  <div class="card mb-3 card-margin-top-0">
                    <h5 class="heading-2-1a2b heading-font-size goal-heading-margin">Chain Your Prompt For More Results</h5>
                    <div class="subText2" style="margin-top:-1vw;padding-left:1vw;font-style:italic;font-size: .7vw;line-height: 1.8;background-color: #ffffff;border-bottom:1px solid #dcd8d8;">
                      Chain your prompt to get more email templates. Each box creates new version. Don't overdue it, as it takes longer to complete everything.
                    </div>
                    <div class="card-body goal-settings-container">
                      <div class="form-check form-check-inline goal-inline">
                        <input type="checkbox" class="form-check-input goal-checkbox" id="drive_clicks" value="web_traffic">
                        <label class="form-check-label" for="drive_clicks">Drive Web Traffic</label>
                      </div>
                      <div class="form-check form-check-inline goal-inline">
                        <input type="checkbox" class="form-check-input goal-checkbox" id="excl_nsl_content" value="excl_content">
                        <label class="form-check-label" for="excl_nsl_content">Drive Newsletter Value</label>
                      </div>
                      <div class="form-check form-check-inline goal-inline">
                        <input type="checkbox" class="form-check-input goal-checkbox" id="promote" value="marketing">
                        <label class="form-check-label" for="promote">Promote Products</label>
                      </div>
                      <div class="form-check form-check-inline goal-inline">
                        <input type="checkbox" class="form-check-input goal-checkbox" id="fix_outlook" value="fix_outlook">
                        <label class="form-check-label" for="fix_outlook">Fix Outlook Rendering Issues</label>
                      </div>
                      <div class="form-check form-check-inline goal-inline">
                        <input type="checkbox" class="form-check-input goal-checkbox" id="reduce_size" value="reduce_size">
                        <label class="form-check-label" for="reduce_size">Reduce Email Content Size</label>
                      </div>
                      <div class="form-check form-check-inline goal-inline">
                        <input type="checkbox" class="form-check-input goal-checkbox" id="optimise_for_esps" value="optimise_for_esps">
                        <label class="form-check-label" for="optimise_for_esps">Optimise for All Email Clients</label>
                      </div>
                      <div class="form-check form-check-inline goal-inline">
                        <input type="checkbox" class="form-check-input goal-checkbox" id="beautify" value="beautify">
                        <label class="form-check-label" for="beautify">Beautify</label>
                      </div>
                      <div class="form-check form-check-inline goal-inline">
                        <input type="checkbox" class="form-check-input goal-checkbox" id="responsive" value="responsive">
                        <label class="form-check-label" for="responsive">Mobile responsiveness</label>
                      </div>
                      <div class="form-check form-check-inline goal-inline">
                        <input type="checkbox" class="form-check-input goal-checkbox" id="summarise" value="summarise">
                        <label class="form-check-label" for="summarise">Summarise</label>
                      </div>
                      <div class="form-check form-check-inline goal-inline" id="brand-consistency-container" style="display: none !important;">
                        <input type="checkbox" class="form-check-input goal-checkbox" id="on_brand" value="on_brand">
                        <label class="form-check-label" for="on_brand">Ensure Brand Consistency</label>
                      </div>
                      <div class="form-check form-check-inline goal-inline">
                        <input type="checkbox" class="form-check-input goal-checkbox" id="goal_other" value="other">
                        <label class="form-check-label" for="other">Other</label>
                      </div>
                      <div id="otherGoalContainer" class="other-goal-container">
                        <label class="form-label-1a2b label-bottom-margin">Other Goals</label>
                        <textarea name="otherGoalDescription" class="form-select-1a2b textarea-bottom-margin"></textarea>
                      </div>
                    </div>
                  </div>
                  <div class="card mb-3">
                    <div class="card-body card-body-right-padding-only">
                      <h5 class="heading-2-1a2b heading-font-size">Model Selection</h5>
                      <div class="form-group">
                        <select name="aiModel" class="form-select-1a2b custom-select" style="font-size:.85vw;">
                          <option value="claude-3-5-haiku@20241022">Claude 3.5 Haiku (fastest but least accurate)</option>
                          <option value="claude-3-5-sonnet@20240620">Claude 3.5 Sonnet (slower but more accurate)</option>
                          <option selected value="claude-3-5-sonnet-v2@20241022">Claude 3.5 V2 Sonnet (balanced)</option>
                          <option value="claude-3-7-sonnet@20250219">Claude 3.7 Sonnet (slowest but most accurate)</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="navigation-buttons-1a2b mt-3 navigation-buttons-mt">
                <button type="button" class="nav-button-1a2b back-button-1a2b text-button-1a2b">Back</button>
                <button type="button" id="generateBlockBtn" class="nav-button-1a2b text-button-1a2b AI_create_and_edit-1a2b">
                  Generate content <span>✨</span>
                </button>
                <button type="button" id="goToResultBtn" class="nav-button-1a2b" style="display:none;">Go to Result</button>
                <span id="submitButtonExplanation" style="font-size:0.9vw; color:#a00; margin-left:1vw;margin-top: .5vw;"></span>
              </div>
            </div>

            <!-- New Step 3: Content Preview -->
            <div class="step-panel-1a2b" data-step="3">
              <div class="card">
                <div class="card">
                  <div class="card-body preview-card-body">
                    <div id="content-preview-container" class="content-preview-container">
                      <!-- Content will be loaded here -->
                    </div>
                    <div id="error-message-container" class="error-message-container"></div>
                  </div>
                </div>
              </div>
              <div class="navigation-buttons-1a2b mt-3 navigation-buttons-mt">
                <button type="button" class="nav-button-1a2b back-button-1a2b text-button-1a2b ">Back</button>
                <!--
                <button type="button" id="save_block_and_exit" class="nav-button-1a2b text-button-1a2b .btn-primary">
                  Save as content
                </button>
                -->
                <button type="button" id="move_cb_to_editor" class="nav-button-1a2b text-button-1a2b btn-primary" style="display:none;">
                  Full Content Overwrite
                </button>
                <button type="button" id="replace_selection_step3" class="nav-button-1a2b text-button-1a2b btn-primary" style="display:none;" onclick="replace_selected_content_from_step3();">
                  Replace Selection
                </button>
                <button type="button" id="append_in_editor" class="nav-button-1a2b text-button-1a2b btn-primary">
                  Add to bottom
                </button>
                <button type="button" id="copy_content_step3" class="nav-button-1a2b text-button-1a2b" onclick="copy_content_from_step3();">
                  Copy Content
                </button>
                <button type="button" id="add_to_clipboard" class="nav-button-1a2b text-button-1a2b">
                  Copy to clipboard
                </button>
              </div>
            </div>
            %%[ if @action_area == "edit" and not empty(@block_id) then ]%%
              <input type="hidden" name="block_id" value="%%=v(@block_id)=%%">
              <input type="hidden" name="emailName" value="%%=v(@emailName)=%%">
              <input type="hidden" name="friendly_name" value="%%=v(@de_eca_friendly_name)=%%">
            %%[ endif ]%%
          </form>
          <!-- JSON Code View & Prompt Builder -->
          <div class="output-container" style="display: block;">
            <!--
            <div id="json-code-view">
              <code id="json-code"></code>
            </div>
            -->
            <div id="prompt-request" style="display:none;">
              <strong>Prompt Builder:</strong>
              <pre id="prompt-request-content"></pre>
            </div>
          </div>
        </div>
        <!-- Block Settings Tab -->
        <div class="tab-pane fade" id="block-settings" role="tabpanel" aria-labelledby="block-settings-tab" style="padding-bottom: 4.8vw;">
          <div id="block-settings-content" style="overflow: auto; height: 100%;">
            <div class="p-3">
              <div class="card-body">
                <!-- Block Settings heading removed -->
                <div class="form-group" style="display: inline-flex;margin-bottom: 0;">
                  <label class="d-block">Position in email</label>
                  <div style="margin-right: .5vw;">
                    <select class="question2" name="PositionInEmail" id="PositionInEmail">
                      <option value="%%=IIF(@action_area == 'edit',@de_eca_PositionInEmail,Add(@CVBRowCount,1))=%%" selected>%%=IIF(@action_area == 'edit',@de_eca_PositionInEmail,Add(@CVBRowCount,1))=%%</option>
                      %%[ IF @action_area == 'edit' THEN ]%%
                      %%[ for @cbp = 1 to @CVBRowCount do IF @cbp != @de_eca_PositionInEmail THEN ]%%<option value="%%=v(@cbp)=%%">%%=v(@cbp)=%%</option>%%[ ENDIF next @cbp ]%%
                      %%[ ELSE ]%%
                      %%[ for @cbp = 1 to @CVBRowCount do ]%%<option value="%%=v(@cbp)=%%">%%=v(@cbp)=%%</option>%%[ next @cbp ]%%
                      %%[ ENDIF ]%%
                    </select>
                  </div>
                  <div class="subText2">
                    Select which content block position you want this content block to move to.
                  </div>
                </div>

                <div class="form-group">
                  <h5 class="heading-2-1a2b heading-font-size">URL Parameter Settings</h5>
                  <div class="form-check mb-2">
                    <input type="checkbox" class="form-check-input sync-field" %%=IIF(@de_eca_disable_utm_appending == true,'checked','')=%% name="disable_utm_appending" value="1" />
                    <label class="form-check-label" for="disable_utm_appending">Disable UTM appending</label>
                    <div class="subText2">
                      UTM appending is enabled by default. Check this box to disable it.
                    </div>
                  </div>

                  <!-- URL Parameter Exclusion Section -->
                  <div class="form-group" id="url_parameter_exclusion_section" style="display: none;">
                    <label class="form-check-label">Exclude URL parameters from links on save (one-off removal)</label>
                    <textarea class="form-control sync-field" name="exclude_url_parameters"id="exclude_url_parameters" rows="3"
                      placeholder="Enter parameter names to exclude, e.g.: utm_campaign, custom_param, tracking_id"
                      style="font-family: monospace; font-size: 12px;"
                    >%%=v(@edit_eca_exclude_url_parameters)=%%</textarea>
                    <div class="subText2 mt-2">
                      Enter parameter names (separated by commas) that should be removed from all links.
                      Both the parameter names and their values will be excluded from URLs.
                      <br><small style="color: #6c757d;">Example: <code>utm_campaign, custom_param, tracking_id</code></small>
                    </div>
                  </div>

                  <div class="form-check mb-2">
                    <input type="checkbox" class="form-check-input sync-field" %%=IIF(@de_eca_auto_syndicate_domain_link == true,'checked','')=%% name="auto_syndicate_domain_link" value="0" />
                    <label class="form-check-label" for="auto_syndicate_domain_link">Auto-syndicate masthead domain link</label>
                    <div class="subText2">
                      Check this box to replace any references of the current email's masthead domain link with the [[[SiteLink]]] dynamic field. This feature is useful when your content block is referenced by other emails used by other brands for cross-site syndicated articles so that you do not have to manually replace the domain link in the URL fields inside the content.
                    </div>
                  </div>

                </div>

                <div class="form-group">
                  <h5 class="heading-2-1a2b heading-font-size">Block Visibility Settings</h5>
                  <div class="form-check mb-2">
                    <input type="checkbox" class="form-check-input sync-field" %%=v(@de_eca_marketing_check_cb)=%% name="marketing" value="1" id="marketing_settings" />
                    <label class="form-check-label" for="marketing_settings">Only show block to marketing-opted-in customers</label>
                  </div>
                  <div class="form-check mb-2">
                    <input type="checkbox" class="form-check-input sync-field" %%=v(@de_eca_prem_check_cb)=%% name="hide_from_prem_subs" value="1" id="hide_from_prem_subs" />
                    <label class="form-check-label" for="hide_from_prem_subs">Always hide block from premium customers</label>
                  </div>
                  <div class="form-check mb-2">
                    <input type="checkbox" class="form-check-input sync-field" %%=v(@hide_after_date_cb)=%% name="use_hide_after_date" value="1" id="hide_after_date_checkbox" onclick="show_or_hide_checkbox('hide_after_date_checkbox','hide_after_date_field');" />
                    <label class="form-check-label" for="hide_after_date_checkbox">Set a "hide-block-from" date</label>
                  </div>
                  <div style="display:none;" id="hide_after_date_field" class="hide-date-field">
                    <input type="date" class="form-control sync-field date-input-width" name="hide_after_date" value="%%=v(@de_eca_hide_after_date)=%%" />
                  </div>
                  <div class="form-check mb-2">
                    <input type="checkbox" class="form-check-input sync-field" %%=v(@hide_before_date_cb)=%% name="use_hide_before_date" value="1" id="hide_before_date_checkbox" onclick="show_or_hide_checkbox('hide_before_date_checkbox','hide_before_date_field');" />
                    <label class="form-check-label" for="hide_before_date_checkbox">Set a "hide-block-until" date</label>
                  </div>
                  <div style="display:none;" id="hide_before_date_field" class="hide-date-field">
                    <input type="date" class="form-control sync-field date-input-width" name="hide_before_date" value="%%=v(@de_eca_hide_before_date)=%%" />
                  </div>
                </div>
                <div class="form-group">
                  <label class="form-check-label">Always hide on certain days of the week</label>
                  <div>
                    <div class="form-check form-check-inline">
                      <input type="checkbox" class="form-check-input day-checkbox sync-field" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Monday') > 0,'checked','')=%% value="Monday" id="day_Monday" name="hide_set_of_days">
                      <label class="form-check-label" for="day_Monday">Mon</label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input type="checkbox" class="form-check-input day-checkbox sync-field" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Tuesday') > 0,'checked','')=%% value="Tuesday" id="day_Tuesday" name="hide_set_of_days">
                      <label class="form-check-label" for="day_Tuesday">Tue</label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input type="checkbox" class="form-check-input day-checkbox sync-field" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Wednesday') > 0,'checked','')=%% value="Wednesday" id="day_Wednesday" name="hide_set_of_days">
                      <label class="form-check-label" for="day_Wednesday">Wed</label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input type="checkbox" class="form-check-input day-checkbox sync-field" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Thursday') > 0,'checked','')=%% value="Thursday" id="day_Thursday" name="hide_set_of_days">
                      <label class="form-check-label" for="day_Thursday">Thu</label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input type="checkbox" class="form-check-input day-checkbox sync-field" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Friday') > 0,'checked','')=%% value="Friday" id="day_Friday" name="hide_set_of_days">
                      <label class="form-check-label" for="day_Friday">Fri</label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input type="checkbox" class="form-check-input day-checkbox sync-field" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Saturday') > 0,'checked','')=%% value="Saturday" id="day_Saturday" name="hide_set_of_days">
                      <label class="form-check-label" for="day_Saturday">Sat</label>
                    </div>
                    <div class="form-check form-check-inline">
                      <input type="checkbox" class="form-check-input day-checkbox sync-field" %%=IIF(IndexOf(@de_eca_hide_set_of_days,'Sunday') > 0,'checked','')=%% value="Sunday" id="day_Sunday" name="hide_set_of_days">
                      <label class="form-check-label" for="day_Sunday">Sun</label>
                    </div>
                  </div>
                  <input type="hidden" name="hide_set_of_days" id="hide_set_of_days_hidden" value="%%=v(@de_eca_hide_set_of_days)=%%">
                </div>
              </div>
            </div>
          </div>
          <div style="padding-top: 1vw;padding-bottom:1vw;position: fixed;bottom: 0;border-top: 1px solid #dbdbdb;width: 48%;background-color: white; z-index: 1;" id="settings_save_actions%%=v(@event_id)=%%">
            <div style="display: flex;gap: 0.5vw;bottom: .5vw;position: relative;padding-top: 1vw;">
              <div style="font-style: italic;color: grey;font-size: 1vw;">Click on the Save button in the bottom right corner to save your changes.</div>
            </div>
          </div>
        </div>
        <!-- Clipboard Tab -->
        <div class="tab-pane fade" id="clipboard" role="tabpanel" aria-labelledby="clipboard-tab">
          <div class="related_fields" style="padding:0!important;margin-bottom:0.8vw;border: 1px solid #dee2e6;border-radius: var(--border-radius, 0.42vw);overflow: hidden;margin-top:1vw;">
            <div class="assetCard">
              <div class="card-header">
                <h5 class="mb-0">
                  <div class="assetRowContainer" style="padding:0px;background-color: #f8f9fa;">
                    <table style="width:100%;border-collapse:separate;border-spacing:0;">
                      <thead>
                        <tr>
                          <th style="width:25.5%;font-size:0.9vw;font-weight:600;padding-left: .4vw;text-align:left;border-bottom:1px solid #dee2e6;height: 3vw;padding-bottom: 1vw;">Added date</th>
                          <th style="width:55%;font-size:0.9vw;font-weight:600;padding-left: .4vw;text-align:left;border-bottom:1px solid #dee2e6;height: 3vw;padding-bottom: 1vw;">Clipboard content</th>
                          <th style="width:19.5%;font-size:0.9vw;font-weight:600;text-align:center;border-bottom:1px solid #dee2e6;height: 3vw;padding-bottom: 1vw;">Actions</th>
                        </tr>
                      </thead>
                    </table>
                  </div>
                </h5>
              </div>
            </div>
            <div class="list_view_table_row_container" style="max-height:8vw!important;margin-bottom:0!important;overflow-y:auto;" id="clipboard_list">
              <!-- Clipboard items will be dynamically added here -->
              <div id="no_clipboard_items" class="retrieve">
                <div class="assetCard">
                  <h5 class="mb-0">
                  <div class="assetRowContainer" style="padding:0px;height:auto;">
                    <table style="width:100%;border-collapse:separate;border-spacing:0;">
                      <tbody>
                        <tr>
                          <td style="width:100%;font-size:0.85vw;padding:0.3vwtext-align:center;border-bottom:1px solid #f0f0f0;color:#6c757d;" class="spanRowData">
                            No items in clipboard
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  </h5>
                </div>
              </div>
            </div>
          </div>
          <div class="subText2" style="font-size: 0.9vw; margin-bottom: 0.8vw; color: var(--secondary-color, #64748b);">
            Use the "Copy to clipboard" button to save content for later use. Content in the clipboard is temporary and will be lost when you close the browser.
          </div>
          <div id="clipboard_preview_box" style="display:none;">
            <div class="view-toggle-buttons" style="display: flex; gap: 0.5vw; margin-bottom: 0.8vw;">
              <button type="button" id="clipboardDesktopView" class="view-toggle-btn active" style="border: 1px solid #dee2e6; background-color: #0d6efd; color: white; border-color: #0d6efd; border-radius: var(--border-radius, 0.42vw); padding: 0.375vw 0.75vw; font-size: var(--font-size-base, 0.97vw);" onclick="$('#ToggleClipboardView').val('Desktop'); toggleClipboardViewMode('Desktop');">💻 Desktop</button>
              <button type="button" id="clipboardMobileView" class="view-toggle-btn" style="border: 1px solid #dee2e6; background-color: #fff; color: black; border-radius: var(--border-radius, 0.42vw); padding: 0.375vw 0.75vw; font-size: var(--font-size-base, 0.97vw);" onclick="$('#ToggleClipboardView').val('Mobile'); toggleClipboardViewMode('Mobile');">📱 Mobile</button>
            </div>
            <select class="question2" id="ToggleClipboardView" style="display:none;">
              <option value="Desktop" selected>Desktop view</option>
              <option value="Mobile">Mobile view</option>
            </select>
            <div id="clipboard_preview_container" style="position:relative!important;width:100%;border: 1px solid #dee2e6;border-radius: var(--border-radius, 0.42vw);background-color:#f8fafc;margin-top:0.5vw;height: 18.75vw;overflow:hidden;display:none;transition: background-color 0.3s ease;overflow-x:hidden;text-align:center;">
              <div id="clipboard_preview_text_placeholder" style="padding-left:1vw;font-size:1vw;margin-top:7vw;text-align:center;">Select a clipboard item from the list to generate a preview.</div>
              <div id="loading_clipboard_preview" style="display:none;position:absolute;top:0;left:0;width:100%;height:100%;background-color:rgba(255,255,255,0.8);z-index:1000;">
                <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);">
                  <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                  </div>
                </div>
              </div>
              <div id="no_clipboard_preview_available" style="display:none;position:absolute;top:0;left:0;width:100%;height:100%;background-color:rgba(255,255,255,0.8);z-index:1000;">
                <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);">
                  <div class="alert alert-warning" role="alert">
                    No preview available
                  </div>
                </div>
              </div>
              <iframe id="clipboardPreviewIframe" class="rightPreviewIframe clipboard-preview-desktop-view" style="display:none; width:100%; height:100%; border:none; position:relative; top:0; left:0; z-index:1; overflow-x:hidden;"></iframe>
            </div>
            <div style="padding-top: 1vw;padding-bottom: 1vw;display: none;position: fixed;bottom: 0;border-top: 1px solid #dbdbdb;width: 48%;background-color: white; z-index: 1;" id="clipboard_actions">
              <div style="display: flex; gap: 0.5vw; justify-content: left;">
                <button class="btn btn-primary" type="button" style="background-color:#0070d2;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="clipboard_append_content_btn" onclick="append_content_from_clipboard();">Add to Bottom</button>
                <button class="btn btn-primary" type="button" style="background-color:#0070d2;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="clipboard_replace_content_btn" onclick="replace_content_with_clipboard();">Full Content Overwrite</button>
                <button class="btn btn-secondary" type="button" style="background-color:#6c757d;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="clipboard_copy_content_btn" onclick="copy_content_from_clipboard_preview();">Copy Content</button>
              </div>
            </div>
          </div>
        </div>
        <!-- previous-saves Tab -->
        <div class="tab-pane fade" id="previous-saves" role="tabpanel" aria-labelledby="previous-saves-tab">
          <div id="previous-saves-content" style="margin-bottom: 5vw;overflow: auto;height: 100%;">
            <div style="padding: 1rem 1.5rem 1rem 1.5rem;">
              <div style="text-align:center;">Loading recent saves...</div>
              <div style="text-align:center;">
                <br>
                <div class="modall-dialog modal-sm" style="text-align:center;max-width:100%;">
                  <div class="modall-content">
                    <span class="fa fa-circle-o-notch fa-spin fa-fw fa-2x" aria-hidden="true"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="padding-top: 1vw;display: none;position: fixed;bottom: 0;border-top: 1px solid #dbdbdb;width: 48%;background-color: white; z-index: 1;" id="save_actions%%=v(@event_id)=%%">
            <div style="display: flex;gap: 0.5vw;bottom: 1vw;position: relative;padding-top: 1vw;">
              <button class="btn btn-primary" type="button" style="background-color:#0070d2;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="append_content_btn%%=v(@event_id)=%%" onclick="append_content_from_save('%%=v(@event_id)=%%');">Add to Bottom</button>
              <button class="btn btn-primary" type="button" style="background-color:#0070d2;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="replace_content_btn%%=v(@event_id)=%%" onclick="replace_content_with_save('%%=v(@event_id)=%%');">Full Content Overwrite</button>
              <button class="btn btn-secondary" type="button" style="background-color:#6c757d;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="copy_content_btn%%=v(@event_id)=%%" onclick="copy_content_from_preview('%%=v(@event_id)=%%');">Copy Content</button>
            </div>
          </div>
        </div>
        <!-- AI Results Tab -->
        <div class="tab-pane fade" id="ai-results" role="tabpanel" aria-labelledby="ai-results-tab">
          <div id="ai-results-content">
            <div style="padding: 1rem 1.5rem 1rem 1.5rem;">
              <div style="text-align:center;">Loading AI results...</div>
              <div style="text-align:center;">
                <br>
                <div class="modall-dialog modal-sm" style="text-align:center;max-width:100%;">
                  <div class="modall-content">
                    <span class="fa fa-circle-o-notch fa-spin fa-fw fa-2x" aria-hidden="true"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="padding-top: 1vw;display: none;position: fixed;bottom: 0;border-top: 1px solid #dbdbdb;width: 48%;background-color: white; z-index: 1;" id="ai_results_actions%%=v(@event_id)=%%">
            <div style="display: flex;gap: 0.5vw;bottom: 1vw;position: relative;padding-top: 1vw;justify-content: space-between;">
              <div>
                <button class="btn btn-info" type="button" style="background-color:#17a2b8;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="reuse_prompt_settings_btn%%=v(@event_id)=%%" onclick="reusePromptSettings('%%=v(@event_id)=%%');">Reuse these prompt settings</button>
              </div>
              <div style="display: flex; gap: 0.5vw;">
                <button class="btn btn-primary" type="button" style="background-color:#0070d2;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="append_ai_content_btn%%=v(@event_id)=%%" onclick="append_content_from_ai_result('%%=v(@event_id)=%%');">Add to Bottom</button>
                <button class="btn btn-primary" type="button" style="background-color:#0070d2;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="replace_ai_content_btn%%=v(@event_id)=%%" onclick="replace_content_with_ai_result('%%=v(@event_id)=%%');">Full Content Overwrite</button>
                <button class="btn btn-secondary" type="button" style="background-color:#6c757d;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="copy_ai_content_btn%%=v(@event_id)=%%" onclick="copy_content_from_ai_result('%%=v(@event_id)=%%');">Copy Content</button>
              </div>
            </div>
          </div>
        </div>
        <!-- Content Library Tab -->
        <div class="tab-pane fade" id="content-library" role="tabpanel" aria-labelledby="content-library-tab">
          <div id="library-content">
            <div style="padding: 1rem 1.5rem 1rem 1.5rem;">
              <div style="text-align:center;">Loading shared content library...</div>
              <div style="text-align:center;">
                <br>
                <div class="modall-dialog modal-sm" style="text-align:center;max-width:100%;">
                  <div class="modall-content">
                    <span class="fa fa-circle-o-notch fa-spin fa-fw fa-2x" aria-hidden="true"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="padding-top: 1vw;display: none;position: fixed;bottom: 0;border-top: 1px solid #dbdbdb;width: 48%;background-color: white; z-index: 1;" id="shared_cb_actions">
            <div style="display: flex;gap: 0.5vw;bottom: 1vw;position: relative;padding-top: 1vw;">
              <button class="btn btn-primary" type="button" style="background-color:#0070d2;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="append_shared_cb_btn" onclick="append_content_from_shared_cb();">Add to Bottom</button>
              <button class="btn btn-primary" type="button" style="background-color:#0070d2;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="replace_shared_cb_btn" onclick="replace_content_with_shared_cb();">Full Content Overwrite</button>
              <button class="btn btn-secondary" type="button" style="background-color:#6c757d;color:white;padding:0.375vw 0.75vw;font-weight:normal;border:none;border-radius:var(--border-radius, 0.42vw);cursor:pointer;font-size:var(--font-size-base, 0.97vw);" id="copy_shared_cb_btn" onclick="copy_content_from_shared_cb();">Copy Content</button>
            </div>
          </div>
        </div>
        <!-- Clipboard Tab -->
        <div class="tab-pane fade" id="clipboard" role="tabpanel" aria-labelledby="clipboard-tab">
          <div id="clipboard-content">
            <div style="padding: 1rem 1.5rem 1rem 1.5rem;">
              <div style="text-align:center;">Loading clipboard...</div>
              <div style="text-align:center;">
                <br>
                <div class="modall-dialog modal-sm" style="text-align:center;max-width:100%;">
                  <div class="modall-content">
                    <span class="fa fa-circle-o-notch fa-spin fa-fw fa-2x" aria-hidden="true"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Preview Container -->
    <div class="preview-container">
      <div id="top_buttons">
        <input type="text" value="%%=v(@de_friendly_name)=%%" name="friendly_name" class="form-input-1a2b sync-field-popup main-block-name friendly-name-input" placeholder="Enter a descriptive internal name" title="Only letters, numbers, hyphens, and underscores allowed">

        <!-- Edit/Preview Toggle -->
        <div class="edit-preview-toggle-container">
          <div class="edit-preview-toggle view-toggle-buttons">
            <button type="button" class="edit-preview-toggle-btn view-toggle-btn active" id="editModeBtn">Edit mode</button>
            <button type="button" class="edit-preview-toggle-btn view-toggle-btn" id="previewModeBtn">Preview mode</button>
          </div>
        </div>

        <!-- Toggle Buttons for Desktop and Mobile View -->
        <div class="toggle-container">
          <div class="view-toggle-buttons" style="height:100%;">
            <button type="button" class="view-toggle-btn active" id="desktopView" title="Switch to desktop preview view">
              <span class="icon-desktop"></span> Desktop
            </button>
            <button type="button" class="view-toggle-btn" id="mobileView" title="Switch to mobile preview view">
              <span class="icon-mobile"></span> Mobile
            </button>
          </div>
        </div>
      </div>
      <div class="preview-content">
          <div class="form-section">
            <!-- Edit Mode Content -->
            <div id="contentPreview">
              <div class="content-wrapper">
                %%[ IF NOT EMPTY(@de_emailName) THEN ]%%
                  %%=TreatAsContent(Lookup(@de_ecc,'HTML','content_id',@event_id))=%%
                %%[ ENDIF ]%%
              </div>
            </div>

            <!-- Preview Mode Content -->
            <div id="emailPreviewContainer" style="display: none; position: relative;">
              <div id="emailPreviewLoadingOverlay">
                <div class="spinner-border text-primary" role="status">
                  <span class="sr-only">Loading...</span>
                </div>
                <div id="emailPreviewLoadingText">Loading preview...</div>
              </div>
              <iframe id="emailPreviewIframe" class="rightPreviewIframe" data-src="%%=Concat('https://cloud.e.newsdigitalmedia.com.au/gh_',IIF(@appStateEnvironment == 'UAT','uat_',''),'email_preview?Brand=',@src_Brand,'&key=',@cookie,'&env=',@appStateEnvironment,'&secret=',@src_secret,'&sys_env=',@environmentAppCentre,'&preview_section=',@preview_section,'&feature_branch=',@feature_branch)=%%"></iframe>
            </div>
          </div>
      </div>
      <div id="result">Content size: 0 bytes (0.00 KB)</div>
      <div id="actionButtons">
        <span id="saveWarningMessage" style="color: #dc3545; margin-right: 10px; font-size: 1vw; display: none;padding-top: .3vw;">You have unsaved changes - switch to Edit mode to save</span>
        <div class="ai-highlight-wrapper" id="closeAiEditingButton_div" data-ai-highlight="true" style="position: relative;display: inline-block;/* width: 100%; */outline: transparent solid 5px;outline-offset: -5px;box-shadow: rgba(255, 0, 0, 0.7) 0px 0px 5px 1px, rgba(0, 255, 255, 0.5) 0px 0px 10px 3px;background-image: linear-gradient(90deg, rgb(255, 0, 0), rgb(255, 103, 0), rgb(255, 146, 0), rgb(255, 220, 0), rgb(220, 255, 0), rgb(189, 255, 0), rgb(48, 255, 0), rgb(0, 255, 207), rgb(0, 235, 255), rgb(0, 143, 255), rgb(0, 68, 255), rgb(143, 0, 255), rgb(196, 0, 255), rgb(255, 0, 169), rgb(255, 0, 0));background-clip: padding-box;background-origin: padding-box;background-size: 400% 400%;background-position: 0% 50%;animation: 30s linear 0s infinite normal none running aiGradientBorder;/* padding: 0.1vw; */border-radius: .5vw;margin-left: .5vw;display:none;"><button id="closeAiEditingButton" class="nav-button-1a2b text-button-1a2b" style="display: none;">Close editing with AI</button></div>
        <button id="undoLastSaveButton" class="btn btn-info me-2" style="display: none;">Undo Last Save</button>
        <button id="saveButton" class="btn btn-primary me-2">Save</button>
        <!--
        <button id="saveBackupButton" class="btn btn-secondary">Save & Backup</button>
        -->
        <button id="undoAllButton" class="btn btn-warning">Undo All</button>
        <button id="redoAllButton" class="btn btn-success" style="display: none;">Redo All</button>
      </div>
    </div>
    <div id="editModal" class="modal">
      <div class="modal-dialog modal-dialog-centered modal-dialog-left">
        <div class="modal-content modal-content-full-height">
          <span class="close-button modal-close-button">&times;</span>
          <div class="modal-header-with-tabs">
            <div class="modal-header-content">
              <ul class="nav nav-tabs edit-modal-tabs" id="editModalTabs" role="tablist">
                <li class="nav-item" id="editor-tab-item">
                  <a class="nav-link active" id="editor-tab" data-toggle="tab" href="#editorContent" role="tab" aria-controls="editorContent" aria-selected="true">Editor</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" id="css-tab" data-toggle="tab" href="#cssContent" role="tab" aria-controls="cssContent" aria-selected="false">Style</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" id="attributes-tab" data-toggle="tab" href="#attributesContent" role="tab" aria-controls="attributesContent" aria-selected="false">Attributes</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link" id="html-tab" data-toggle="tab" href="#htmlContent" role="tab" aria-controls="htmlContent" aria-selected="false">HTML</a>
                </li>
                <li class="nav-item">
                  <a class="nav-link AI_create_and_edit" id="ai-edit-tab" href="#" role="tab" aria-selected="false">Edit with AI <span style="font-size: 0.625vw">✨</span></a>
                </li>
              </ul>
            </div>
            <div class="nav-tabs-spacer">
              <ul class="nav nav-tabs" id="editModalTabs" role="tablist" style="display: none;">
              </ul>
            </div>
          </div>
          <div class="tab-content" id="editModalTabContent">
            <div class="tab-pane fade show active" id="editorContent" role="tabpanel" aria-labelledby="editor-tab">
              <div id="modalFormFields" class="modal-form-fields modal-form-fields-container">
              </div>
            </div>
            <div class="tab-pane fade" id="cssContent" role="tabpanel" aria-labelledby="css-tab">
              <div id="cssFormFields" class="modal-form-fields modal-form-fields-container">
                <!-- CSS fields will be populated here -->
              </div>
            </div>
            <div class="tab-pane fade" id="attributesContent" role="tabpanel" aria-labelledby="attributes-tab">
              <div id="attributesFormFields" class="modal-form-fields modal-form-fields-container">
                <!-- Attributes fields will be populated here -->
              </div>
            </div>
            <div class="tab-pane fade" id="htmlContent" role="tabpanel" aria-labelledby="html-tab">
              <div id="validation-toggle-container" class="validation-toggle-container" style="position: relative; padding-left: 1vw; padding-bottom: 0.5vw; display: flex; align-items: center; gap: 0.5vw;">
                <label class="validation-toggle-label" style="font-size: 0.9vw; font-weight: 600; color: #495057; margin: 0;">
                  Validation & Syntax (BETA):
                </label>
                <div class="toggle-switch" style="position: relative; display: inline-block; width: 2.2vw; height: 1.2vw;">
                  <input type="checkbox" id="validation-toggle" class="toggle-input" checked style="opacity: 0; width: 0; height: 0;">
                  <span class="toggle-slider" style="position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #28a745; transition: 0.3s; border-radius: 1.2vw;">
                    <span class="toggle-slider-circle" style="position: absolute; content: ''; height: 0.9vw; width: 0.9vw; left: 0.15vw; bottom: 0.15vw; background-color: white; transition: 0.3s; border-radius: 50%; transform: translateX(0.85vw);"></span>
                  </span>
                </div>
                <span class="validation-toggle-status" style="font-size: 0.85vw; color: #28a745; font-weight: 500;">ON</span>
                <span class="validation-beta-note" style="font-size: .9vw; color: #6c757d; font-style: italic; margin-left: .5vw;" title="This feature is in beta. You can disable it if it interferes with your workflow.">
                  This feature is in BETA. Turn it OFF it you notice bugs.
                </span>
                <button id="validation-info-btn" type="button" class="btn btn-outline-info btn-sm" style="font-size: 0.55vw; padding: 0.1vw 0.4vw; margin-left: 0.5vw; height: 1.2vw; line-height: 1;color: white;right: 1vw;position: absolute;border-radius:5vw;" title="Click for more information about validation features">
                  INFO
                </button>
              </div>
              <span id="validation-status-dropdown" class="validation-status-container" style="display: none; padding-left: 1vw;">
                <span id="validation-status" class="validation-status valid" title="Click to view validation details">
                  Valid
                  <span class="dropdown-arrow" style="display: none;">▼</span>
                </span>
                <div id="validation-dropdown" class="validation-dropdown" style="display: none;">
                  <div class="validation-dropdown-header">
                    <span class="validation-issues-text">Validation Issues</span>
                    <!-- future feature
                    <div style="display:none;">
                      <button id="fix-all-button" class="fix-all-button" style="display: none;" title="Fix all validation issues automatically">
                        <span class="fix-all-text">Fix All</span>
                        <span class="fix-all-progress" style="display: none;"></span>
                      </button>
                    </div>
                    -->
                  </div>
                  <div id="validation-error-list" class="validation-error-list">
                    <div class="no-errors-message">No validation issues found.</div>
                  </div>
                </div>
              </span>

              <!-- Validation Info Dropdown -->
              <div id="validation-info-dropdown" class="validation-info-dropdown" style="display: none; position: absolute; top: 5vw; left: 0; right: 0; background: white; border: 0.05vw solid #dee2e6; border-radius: 0.3vw; box-shadow: 0 0.2vw 0.6vw rgba(0,0,0,0.15); z-index: 1000; padding: 1vw; margin-top: 0.25vw; max-width: 100%;overflow-y: auto;height: 22vw;">
                <div style="position: relative;">
                  <button type="button" id="validation-info-close" style="position: absolute; top: -0.5vw; right: -0.5vw; background: none; border: none; font-size: 1.5vw; color: #6c757d; cursor: pointer; padding: 0.25vw;" title="Close">×</button>

                  <h6 style="color: #495057; margin-bottom: 0.75vw; font-weight: 600; font-size: 0.7vw;">HTML Validation & Auto-Fix Features</h6>

                  <div style="font-size: 0.65vw; line-height: 1.5; color: #495057;">
                    <p style="margin-bottom: 0.6vw;">When validation is <strong>ON</strong>, the system automatically ensures your email HTML works properly across different email clients:</p>

                    <div style="margin-bottom: 0.75vw;">
                      <strong>🔧 Auto-Removal Logic:</strong>
                      <ul style="margin: 0.4vw 0 0 1vw; padding: 0;">
                        <li><strong>Invalid HTML tags:</strong> Automatically removes tags that don't work in emails (like &lt;script&gt;, &lt;style&gt;, &lt;div&gt; etc.)</li>
                        <li><strong>Invalid attributes:</strong> Removes HTML attributes that aren't allowed on specific tags (like cellpadding on non-table elements)</li>
                        <li><strong>Email-unfriendly code:</strong> Cleans up code that causes display issues in email clients</li>
                        <li><strong>On HTML tab load:</strong> Automatically cleans invalid content when you open the HTML tab</li>
                        <li><strong>On paste:</strong> Prevents pasting of invalid HTML attributes and tags</li>
                      </ul>
                    </div>

                    <div style="margin-bottom: 0.75vw;">
                      <strong>✨ Auto-Fix Suggestions:</strong>
                      <ul style="margin: 0.4vw 0 0 1vw; padding: 0;">
                        <li><strong>Click to fix:</strong> Click on validation errors to automatically apply suggested corrections</li>
                        <li><strong>Fix All button:</strong> Automatically fix multiple issues at once</li>
                        <li><strong>Smart conversions:</strong> Converts problematic tags to email-friendly alternatives (e.g., &lt;div&gt; to table structure)</li>
                      </ul>
                    </div>

                    <div style="margin-bottom: 0.75vw;">
                      <strong>🎯 When to turn it OFF:</strong>
                      <ul style="margin: 0.4vw 0 0 1vw; padding: 0;">
                        <li>If you're working with custom HTML that you don't want automatically modified</li>
                        <li>If you're testing specific code and need it to remain unchanged</li>
                        <li>If you encounter any bugs or unexpected behavior</li>
                      </ul>
                    </div>

                    <div style="background: #e7f3ff; padding: 0.6vw; border-radius: 0.2vw; border-left: 0.2vw solid #007bff;">
                      <strong>💡 Note:</strong> Your validation preference is saved during your current session. It will reset to ON when you refresh the page or start a new session.
                    </div>
                  </div>
                </div>
              </div>

              <div id="htmlFormFields" class="modal-form-fields modal-form-fields-container">
                <!-- HTML editor will be populated here -->
              </div>
            </div>
          </div>
          <div class="modal-buttons">
            <div class="element-path-container">
              <span class="path-label">Path:</span>
              <div id="elementPath" class="element-path"></div>
            </div>
            <div class="button-group">
              <button id="keepButton" class="btn modal-button-margin">Keep</button>
              <button id="cancelButton" class="btn modal-button-margin">Cancel</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="loader">
      <div class="loader-animation">
        <div class="loader-dot"></div>
        <div class="loader-dot"></div>
        <div class="loader-dot"></div>
      </div>
    </div>
    <div class="modall bd-example-modal-lg" data-backdrop="static" data-keyboard="false" tabindex="-1">
      <div class="modall-dialog modal-sm">
        <div class="modall-content modall-content-width">
          <span class="fa fa-circle-o-notch fa-spin fa-fw fa-2x"></span>
        </div>
      </div>
    </div>
    <!--
    <div id="aiAssistantModal" class="modal" style="z-index:12000;">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content custom-modal">
        <div class="modal-header">
          <h2 class="modal-title">Generate new content with AI ✨</h2>
          <button type="button" class="btn-close" id="aiAssistantClose" aria-label="Close">&times;</button>
        </div>
        <div class="modal-body">
          <p>Enter a prompt. The response will be inserted into the editor.</p>
          <textarea id="aiAssistantPrompt" class="form-input" style="height:6.94vw; width: 100%"></textarea>
        </div>
        <div class="modal-footer">
          <button id="aiAssistantSubmit" class="btn btn-primary">Submit</button>
          <button id="aiAssistantCancel" class="btn btn-secondary">Cancel</button>
        </div>
        </div>
      </div>
    </div>
    -->
    <div class="modal fade layer_3_z_index" id="loadingModal" tabindex="-1" aria-labelledby="loadingModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modal-content-transparent">
          <div class="modal-body text-center">
            <div class="spinner-border text-primary spinner-dimensions" role="status">
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Image Upload Modal -->
    <div class="modal fade layer_3_z_index" id="imageUploadModal" tabindex="-1" role="dialog" aria-labelledby="imageUploadModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false" data-focus="false">
      <div class="modal-dialog modal-dialog-centered modal-dialog-custom" role="document">
        <div class="modal-content custom-modal">
          <div class="modal-header">
            <h5 class="modal-title" id="imageUploadModalLabel">Image Upload</h5>
            <button type="button" class="btn-close" id="imageUploadModalClose" aria-label="Close" style="margin-top: -.5vw;">&times;</button>
          </div>
          <div class="modal-body text-center py-4">
            <div class="spinner-border text-primary mb-3" role="status">
              <span class="sr-only">Uploading...</span>
            </div>
            <h5>Auto-Converting Base64 Image</h5>
            <p id="imageUploadModalMessage">You pasted a base64 image into this field that is very large file not email friendly. But don't worry, it is now automatically being converted to an image URL. This saves you time from manually uploading the image whilst keeping the email more light-weight and performant. Please wait while the conversion of base64 file to image URL completes...</p>
            <div class="alert alert-info mt-3" style="font-size: 0.9em;">
              <strong>Note:</strong> If the image doesn't appear immediately after conversion, don't panic as the server may sometimes take a few seconds to refresh the image cache. Switching to Preview mode after 10-15 seconds should show the image properly previewed. Another way to quickly check if the upload worked is by checking that the image url starts with "https://image.e.newscorpaus.com.au/lib/".
            </div>
            <div class="progress mt-3">
              <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%"></div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelImageUpload">Cancel</button>
          </div>
        </div>
      </div>
    </div>
    <div id="contextMenu" class="custom-context-menu">
      <ul>
        <li id="selectElementsContainer" class="context-menu-item-top">
          <table class="element-selection-table" style="width: 100%; border-collapse: collapse; margin-bottom: 0;">
            <tr>
              <td style="text-align: center; width: 33%; padding: .3vw; font-weight: bold; background-color: #f8f9fa; border-right: 1px solid #dee2e6;font-size:.8vw;color: #0070d2;">Select:</td>
              <td style="text-align: center; width: 33%; padding: 0;" id="selectChildElement">
                <button type="button" style="width: 100%; border: none; background: none; padding: 5px; cursor: pointer;">Inner</button>
              </td>
              <td style="text-align: center; width: 33%; padding: 0; border-left: 1px solid #dee2e6;" id="selectParentElement">
                <button type="button" style="width: 100%; border: none; background: none; padding: 5px; cursor: pointer;">Outer</button>
              </td>
            </tr>
          </table>
        </li>
        <li id="copyToClipboardElement">
          <button type="button">Copy to clipboard</button>
        </li>
        <li id="editElement">
          <button type="button">Edit</button>
        </li>
        <li id="aiElement_edit">
          <button type="button" class="AI_create_and_edit ai-create-edit-button">Edit with AI <span style="font-size: 0.625vw">✨</span></button>
        </li>
        <li id="copyElement">
          <button type="button">Copy</button>
        </li>
        <li id="cutElement">
          <button type="button">Cut</button>
        </li>
        <li id="pasteElement">
          <button type="button">Paste</button>
        </li>
        <li id="duplicateIntoNewRow" >
          <button type="button">Duplicate</button>
        </li>
        <li id="duplicateElement" style="display:none;">
          <button type="button">Duplicate</button>
        </li>
        <li id="reviewElement">
          <button type="button">Review</button>
        </li>
        <li id="deleteTrigger" class="deleteTrigger context-menu-item-bottom">
          <button type="button">Delete</button>
        </li>
      </ul>
    </div>
    <div id="deleteModal" class="modal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content custom-modal">
          <div class="modal-header">
            <h2 class="modal-title" id="deleteModalLabel">Confirm Deletion</h2>
            <button type="button" class="btn-close" id="deleteModalClose" aria-label="Close">&times;</button>
          </div>
          <div class="modal-body">
            <p>Are you sure you want to delete this block?</p>
          </div>
          <div class="modal-footer">
            <button id="deleteElement" class="btn btn-danger">Delete</button>
            <button id="cancelDelete" class="btn btn-secondary">Cancel</button>
          </div>
        </div>
      </div>
    </div>
    <div style="display:none!important;" class="modal fade" id="duplicateModal" tabindex="-1" aria-labelledby="duplicateModalLabel" aria-hidden="true" data-bs-backdrop="true" data-bs-keyboard="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content custom-modal">
          <div class="modal-header">
            <h2 class="modal-title" id="duplicateModalLabel">Duplicate Element</h2>
            <button type="button" class="btn-close" id="duplicateModalClose" aria-label="Close">&times;</button>
          </div>
          <div class="modal-body">
            <p>Select how you want to duplicate this element:</p>
            <div class="d-grid gap-2">
              <button id="duplicateIntoNewColumn" class="btn btn-primary">Duplicate into a New Column</button>
              <button id="duplicateIntoNewRow" class="btn btn-secondary">Duplicate into a New Row</button>
            </div>
          </div>
        </div>
      </div>
    </div>

     <!-- Paste Options Modal -->
    <div class="modal fade" id="pasteOptionsModal" tabindex="-1" aria-labelledby="pasteOptionsModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content custom-modal">
          <div class="modal-header">
            <h2 class="modal-title" id="pasteOptionsModalLabel">Paste Options</h2>
            <button type="button" class="btn-close" id="pasteOptionsModalClose" aria-label="Close">&times;</button>
          </div>
          <div class="modal-body">
            <p>Select how you want to paste the content:</p>
            <div class="d-grid gap-2">
              <button id="pasteOverwrite" class="btn btn-primary">Overwrite</button>
              <button id="pasteAsNewRow" class="btn btn-secondary">Paste as new row</button>
              <button id="pasteBelow" class="btn btn-secondary">Paste below</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced save modal with cancel button -->
    <div class="modal fade layer_3_z_index" id="saveModal" tabindex="-1" role="dialog" aria-labelledby="saveModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
      <div class="modal-dialog modal-dialog-centered modal-dialog-custom" role="document">
        <div class="modal-content custom-modal">
          <div class="modal-header">
            <h5 class="modal-title" id="saveModalLabel">Saving Content Block</h5>
          </div>
          <div class="modal-body">
            <div class="text-center mb-4">
              <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Saving...</span>
              </div>
              <p class="mt-3" id="saveModalMessage">Your content block is being saved. Please wait...</p>
              <div class="progress mt-3">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100" style="width: 100%"></div>
              </div>
            </div>
            <div class="text-center">
              <button type="button" class="btn btn-sm btn-outline-secondary" id="cancelSaveButton">Cancel</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Error modal for detailed error information -->
    <div class="modal fade layer_3_z_index" id="errorModal" tabindex="-1" role="dialog" aria-labelledby="errorModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered modal-dialog-custom" role="document">
        <div class="modal-content custom-modal">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="errorModalLabel">Error</h5>
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <div class="alert alert-danger" id="errorModalContent"></div>
            <div id="errorDetails" class="error-details">
              <hr>
              <h6>Technical Details:</h6>
              <pre id="errorDetailContent" class="error-detail-content"></pre>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            <button type="button" class="btn btn-primary retry-button" id="retryButton">Retry</button>
          </div>
        </div>
      </div>
    </div>


    <!-- Generating Content Modal -->
    <div class="modal fade layer_3_z_index" id="loadingModal-1a2b" tabindex="-1" role="dialog" aria-modal="true" data-backdrop="static" data-keyboard="false">
      <div class="modal-dialog modal-dialog-centered modal-dialog-custom" role="document">
        <div class="modal-content custom-modal">
          <div class="modal-body text-center ai-modal-body">
            <!-- Loading State -->
            <div id="loading-state">
              <div class="ai-progress-container">
                <div class="ai-progress-circle">
                  <div class="ai-progress-text">0%</div>
                </div>
              </div>

              <h5 class="ai-modal-heading">Generating Content...</h5>

              <!-- Model Processing List -->
              <div class="ai-model-list-container">
                <div class="ai-model-list" id="ai-model-list">
                  <!-- Model entries will be dynamically added here -->
                </div>
              </div>

              <!-- Current Status Message -->
              <p id="loading-message" class="ai-status-message">Preparing request...</p>

              <div class="ai-modal-actions">
                <button type="button" id="cancel-api-btn" class="btn btn-outline-secondary ai-cancel-btn" tabindex="0">Cancel</button>
              </div>
            </div>

            <!-- Error State -->
            <div id="error-state" class="error-state">
              <div class="ai-error-icon">
                <i class="fas fa-exclamation-circle"></i>
                <span class="sr-only">Error</span>
              </div>
              <h5 class="ai-error-heading">Error</h5>
              <p id="modal-error-message" class="ai-error-message"></p>
              <div class="ai-error-actions">
                <button type="button" id="retry-api-btn" class="btn btn-primary ai-retry-btn" tabindex="0">Retry</button>
                <button type="button" id="close-api-btn" class="btn btn-secondary ai-close-btn" data-dismiss="modal" tabindex="0">Close</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Content Size Error Modal -->
    <div class="modal fade layer_3_z_index" id="contentSizeErrorModal" tabindex="-1" role="dialog" aria-labelledby="contentSizeErrorModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered modal-dialog-custom" role="document">
        <div class="modal-content custom-modal">
          <div class="modal-header bg-danger text-white">
            <h5 class="modal-title" id="contentSizeErrorModalLabel">The selected content is too large</h5>
            <button type="button" class="btn-close text-white" data-dismiss="modal" id="ContentLargeModalClose" aria-label="Close" style="margin-top: -.5vw;">&times;</button>
          </div>
          <div class="modal-body" style="font-size:1vw;">
            <div class="alert alert-danger" id="contentSizeErrorContent">
              The selected content area is too large for AI processing.
            </div>
            <div id="contentSizeDetails">
              <p><strong>Current size:</strong> <span id="currentContentSize">0</span> characters</p>
              <p><strong>Maximum allowed:</strong> <span id="maxContentSize">16,000</span> characters (4,000 tokens)</p>
              <hr>
              <h4 style="padding-top:1vw;">Suggestions:</h4>
              <ul style="padding:20px 0 0 20px;">
                <li>Select a smaller content area within the current selection</li>
                <li>Break down the content into smaller sections</li>
                <li>Remove unnecessary HTML elements or text</li>
                <li>Use the regular AI generation for creating new content instead</li>
              </ul>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Settings Reuse Modal -->
    <div class="modal fade layer_3_z_index" id="settingsReuseModal" tabindex="-1" role="dialog" aria-labelledby="settingsReuseModalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
      <div class="modal-dialog modal-dialog-centered modal-dialog-custom" role="document">
        <div class="modal-content custom-modal">
          <div class="modal-header">
            <h5 class="modal-title" id="settingsReuseModalLabel">Previous Settings Detected</h5>
            <button type="button" class="btn-close" id="settingsReuseModalClose" aria-label="Close" style="margin-top: -.5vw;">&times;</button>
          </div>
          <div class="modal-body" style="font-size:1vw;">
            <p>You have previously used the Generate button with specific settings. Would you like to reuse those settings or start fresh?</p>
            <div class="alert alert-info mt-3" style="font-size: 0.9em;">
              <strong>Previous settings include:</strong> Content source, creativity level, chain prompts, model selection, and other preferences from Step 2.
            </div>
            <div class="form-check mt-3">
              <input type="checkbox" class="form-check-input" id="dontAskAgainCheckbox">
              <label class="form-check-label" for="dontAskAgainCheckbox" style="font-size: 0.9em;">
                Don't ask me again during this session
              </label>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="discardSettingsBtn">Discard & Start Fresh</button>
            <button type="button" class="btn btn-primary" id="reuseSettingsBtn">Reuse Settings</button>
          </div>
        </div>
      </div>
    </div>

    <!-- New Prompts Modal -->
    <div class="modal fade layer_3_z_index" id="promptImprovementModal" tabindex="-1" role="dialog" aria-labelledby="promptImprovementModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered modal-dialog-custom" role="document" style="max-width: 90vw; width: 90vw;">
        <div class="modal-content custom-modal">
          <div class="modal-header">
            <h5 class="modal-title" id="promptImprovementModalLabel">✨ Generate New Prompts</h5>
            <button type="button" class="btn-close" data-dismiss="modal" id="promptModalClose" aria-label="Close" style="margin-top: -.5vw;">&times;</button>
          </div>
          <div class="modal-body">
            <!-- Progress Bar -->
            <div class="progress-bar-1a2b" style="margin-top:0;">
              <div class="step-indicator-1a2b active" data-step="1">
                <div class="step-bubble-1a2b">1</div>
              </div>
              <div class="step-indicator-1a2b" data-step="2">
                <div class="step-bubble-1a2b">2</div>
              </div>
            </div>

            <!-- Modal Step 1: Form -->
            <div class="modal-step-panel active" data-modal-step="1">
              <form id="promptImprovementForm" style="margin-top: 1vw;">
                <div class="row">
                  <div class="col-md-6" style="padding-right: 1vw;">
                    <div class="form-group">
                      <label class="form-label-1a2b">What type of email template?</label>
                      <select class="form-select-1a2b custom-select prompt-improvement-field" id="emailType" name="emailType">
                        <option value="">Select type...</option>
                        <option value="newsletter">Newsletter/Update</option>
                        <option value="promotional">Promotional/Sales</option>
                        <option value="announcement">Product/Service Announcement</option>
                        <option value="welcome">Welcome/Onboarding</option>
                        <option value="event">Event Invitation</option>
                        <option value="transactional">Transactional/Receipt</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label class="form-label-1a2b">Preferred layout structure?</label>
                      <select class="form-select-1a2b custom-select prompt-improvement-field" id="layoutStructure" name="layoutStructure">
                        <option value="">Select structure...</option>
                        <option value="mixed-layouts" selected>Mixed layouts (single, two, and three columns)</option>
                        <option value="single-column">Single column focus</option>
                        <option value="two-column">Two column focus</option>
                        <option value="three-column">Three column focus</option>
                        <option value="grid-style">Product grid style</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label class="form-label-1a2b">Visual style and branding?</label>
                      <select class="form-select-1a2b custom-select prompt-improvement-field" id="visualStyle" name="visualStyle">
                        <option value="">Select style...</option>
                        <option value="modern-sophisticated" selected>Modern & Sophisticated</option>
                        <option value="clean-minimal">Clean & Minimal</option>
                        <option value="professional-corporate">Professional & Corporate</option>
                        <option value="bold-engaging">Bold & Engaging</option>
                        <option value="elegant-premium">Elegant & Premium</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label class="form-label-1a2b">Key content elements needed?</label>
                      <select class="form-select-1a2b custom-select prompt-improvement-field" id="contentElements" name="contentElements">
                        <option value="">Select elements...</option>
                        <option value="images-cta">Images with strong CTAs</option>
                        <option value="text-heavy">Text-heavy with headlines</option>
                        <option value="product-showcase">Product showcase with pricing</option>
                        <option value="mixed-media" selected>Mixed text, images, and buttons</option>
                        <option value="feature-highlights">Feature highlights with icons</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div>
                  <div class="col-md-12">
                    <div class="form-group">
                      <label class="form-label-1a2b">Role/Specialisation Context (Optional)</label>
                      <textarea class="form-select-1a2b form-textarea prompt-improvement-field" id="brandContext" name="brandContext" rows="3" maxlength="1000" placeholder="Add specific context like: 'Australian news brand', 'luxury retail company', 'tech startup', 'healthcare provider', etc. Or describe your brand's unique style requirements." style="margin-bottom: -1vw;"></textarea>
                    </div>
                  </div>
                </div>
              </form>
              <div class="navigation-buttons-1a2b mt-3">
                <button type="button" class="nav-button-1a2b text-button-1a2b" id="cancelModalBtn2">Cancel</button>
                <button type="button" id="generatePromptSuggestions" class="nav-button-1a2b text-button-1a2b AI_create_and_edit-1a2b" disabled>
                  Generate New Prompts <span>✨</span>
                </button>
                <button type="button" id="goToResultsBtn" class="nav-button-1a2b text-button-1a2b" style="display: none;">
                  Go to Results
                </button>
                <span id="promptValidationMessage" style="display: none; margin-left: 1vw; font-size: 0.9vw; color: #dc3545 !important; font-weight: 500;">
                  Ready to generate! The form has smart defaults, or you can customize the fields above.
                </span>
              </div>
            </div>

            <!-- Modal Step 2: Results -->
            <div class="modal-step-panel" data-modal-step="2">
              <div class="row" id="promptSuggestionsRow">
                <!-- Generated suggestions will appear here as horizontal cards -->
              </div>
              <div class="navigation-buttons-1a2b mt-3">
                <button type="button" class="nav-button-1a2b text-button-1a2b" id="backToStep1">Back</button>
                <button type="button" class="nav-button-1a2b text-button-1a2b" id="cancelModalBtn">Cancel</button>
              </div>
            </div>

            <!-- Loading Overlay -->
            <div id="promptGenerationLoading" style="display: none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255,255,255,0.9); z-index: 1000;">
              <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
                <div class="spinner-border text-primary" role="status">
                  <span class="sr-only">Generating new prompts...</span>
                </div>
                <p class="mt-2">Creating new prompt suggestions...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>



    <div id="propertiesPanel"></div>
  </div>
</body>
