%%[
  SET @action_area = QueryParameter('action_area')
  SET @src_Secret = QueryParameter('e')
  SET @event_id = QueryParameter('event_id')
  SET @sourceTable = IIF(@AppStateEnvironment == 'UAT','ent.EmailObject_UAT','ent.EmailObject')
  SET @de_ecc = IIF(@AppStateEnvironment == 'UAT','ent.email_content_cache_UAT','ent.email_content_cache')
  SET @de_ecc_saves = IIF(@AppStateEnvironment == 'UAT','ent.email_content_cache_saves_UAT','ent.email_content_cache_saves')
]%%
<div class="related_fields" style="padding:0!important;margin-bottom:0.8vw;border: 1px solid #dee2e6;border-radius: var(--border-radius, 0.42vw);overflow: hidden;margin-top:1vw;">
  <div class="assetCard">
    <div class="card-header">
      <h5 class="mb-0">
      <div class="assetRowContainer" style="padding:0px;background-color: #f8f9fa;">
        <table style="width:100%;border-collapse:separate;border-spacing:0;">
          <thead>
            <tr>
              <th style="width:25.5%;font-size:0.9vw;font-weight:600;padding-left: .4vw;text-align:left;border-bottom:1px solid #dee2e6;height: 3vw;padding-bottom: .6vw;">Updated date</th>
              <th style="width:55%;font-size:0.9vw;font-weight:600;padding-left: .4vw;text-align:left;border-bottom:1px solid #dee2e6;height: 3vw;padding-bottom: .6vw;">Select a saved content</th>
            </tr>
          </thead>
        </table>
      </div>
      </h5>
    </div>
  </div>
  <div class="list_view_table_row_container" style="max-height: 8vw!important;margin-bottom:0!important;overflow-y:auto;" id="saves_list%%=v(@event_id)=%%">
    %%[
    /* Use UpdatedDate for sorting if it exists, otherwise use CreatedDate */
    SET @sort_field = 'UpdatedDate'
    SET @has_updated_date = RowCount(LookupRows(@de_ecc_saves,'content_id',@event_id))
    IF @has_updated_date == 0 THEN SET @sort_field = 'CreatedDate' ENDIF

    for @save = 1 to RowCount(LookupOrderedRows(@de_ecc_saves,10,Concat(@sort_field,' DESC'),'content_id',@event_id)) do
      SET @save_rows = LookupOrderedRows(@de_ecc_saves,10,Concat(@sort_field,' DESC'),'content_id',@event_id)
      SET @current_content_id = Field(Row(@save_rows,@save),'content_id')
      SET @current_save_id = Field(Row(@save_rows,@save),'save_id')
      SET @current_content_name = Field(Row(@save_rows,@save),'content_name')
      SET @current_friendly_name = Field(Row(@save_rows,@save),'friendly_name')
      /* Use UpdatedDate if it exists, otherwise use CreatedDate */
      SET @current_date = IIF(NOT EMPTY(Field(Row(@save_rows,@save),'UpdatedDate')), Field(Row(@save_rows,@save),'UpdatedDate'), Field(Row(@save_rows,@save),'CreatedDate'))
      /* The table doesn't have an UpdatedBy field, so we'll leave it blank */
      SET @current_UpdatedBy = ''
      SET @current_HTML = Field(Row(@save_rows,@save),'HTML')
      ]%%
      <div class="retrieve">
        <div class="assetCard">
          <h5 class="mb-0">
          <div class="assetRowContainer" style="padding:0px;">
            <table style="width:100%;border-collapse:separate;border-spacing:0;">
              <tbody>
                <tr id="table_tr_row%%=v(@event_id)=%%%%=v(@current_save_id)=%%" class="table_tr_row" style="cursor:pointer;transition:background-color 0.2s ease;" onclick="
                  // Reset all row backgrounds
                  $('tr').css('background-color', '');
                  // Set this row's background directly
                  $(this).css('background-color', '#daedf9');
                  // Trigger click on the span
                  $(this).find('span.toggleSortByFields').click();">
                  <td style="width:22%;font-size:0.85vw;padding:0.6vw 0.5vw;text-align:left;border-bottom:1px solid #f0f0f0;cursor:pointer;" class="spanRowData" onclick="
                    // Prevent event from bubbling up to the row
                    event.stopPropagation();
                    // Reset all row backgrounds
                    $('tr').css('background-color', '');
                    // Set this row's background directly
                    $(this).closest('tr').css('background-color', '#daedf9');
                    // Trigger click on the span in the next cell
                    $(this).closest('tr').find('span.toggleSortByFields').click();">
                    %%=Format(@current_date,'dd/MM/yy hh:mm tt')=%%
                  </td>
                  <td style="width:55%;font-size:0.85vw;padding: 0.0vw .8vw 0.5vw .5vw;text-align:left;border-bottom:1px solid #f0f0f0;" class="spanRowData">
                    <span style="color:#0070d2;font-size:0.85vw;" type="button" class="toggleSortByFields" id='{"last_updated_date":"%%=Format(@current_date,'dd/MM/yy hh:mm tt')=%%","secret":"%%=v(@src_Secret)=%%","content_id":"%%=v(@current_content_id)=%%","save_id":"%%=v(@current_save_id)=%%","html":"%%=Base64Encode(@current_HTML)=%%"}'  onclick="
                      // Prevent event from bubbling up to the row
                      event.stopPropagation();
                      $('#savePreviewIframe%%=v(@event_id)=%%').attr('src','');
                      $('#loading_save_preview_left_view%%=v(@event_id)=%%').show();
                      extract_val_from_select_option_id_save(event,'%%=v(@event_id)=%%');
                      $('#preview_box%%=v(@event_id)=%%').css('display','block');
                      $('#preview_text_placeholder%%=v(@event_id)=%%').css('display','none');
                      // Reset all row backgrounds
                      $('tr').css('background-color', '');
                      // Set this row's background directly
                      $(this).closest('tr').css('background-color', '#daedf9');">
                      %%=IIF(NOT EMPTY(@current_friendly_name),@current_friendly_name,Concat('Save from ',Format(@current_date,'dd/MM/yy hh:mm tt')))=%%
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          </h5>
        </div>
      </div>
      %%[
    next @save
    ]%%
    %%[ IF RowCount(LookupOrderedRows(@de_ecc_saves,10,Concat(@sort_field,' DESC'),'content_id',@event_id)) == 0 THEN ]%%
    <div class="retrieve">
      <div class="assetCard">
        <h5 class="mb-0">
        <div class="assetRowContainer" style="padding:0px;">
          <table style="width:100%;border-collapse:separate;border-spacing:0;">
            <tbody>
              <tr>
                <td style="width:100%;font-size:0.85vw;padding:0.6vw 0.5vw;text-align:center;border-bottom:1px solid #f0f0f0;color:#6c757d;" class="spanRowData">
                  No available saves
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        </h5>
      </div>
    </div>
    %%[ ENDIF ]%%
  </div>
</div>
<div class="subText2" style="font-size: 0.9vw; margin-bottom: 0.8vw; color: var(--secondary-color, #64748b);">
  %%[
  /* Use the same sort field as defined above */
  IF RowCount(LookupOrderedRows(@de_ecc_saves,10,Concat(@sort_field,' DESC'),'content_id',@event_id)) == 0 THEN ]%%
  You don't have any saved content. Each time you save content in the editor, a copy is automatically stored that you can revert to if needed.
  %%[ ELSE ]%%
  To preview and restore content from a previous save, click on a row in the above table.
  %%[ ENDIF ]%%
</div>
<div id="preview_box%%=v(@event_id)=%%" style="display:none;height:calc(100vh - 30vw);position:relative;" class="dynamic-preview-container">
  <div style="width:100%;margin-bottom:0.8vw;display:block;">
    <div class="view-toggle-buttons" style="display:flex;gap:0.5vw;height:auto;">
      <button type="button" id="desktopView" class="view-toggle-btn active" style="border: 1px solid #dee2e6; background-color: #0d6efd; color: white; border-color: #0d6efd; border-radius: var(--border-radius, 0.42vw); padding: 0.375vw 0.75vw; font-size: var(--font-size-base, 0.97vw);" onclick="$('#ToggleSaveEmailView%%=v(@event_id)=%%').val('Desktop'); desktop_or_mobile('ToggleSaveEmailView%%=v(@event_id)=%%','savePreviewIframe%%=v(@event_id)=%%', 'Desktop');">💻 Desktop</button>
      <button type="button" id="mobileView" class="view-toggle-btn" style="border: 1px solid #dee2e6; background-color: #fff; color: black; border-radius: var(--border-radius, 0.42vw); padding: 0.375vw 0.75vw; font-size: var(--font-size-base, 0.97vw);" onclick="$('#ToggleSaveEmailView%%=v(@event_id)=%%').val('Mobile'); desktop_or_mobile('ToggleSaveEmailView%%=v(@event_id)=%%','savePreviewIframe%%=v(@event_id)=%%', 'Mobile');">📱 Mobile</button>
    </div>
  </div>
  <select class="question2" id="ToggleSaveEmailView%%=v(@event_id)=%%" style="display:none;">
    <option value="Desktop" selected>Desktop view</option>
    <option value="Mobile">Mobile view</option>
  </select>
  <div style="position:relative;width:100%;border: 1px solid #dee2e6;border-radius: var(--border-radius, 0.42vw);background-color:#f8fafc;margin-top:0.5vw;height:calc(100% - 2vw);min-height:25vw;overflow:hidden;margin-bottom:5vw;">
    <div id="preview_text_placeholder%%=v(@event_id)=%%" style="padding-left:1vw;font-size:1vw;margin-top:7vw;text-align:center;">Select a saved content from the list to generate a preview.</div>
    <div id="loading_save_preview_left_view%%=v(@event_id)=%%" style="display:none;position:absolute;top:0;left:0;width:100%;height:100%;background-color:rgba(255,255,255,0.8);z-index:1000;">
      <div style="position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>
    </div>
    <iframe id="savePreviewIframe%%=v(@event_id)=%%" class="rightPreviewIframe other_block_preview save-preview-desktop-view" style="display:none;"></iframe>
  </div>
</div>
