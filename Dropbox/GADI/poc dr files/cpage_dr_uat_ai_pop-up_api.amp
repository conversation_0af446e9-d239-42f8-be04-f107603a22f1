%%[
SET @cookie = QueryParameter('c')
SET @localTime = SystemdateToLocalDate(NOW())
SET @userName = Lookup("ent.Users","Username","SessionID",@cookie)
SET @lastLoginDate = Lookup("ent.Users","LastLoginDate","SessionID",@cookie)
IF @lastLoginDate == '' THEN 
  SET @lastLoginDate = '1900-01-01' 
ENDIF
SET @lastLoginDateFormatted = FormatDate(@lastLoginDate,"YYYY-MM-DD")
SET @localTimeFormatted = FormatDate(@localTime,"YYYY-MM-DD")
SET @stateDE = 'ent.AppC_Notifications'
]%%<script runat="server" language="javascript">
var sys_env = Platform.Request.GetQueryStringParameter('sys_env');
Platform.Variable.SetValue("@environmentAppCentre",sys_env);
var guid = Platform.Function.GUID();
var cookie = Platform.Request.GetQueryStringParameter('c');
var biz_env = Platform.Request.GetQueryStringParameter('biz_env');
Platform.Variable.SetValue("@biz_env",biz_env);
var localTime = Platform.Variable.GetValue("@localTime");
var userName = Platform.Variable.GetValue("@userName");
var env = biz_env;

var feature_branch = Platform.Request.GetQueryStringParameter('feature_branch');
Platform.Variable.SetValue("@feature_branch",feature_branch);

var now = Platform.Function.Now();
var localTime = Platform.Function.SystemDateToLocalDate(now);
var userCookie           = Platform.Function.Lookup("ent.Users", "SessionID", "SessionID", cookie);
var formattedLastLoginDate = Platform.Variable.GetValue("@lastLoginDateFormatted");
var formattedLocalTime     = Platform.Variable.GetValue("@localTimeFormatted");


function log_ui_error(var_name, errMsg, raw_error) { var errMsg;
  try { errMsg = (raw_error) ? errMsg + '<br>' + Platform.Function.Stringify(raw_error) : errMsg;
    var file_paths = Platform.Variable.GetValue("@paths") || "";
    if (errMsg) {
     Platform.Response.Write(file_paths + '<br>' + errMsg); Platform.Variable.SetValue("@errorMessage", errMsg);
    }
    Platform.Function.InsertData("ent.appC_ui_error_log",
      ["event_id", "SessionID", "file_paths", "var_name", "error_message", "created_date", "created_by", "sys_env","biz_env"],
      [Platform.Function.GUID(), cookie, file_paths, var_name, errMsg, localTime, userName, sys_env, env]
    );
  } catch(e) { Platform.Response.Write("Error logging: " + Platform.Function.Stringify(e)); }
}

/* access check */
var access = 1;
if (feature_branch) {
  if (!userName) {
    log_ui_error('access_check','Invalid request..'); access = 0;
  } else {
    var fb_access = Platform.Function.Lookup("ent.Users", "feature_branch_access", "UserName", userName);
    fb_access = (fb_access === true);
    if (!fb_access) {
      log_ui_error('access_check','The username "'+userName+'" does not have access to the feature branch environments.'); access = 0;
    }
  }
} else if (sys_env != 'PROD') {
  var env_access = Platform.Function.Lookup("ent.Users", "appc_"+sys_env+"_access", "UserName", userName);
  env_access = (env_access === true);
  if (!env_access) {
    log_ui_error('access_check','The username "'+userName+'" does not have access to the '+sys_env+' environment."'); access = 0;
  }
}

function safeJsonParse(jsonString) {
  if (typeof jsonString !== "string") { return jsonString; }
  jsonString = jsonString.replace(/[\u0000-\u001F]/g, "");
  try { var result = eval("(" + jsonString + ")"); return result; }
  catch (e) { log_ui_error(name, "Error parsing JSON: ",e); return; }
}

function get_file(path, branch, name) { var b64d;
  try { var paths = Platform.Variable.GetValue("@paths") || "";
    Platform.Variable.SetValue("@paths", (!paths) ? " ➜ " + path : paths + "<br> ➜ " + path);
    if (!path) { log_ui_error(name, "The path param is empty."); return; }
    if (sys_env != 'PROD' && sys_env != 'Release' && /archive\/|pocs\/|cpage\//.test(path)) {
      log_ui_error(name, "Remove file from the archive, cpage or POCs folders."); return;
    }
    if (branch) {
      try { var status = [];
        var requestUrl = ['https://api.github.com/repos', 'newscorp-ghfb', 'app-centre', 'contents', Platform.Function.UrlEncode(path, false)].join('/') + '?ref=' + Platform.Function.UrlEncode(branch);
        var resp = Platform.Function.HTTPGet(requestUrl, false, 0, ["Authorization", "User-Agent"], ["token " + Platform.Function.Lookup("ent.api_keys", "Key", "Id", "GitHub"), "SSJS-GitHub-App"], status);
        var j_resp; j_resp = safeJsonParse(resp);
        if (j_resp && j_resp.content) {
          b64d = Platform.Function.Base64Decode(j_resp.content.replace(/(\r\n|\n|\r)/gm, ""));
          if (b64d) { Platform.Variable.SetValue("@b64d", b64d);
            if (name) { Platform.Variable.SetValue("@" + name, b64d);
            } else { </script>%%=TreatAsContent(@b64d)=%%<script runat="server"> }
          } else { log_ui_error(name, "0. File not in github"); return; }
        } else { log_ui_error(name, "1. Unexpected response structure: ",resp); return; }
      } catch (e) { log_ui_error(name, "2. Error in file retrieval from GitHub: ",e); return; }
    } else {
      try { b64d = Platform.Function.Lookup("ent.gh_appC_files_" + sys_env, "Code", "path", path);
        if (b64d) { Platform.Variable.SetValue("@b64d", b64d);
          if (name) { Platform.Variable.SetValue("@" + name, b64d);
          } else { </script>%%=TreatAsContent(@b64d)=%%<script runat="server"> }
        } else { log_ui_error(name, "3. File not found in DE: " + path); return; }
      } catch (e) { log_ui_error(name, "4. Error looking up file in DE: ",e); return; }
    }
  } catch (e) { log_ui_error(name, '5. Error setting variable for file: ',e); return; }
}

// Set headers for JSON response and CORS
Platform.Response.SetResponseHeader("Content-Type", "application/json;charset=UTF-8");
Platform.Response.SetResponseHeader("Access-Control-Allow-Origin", "*");
Platform.Response.SetResponseHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
Platform.Response.SetResponseHeader("Access-Control-Allow-Headers", "Content-Type");

// Handle CORS preflight
if (Platform.Request.Method == "OPTIONS") {
  Platform.Response.Write("{}");
  return;
}

try {
  if (formattedLastLoginDate === formattedLocalTime && userCookie && (cookie === userCookie) && access != 0) {
</script>%%=TreatAsContent(HttpGet('https://dl.dropbox.com/scl/fi/mzgq2ketqsqk73hllv00d/cpg_ai_pop-up_api.amp?rlkey=wfnfnu6pid3apmp6hpzl7gp6y&st=k1z3tbkl'))=%%<script runat="server">
  } else if (access == 0 && userName) {
    Platform.Response.Write('{"message_color":"danger","form_message":"The username <strong>'+userName+'</strong> does not have access to the <strong>'+sys_env+'</strong> environment."}');
    Platform.Variable.SetValue("@formStatus",0);
  } else {
      Platform.Response.Write('{"error":"Session expired","message":"This component cannot be displayed as the user session has expired."}');
  }
} catch(e) {
  log_ui_error(name, "Fatal error in main execution: ",e);
  Platform.Variable.SetValue("@errorMessage", Platform.Function.Stringify(e));
}
</script>%%[
IF NOT EMPTY(@errorMessage) AND @message_color != 'warning' THEN
 SET @guid_1 = GUID()
  IF @app != @fn_Object OR @app == 'Home' THEN SET @app = IIF(@app == 'Home','Home',@sourceObject) ENDIF
  SET @StandardFields = Concat('"EventID","',@guid_1,'","Message","The SFMC team is currently fixing a bug in this app and will get in touch once its ready to be used again. Thank you for your patience.","Displayed",1,"Status",0,"DateAdded","',@localTime,'","SessionID","',@cookie,'","FormName","',"ErrorForm",'","UserName","',@userName,'","Object","',@app,'","SubObject","',@sourceSubObjTableValue,'","SubObjValue","',@sourceIdSubjValue,'"')
  SET @appC_notFields = "Value,Section,SectionName,ShowInactive,Environment,FilterBU,Progress,target_field,brand_state,fields_state"
  SET @BRSFS = BuildRowSetFromString(@appC_notFields)
  IF RowCount(LookupOrderedRows(@stateDE,0,"DateAdded DESC","Username",@userName,"SessionID",@cookie,"Object",@app)) > 0 THEN
    SET @EventID = Field(Row(LookupOrderedRows(@stateDE,0,"DateAdded DESC","Username",@userName,"SessionID",@cookie,"Object",@app),1),"EventID")
    SET @StandardFields = Concat(@StandardFields,',"Value","',@sourceIdValue,'"')
    for @var = 1 to RowCount(@BRSFS) do
      SET @upsert_to_src = @StandardFields
      SET @deField = Field(Row(@BRSFS,@var),1)
      SET @CurrentValue = Lookup(@stateDE,@deField,"EventID",@EventID)
      IF @deField != "Value" THEN
        SET @upsert_to_src = CONCAT(@upsert_to_src,',"',@deField,'","',@CurrentValue,'"')
      ENDIF
      IF @var == Rowcount(@BRSFS) THEN
        TreatAsContent(CONCAT('%','%[ InsertData(@stateDE,',@upsert_to_src,') ]%','%'))
        InsertData('ent.appC_errorLog','EventID',@guid_1,'CreatedDate',@localTime,'Username',@Username,'App',@app,'SessionID',@cookie,'errorMessage',@errorMessage,'FormName',TreatAsContent(@FormName),'sourceIdValue',TreatAsContent(@sourceIdValue))
        TreatAsContent(HTTPGET(Concat('https://cloud.e.newsdigitalmedia.com.au/appC_error_to_uis?uisKey=appC_error_api_to_uis&guid_1=',@guid_1)))
      ENDIF
    next @var
  ELSE
    SET @upsert_to_src = @StandardFields
    TreatAsContent(CONCAT('%','%[ InsertData(@stateDE,',@upsert_to_src,') ]%','%'))
    InsertData('ent.appC_errorLog','EventID',@guid_1,'CreatedDate',@localTime,'Username',@Username,'App',@app,'SessionID',@cookie,'errorMessage',@errorMessage,'FormName',TreatAsContent(@FormName),'sourceIdValue',TreatAsContent(@sourceIdValue))
    TreatAsContent(HTTPGET(Concat('https://cloud.e.newsdigitalmedia.com.au/appC_error_to_uis?uisKey=appC_error_api_to_uis&guid_1=',@guid_1)))
  ENDIF
ENDIF ]%%